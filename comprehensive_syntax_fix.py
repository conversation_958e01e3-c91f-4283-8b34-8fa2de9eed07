#!/usr/bin/env python3
"""
Comprehensive syntax fix for all Python files
"""

import ast
import glob
import re


def check_and_fix_file(filepath):
    """Check and fix syntax errors in a file"""
    print(f"Processing {filepath}...")

    try:
        with open(filepath, encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Fix common issues
        content = fix_malformed_imports(content)
        content = fix_extra_triple_quotes(content)
        content = fix_hanging_docstrings(content)
        content = fix_indentation_issues(content)

        # Test if fixed content is valid Python
        try:
            ast.parse(content, filename=filepath)
            syntax_ok = True
        except SyntaxError as e:
            print(f"  ⚠️  Still has syntax errors: {e}")
            syntax_ok = False

        # Only write if changed and valid
        if content != original_content and syntax_ok:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✓ Fixed {filepath}")
            return True
        elif content != original_content:
            print(f"  ✗ Could not fix {filepath} - syntax still invalid")
            return False
        else:
            print(f"  - No changes needed for {filepath}")
            return False

    except Exception as e:
        print(f"  ✗ Error processing {filepath}: {e}")
        return False

def fix_malformed_imports(content):
    """Fix malformed import blocks"""
    # Fix cases where imports are inside other import blocks
    content = re.sub(r'from [^(]*\(\s*(from [^)]*)\)', r'\1', content, flags=re.DOTALL)

    # Fix standalone import statements that got mixed in
    lines = content.split('\n')
    new_lines = []
    in_import_block = False

    for line in lines:
        # Detect import block start
        if re.match(r'^from .* import \($', line):
            in_import_block = True
            len(line) - len(line.lstrip())
            new_lines.append(line)
            continue

        # Detect import block end
        if in_import_block and line.strip() == ')':
            in_import_block = False
            new_lines.append(line)
            continue

        # Handle lines inside import block
        if in_import_block:
            # If this is a standalone import, move it outside
            if re.match(r'^\s*(from|import)\s+', line) and not line.strip().endswith(','):
                # This is a standalone import inside an import block
                # We'll add it later
                continue
            else:
                new_lines.append(line)
        else:
            new_lines.append(line)

    return '\n'.join(new_lines)

def fix_extra_triple_quotes(content):
    """Fix extra triple quotes after docstrings"""
    # Pattern to find docstring followed by extra triple quotes
    pattern = r'("""[^"]*?""")(\s*""")'
    return re.sub(pattern, r'\1', content, flags=re.DOTALL)

def fix_hanging_docstrings(content):
    """Fix hanging docstrings at start of files"""
    # Pattern to find docstring that doesn't close before imports
    pattern = r'("""[^"]*?)(^\s*(?:import|from)\s+)'

    def replace_func(match):
        return match.group(1) + '"""\n\n' + match.group(2)

    return re.sub(pattern, replace_func, content, flags=re.MULTILINE)

def fix_indentation_issues(content):
    """Fix obvious indentation issues"""
    lines = content.split('\n')
    new_lines = []

    for i, line in enumerate(lines):
        # Fix imports that are indented for no reason
        if re.match(r'^\s+(import|from)\s+', line) and i > 0:
            prev_line = lines[i-1].strip()
            # If previous line doesn't suggest this should be indented
            if not (prev_line.endswith(':') or prev_line.endswith('\\') or prev_line.endswith('(')):
                # Remove indentation
                line = line.lstrip()

        new_lines.append(line)

    return '\n'.join(new_lines)

def main():
    """Fix all Python files"""
    view_files = glob.glob('CLEAR/views/*.py')
    service_files = glob.glob('CLEAR/services/*.py')
    model_files = glob.glob('CLEAR/models/*.py')

    all_files = view_files + service_files + model_files

    fixed_count = 0
    for filepath in all_files:
        if check_and_fix_file(filepath):
            fixed_count += 1

    print(f"\n✓ Fixed {fixed_count} files out of {len(all_files)} total files")

if __name__ == "__main__":
    main()
