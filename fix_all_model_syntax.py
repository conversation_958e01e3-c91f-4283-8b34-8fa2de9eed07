#!/usr/bin/env python3
"""Fix all syntax errors in CLEAR models directory."""

import os
import ast
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

MODELS_DIR = '/workspaces/clear_htmx/CLEAR/models'

def fix_file_syntax(filepath):
    """Fix common syntax errors in a file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original = content
        
        # Remove trailing unclosed docstrings
        lines = content.split('\n')
        if lines and lines[-1].strip() == '"""':
            lines = lines[:-1]
            content = '\n'.join(lines)
            logger.info(f"Removed trailing docstring from {filepath}")
        
        # Check for unclosed docstrings
        triple_double_count = content.count('"""')
        content.count("'''")
        
        if triple_double_count % 2 == 1:
            # Find last occurrence and check context
            last_pos = content.rfind('"""')
            if last_pos > 0:
                # Check if it's at the start of a line (likely a docstring start)
                line_start = content.rfind('\n', 0, last_pos) + 1
                if content[line_start:last_pos].strip() == '':
                    # This is likely an unclosed docstring
                    # Check if there's actual content after it
                    after_docstring = content[last_pos + 3:].strip()
                    if after_docstring and not after_docstring.startswith('"""'):
                        # Add closing docstring
                        content = content[:last_pos + 3] + '\n"""' + content[last_pos + 3:]
                        logger.info(f"Added closing docstring in {filepath}")
        
        # Fix common indentation issues
        lines = content.split('\n')
        fixed_lines = []
        for i, line in enumerate(lines):
            # Fix lines that start with excessive indentation after imports
            if i > 0 and lines[i-1].strip().startswith('from') and line.strip().startswith('from'):
                # Check if current line has wrong indentation
                if len(line) - len(line.lstrip()) > 0:
                    line = line.lstrip()
                    logger.info(f"Fixed indentation at line {i+1} in {filepath}")
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # Ensure file ends with newline
        if content and not content.endswith('\n'):
            content += '\n'
        
        # Only write if changed
        if content != original:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error processing {filepath}: {e}")
        return False

def check_syntax(filepath):
    """Check if file has valid syntax."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True
    except SyntaxError as e:
        logger.error(f"Syntax error in {filepath}: {e}")
        return False
    except Exception as e:
        logger.error(f"Error checking {filepath}: {e}")
        return False

def main():
    """Fix all model files."""
    logger.info("Starting model syntax fixes...")
    
    fixed_count = 0
    error_count = 0
    
    # Get all Python files in models directory
    for filename in os.listdir(MODELS_DIR):
        if filename.endswith('.py'):
            filepath = os.path.join(MODELS_DIR, filename)
            
            # First check if it has syntax errors
            if not check_syntax(filepath):
                # Try to fix it
                if fix_file_syntax(filepath):
                    fixed_count += 1
                    
                    # Check again
                    if not check_syntax(filepath):
                        error_count += 1
                        logger.error(f"Still has syntax errors after fix: {filename}")
                else:
                    error_count += 1
    
    logger.info(f"\nFixed {fixed_count} files")
    logger.info(f"Files with remaining errors: {error_count}")
    
    return error_count == 0

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("\nAll model syntax errors fixed!")
    else:
        logger.warning("\nSome syntax errors remain.")