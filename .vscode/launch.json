{"version": "0.2.0", "configurations": [{"name": "Django: Debug Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "0.0.0.0:8000"], "django": true, "autoReload": {"enable": true}, "env": {"DJANGO_SETTINGS_MODULE": "clear_htmx.settings", "PYTHONPATH": "${workspaceFolder}", "DEBUG": "True"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/workspaces/clear_htmx/.conda/bin/python"}, {"name": "Django: Run Tests", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["test", "--verbosity=2"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "clear_htmx.settings", "PYTHONPATH": "${workspaceFolder}"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/workspaces/clear_htmx/.conda/bin/python"}, {"name": "Django: Shell", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["shell"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "clear_htmx.settings", "PYTHONPATH": "${workspaceFolder}"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/workspaces/clear_htmx/.conda/bin/python"}, {"name": "Django: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["migrate"], "django": true, "env": {"DJANGO_SETTINGS_MODULE": "clear_htmx.settings", "PYTHONPATH": "${workspaceFolder}"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "/workspaces/clear_htmx/.conda/bin/python"}]}