{"name": "CLEAR Spatial Library Configuration", "description": "Configuration for GIS libraries used in the CLEAR project", "libraries": {"gdal": {"version": "3.6.2", "bindings": "python-gdal", "features": ["raster_io", "vector_io", "coordinate_transformation", "spatial_reference_systems"]}, "geos": {"version": "3.10.6", "bindings": "python-geos", "features": ["geometry_operations", "spatial_predicates", "topology_operations", "buffer_operations"]}, "proj": {"description": "Coordinate transformation library", "integration": "via_gdal_geos"}, "spatialite": {"description": "SQLite spatial extension", "status": "available_for_development"}, "postgis": {"description": "PostgreSQL spatial extension", "status": "production_ready", "version": "3.x"}}, "django_integration": {"geodjango": {"enabled": true, "backend": "django.contrib.gis.db.backends.postgis", "geometry_fields": ["PointField", "LineStringField", "PolygonField", "MultiPointField", "MultiLineStringField", "MultiPolygonField", "GeometryCollectionField"], "spatial_lookups": ["contains", "contained", "covers", "covered_by", "crosses", "disjoint", "equals", "intersects", "overlaps", "touches", "within", "dwithin", "distance_gt", "distance_gte", "distance_lt", "distance_lte"], "spatial_functions": ["Area", "AsGeoJSON", "AsGML", "AsKML", "AsSVG", "BoundingCircle", "Centroid", "Difference", "Distance", "Envelope", "ForcePolygonCW", "GeoHash", "Intersection", "Length", "<PERSON><PERSON><PERSON><PERSON>", "NumGeometries", "NumPoints", "Perimeter", "PointOnSurface", "Reverse", "Scale", "SnapToGrid", "SymDifference", "Transform", "Translate", "Union"]}}, "type_checking": {"geometry_types": {"Point": "django.contrib.gis.geos.Point", "LineString": "django.contrib.gis.geos.LineString", "Polygon": "django.contrib.gis.geos.Polygon", "MultiPoint": "django.contrib.gis.geos.MultiPoint", "MultiLineString": "django.contrib.gis.geos.MultiLineString", "MultiPolygon": "django.contrib.gis.geos.MultiPolygon", "GeometryCollection": "django.contrib.gis.geos.GeometryCollection"}, "coordinate_systems": {"WGS84": 4326, "Web_Mercator": 3857, "UTM_Zone_16N": 32616, "State_Plane_Indiana_East": 2965}}, "performance_optimization": {"spatial_indexes": {"enabled": true, "type": "GiST", "description": "PostgreSQL GiST indexes for spatial queries"}, "geometry_simplification": {"enabled": true, "tolerance": 0.0001, "preserve_topology": true}, "query_optimization": {"use_spatial_indexes": true, "bbox_filtering": true, "geometry_caching": true}}}