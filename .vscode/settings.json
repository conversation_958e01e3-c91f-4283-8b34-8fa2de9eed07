{
    // Development Container Settings
    "dev.containers.defaultExtensions": [
        "ms-vscode-remote.remote-containers"
    ],
    "dev.containers.forwardPortsToHost": true,
    "dev.containers.installDockerInWSL": false,
    "remote.WSL.defaultDistribution": "Ubuntu-24.04",
    "terminal.integrated.defaultProfile.windows": "Ubuntu-24.04 (WSL)",
    "terminal.integrated.profiles.windows": {
        "Ubuntu-24.04 (WSL)": {
            "path": "C:\\Windows\\system32\\wsl.exe",
            "args": [
                "-d",
                "Ubuntu-24.04"
            ]
        }
    },

    // Python Interpreter Configuration
    "python.defaultInterpreterPath": "/workspaces/clear_htmx/.conda/bin/python",
    "python.pythonPath": "/workspaces/clear_htmx/.conda/bin/python",

    // Python Testing Configuration
    "python.testing.pytestArgs": [
        "tests"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,

    // Pylance Language Server Configuration
    "python.languageServer": "Pylance",
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.autoSearchPaths": true,
    "python.analysis.diagnosticMode": "workspace",
    "python.analysis.indexing": true,

    // Django Type Checking Enhancements
    "python.analysis.typeshedPaths": [
        "./typings"
    ],

    // Django-specific Pylance Configuration
    "python.analysis.extraPaths": [
        "./",
        "./CLEAR",
        "./clear_htmx",
        "./templates",
        "./static"
    ],

    // Enable Django plugin for better type checking
    "python.analysis.plugins": [
        "django-stubs"
    ],

    // Import Resolution Configuration
    "python.analysis.include": [
        "CLEAR/**",
        "clear_htmx/**",
        "tests/**"
    ],

    // File Exclusions for Better Performance
    "python.analysis.exclude": [
        "**/node_modules",
        "**/__pycache__",
        "**/.*",
        "**/migrations/**",
        "**/staticfiles/**",
        "**/media/**",
        "**/logs/**",
        "**/coty-playground/**",
        "**/source-repo/**",
        "**/refactor_migration/**",
        "**/scripts/__pycache__/**",
        "**/*.pyc",
        "**/*.pyo",
        "**/fix_*.py",
        "**/check_*.py",
        "**/comprehensive_*.py",
        "**/configure_*.py",
        "**/run_all_*.py"
    ],

    // Stub Path Configuration for Better Type Hints
    "python.analysis.stubPath": "./typings",

    // Django Environment Variables
    "python.envFile": "${workspaceFolder}/.env",

    // Auto-completion and IntelliSense Settings
    "python.analysis.completeFunctionParens": true,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.variableTypes": true,

    // Memory and Performance Optimization
    "python.analysis.memory.keepLibraryAst": true,
    "python.analysis.nodeExecutable": "",

    // Django-specific Settings
    "python.analysis.packageIndexDepths": [
        {
            "name": "django",
            "depth": 3
        },
        {
            "name": "django_htmx",
            "depth": 2
        },
        {
            "name": "django.contrib.gis",
            "depth": 4
        },
        {
            "name": "osgeo",
            "depth": 3
        },
        {
            "name": "gdal",
            "depth": 2
        },
        {
            "name": "geos",
            "depth": 2
        },
        {
            "name": "fiona",
            "depth": 2
        }
    ],

    // GIS and Spatial Library Configuration
    "python.analysis.libraryCodeForTypes": true,
    "python.analysis.useImportHeuristic": true,

    // Advanced Type Checking Configuration
    "python.analysis.logLevel": "Information",
    "python.analysis.symbolsHierarchyDepthLimit": 10,
    "python.analysis.userFileIndexingLimit": 2000,

    // Django Model and ORM Recognition
    "python.analysis.importFormat": "absolute",
    "python.analysis.fixAll": [
        "source.unusedImports",
        "source.convertImportFormat"
    ],

    // Code Completion and Suggestions
    "python.analysis.completeFunctionParens": true,
    "python.analysis.addImport.exactMatchOnly": false,
    "python.analysis.gotoDefinitionInStringLiteral": true,

    // Django Template and Static File Recognition
    "files.associations": {
        "*.html": "html",
        "*.htm": "html",
        "*.django-html": "html",
        "*.jinja": "html",
        "*.jinja2": "html"
    },

    // HTMX and JavaScript Integration
    "emmet.includeLanguages": {
        "django-html": "html",
        "jinja-html": "html"
    },

    // Editor Configuration for Django Files
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    },

    // Django-specific File Patterns
    "python.analysis.diagnosticSeverityOverrides": {
        "reportMissingImports": "information",
        "reportMissingTypeStubs": "none",
        "reportImportCycles": "warning",
        "reportUnusedImport": "information",
        "reportUnusedClass": "information",
        "reportUnusedFunction": "information",
        "reportUnusedVariable": "information",
        "reportDuplicateImport": "warning",
        "reportOptionalSubscript": "information",
        "reportOptionalMemberAccess": "information",
        "reportOptionalCall": "information",
        "reportOptionalIterable": "information",
        "reportOptionalContextManager": "information",
        "reportOptionalOperand": "information",
        "reportGeneralTypeIssues": "information",
        "reportUntypedFunctionDecorator": "none",
        "reportUntypedClassDecorator": "none",
        "reportUntypedBaseClass": "information",
        "reportUntypedNamedTuple": "information",
        "reportPrivateUsage": "information",
        "reportConstantRedefinition": "information",
        "reportIncompatibleMethodOverride": "warning",
        "reportIncompatibleVariableOverride": "warning",
        "reportInconsistentConstructor": "information",
        "reportOverlappingOverloads": "warning",
        "reportMissingSuperCall": "information",
        "reportUninitializedInstanceVariable": "information",
        "reportInvalidStringEscapeSequence": "warning",
        "reportUnknownParameterType": "none",
        "reportUnknownArgumentType": "none",
        "reportUnknownLambdaType": "information",
        "reportUnknownVariableType": "none",
        "reportUnknownMemberType": "none",
        "reportMissingParameterType": "none",
        "reportMissingTypeArgument": "information",
        "reportInvalidTypeVarUse": "warning",
        "reportCallInDefaultInitializer": "information",
        "reportUnnecessaryIsInstance": "information",
        "reportUnnecessaryCast": "information",
        "reportUnnecessaryComparison": "information",
        "reportUnnecessaryContains": "information",
        "reportAssertAlwaysTrue": "warning",
        "reportSelfClsParameterName": "warning",
        "reportImplicitStringConcatenation": "information",
        "reportAttributeAccessIssue": "information",
        "reportUnusedCallResult": "none"
    }
}