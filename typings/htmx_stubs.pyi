# HTMX and django-htmx type stubs for CLEAR project
# This file helps <PERSON><PERSON><PERSON> understand HTMX patterns and django-htmx integration

from typing import Any, Dict, Optional, Union
from django.http import HttpRequest, HttpResponse

# django-htmx middleware and request extensions
class HtmxDetails:
    boosted: bool
    current_url: Optional[str]
    history_restore_request: bool
    prompt: Optional[str]
    request: bool
    target: Optional[str]
    trigger: Optional[str]
    trigger_name: Optional[str]
    triggering_element: Optional[str]

# Extended HttpRequest with HTMX support
class HtmxHttpRequest(HttpRequest):
    htmx: HtmxDetails

# HTMX Response Classes
class HttpResponseClientRedirect(HttpResponse):
    def __init__(self, redirect_to: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseClientRefresh(HttpResponse):
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseLocation(HttpResponse):
    def __init__(self, redirect_to: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponsePushUrl(HttpResponse):
    def __init__(self, push_url: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseReplaceUrl(HttpResponse):
    def __init__(self, replace_url: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseReswap(HttpResponse):
    def __init__(self, reswap: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseRetarget(HttpResponse):
    def __init__(self, retarget: str, *args: Any, **kwargs: Any) -> None: ...

class HttpResponseStopPolling(HttpResponse):
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...

# HTMX Template Tags and Utilities
def htmx_partial(template_name: str, context: Optional[Dict[str, Any]] = None) -> str: ...

# HTMX Decorators
def require_htmx(view_func: Any) -> Any: ...

# HTMX Middleware
class HtmxMiddleware:
    def __init__(self, get_response: Any) -> None: ...
    def __call__(self, request: HttpRequest) -> HttpResponse: ...
    def process_request(self, request: HttpRequest) -> None: ...

# Common HTMX Response Headers
HTMX_HEADERS = {
    'HX-Location': str,
    'HX-Push-Url': str,
    'HX-Redirect': str,
    'HX-Refresh': str,
    'HX-Replace-Url': str,
    'HX-Reswap': str,
    'HX-Retarget': str,
    'HX-Reselect': str,
    'HX-Trigger': str,
    'HX-Trigger-After-Settle': str,
    'HX-Trigger-After-Swap': str,
}

# HTMX Request Headers
HTMX_REQUEST_HEADERS = {
    'HX-Boosted': str,
    'HX-Current-URL': str,
    'HX-History-Restore-Request': str,
    'HX-Prompt': str,
    'HX-Request': str,
    'HX-Target': str,
    'HX-Trigger-Name': str,
    'HX-Trigger': str,
}
