# Django type hints for CLEAR project
from typing import Any, Dict, Optional, Union
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.contrib.auth.models import AbstractUser
from django.db.models import QuerySet, Model

# Common Django request type
DjangoRequest = HttpRequest

# Common Django response types
DjangoResponse = Union[HttpResponse, HttpResponseRedirect]

# Django model instance type
DjangoModel = Model

# Django QuerySet type
DjangoQuerySet = QuerySet[Any]

# Django admin method signatures
def admin_method(self: Any, request: HttpRequest, queryset: QuerySet[Any]) -> None: ...

# Django view function signature
def django_view(request: HttpRequest, *args: Any, **kwargs: Any) -> DjangoResponse: ...

# Django class-based view method signature
def cbv_method(self: Any, request: HttpRequest, *args: Any, **kwargs: Any) -> DjangoResponse: ...
