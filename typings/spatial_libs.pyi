# Comprehensive spatial library type stubs for CLEAR project
# Covers GDAL, GEOS, PostGIS, and GeoDjango integration

from typing import Any, Dict, List, Optional, Tuple, Union, Iterator
from django.db import models
from django.contrib.gis.db import models as gis_models

# GDAL Library Types
class GDALException(Exception): ...

class Driver:
    name: str
    def __init__(self, dr_input: Union[str, int]) -> None: ...

class DataSource:
    name: str
    layer_count: int
    
    def __init__(self, ds_input: Union[str, bytes], write: bool = False) -> None: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator['Layer']: ...
    def __getitem__(self, index: Union[int, str]) -> 'Layer': ...

class Layer:
    name: str
    num_feat: int
    num_fields: int
    geom_type: str
    srs: 'SpatialReference'
    extent: Tuple[float, float, float, float]
    
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator['Feature']: ...
    def __getitem__(self, index: int) -> 'Feature': ...
    def get_fields(self) -> List[str]: ...
    def get_geoms(self, geos: bool = False) -> List[Any]: ...

class Feature:
    fid: int
    layer_name: str
    num_fields: int
    fields: List[str]
    geom: Any
    geom_type: str
    
    def __getitem__(self, field: Union[str, int]) -> Any: ...
    def get(self, field: Union[str, int]) -> Any: ...

class OGRGeometry:
    dimension: int
    coord_dim: int
    geom_count: int
    point_count: int
    num_points: int
    num_coords: int
    geom_type: str
    geom_name: str
    area: float
    extent: Tuple[float, float, float, float]
    srs: Optional['SpatialReference']
    srid: Optional[int]
    geos: Any
    gml: str
    hex: str
    json: str
    kml: str
    wkb_size: int
    wkb: bytes
    wkt: str
    ewkt: str
    
    def __init__(self, geom_input: Any, srs: Optional['SpatialReference'] = None) -> None: ...
    def clone(self) -> 'OGRGeometry': ...
    def close_rings(self) -> None: ...
    def transform(self, coord_trans: Any, clone: bool = False) -> Optional['OGRGeometry']: ...
    def transform_to(self, srs: 'SpatialReference') -> None: ...

class SpatialReference:
    name: str
    srid: Optional[int]
    linear_name: str
    linear_units: float
    angular_name: str
    angular_units: float
    units: Tuple[str, float]
    ellipsoid: Tuple[str, float, float]
    semi_major: float
    semi_minor: float
    inverse_flattening: float
    geographic: bool
    local: bool
    projected: bool
    
    def __init__(self, srs_input: Union[str, int, bytes] = None) -> None: ...
    def __str__(self) -> str: ...
    def attr_value(self, target: str, index: int = 0) -> Optional[str]: ...
    def auth_name(self, target: str) -> Optional[str]: ...
    def auth_code(self, target: str) -> Optional[str]: ...
    def clone(self) -> 'SpatialReference': ...
    def identify_epsg(self) -> None: ...
    def morphFromESRI(self) -> None: ...
    def validate(self) -> None: ...

# GEOS Library Types  
class GEOSException(Exception): ...

class PreparedGeometry:
    def __init__(self, geom: 'GEOSGeometry') -> None: ...
    def contains(self, other: 'GEOSGeometry') -> bool: ...
    def contains_properly(self, other: 'GEOSGeometry') -> bool: ...
    def covers(self, other: 'GEOSGeometry') -> bool: ...
    def intersects(self, other: 'GEOSGeometry') -> bool: ...

class GEOSGeometry:
    ptr: Any
    srid: int
    _srid: int
    
    # Geometry properties
    geom_type: str
    geom_typeid: int
    num_geom: int
    num_coords: int
    num_points: int
    dims: int
    coord_seq: Any
    shell: Any
    tuple: Tuple[Any, ...]
    coords: Any
    x: float
    y: float
    z: Optional[float]
    
    # Spatial properties
    area: float
    length: float
    empty: bool
    ring: bool
    simple: bool
    valid: bool
    valid_reason: str
    
    # Output formats
    ewkt: str
    hex: str
    hexewkb: str
    json: str
    geojson: str
    kml: str
    ogr: OGRGeometry
    wkb: bytes
    wkt: str
    extent: Tuple[float, float, float, float]
    
    def __init__(self, geo_input: Any, srid: Optional[int] = None) -> None: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __eq__(self, other: Any) -> bool: ...
    def __hash__(self) -> int: ...
    
    # Geometry operations
    def buffer(self, width: float, quadsegs: int = 8) -> 'GEOSGeometry': ...
    def centroid(self) -> 'Point': ...
    def convex_hull(self) -> 'GEOSGeometry': ...
    def difference(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def envelope(self) -> 'Polygon': ...
    def intersection(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def point_on_surface(self) -> 'Point': ...
    def relate_pattern(self, other: 'GEOSGeometry', pattern: str) -> bool: ...
    def simplify(self, tolerance: float = 0.0, preserve_topology: bool = False) -> 'GEOSGeometry': ...
    def sym_difference(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def union(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    
    # Spatial predicates
    def contains(self, other: 'GEOSGeometry') -> bool: ...
    def covers(self, other: 'GEOSGeometry') -> bool: ...
    def crosses(self, other: 'GEOSGeometry') -> bool: ...
    def disjoint(self, other: 'GEOSGeometry') -> bool: ...
    def equals(self, other: 'GEOSGeometry') -> bool: ...
    def equals_exact(self, other: 'GEOSGeometry', tolerance: float = 0) -> bool: ...
    def intersects(self, other: 'GEOSGeometry') -> bool: ...
    def overlaps(self, other: 'GEOSGeometry') -> bool: ...
    def relate(self, other: 'GEOSGeometry') -> str: ...
    def touches(self, other: 'GEOSGeometry') -> bool: ...
    def within(self, other: 'GEOSGeometry') -> bool: ...
    
    # Distance operations
    def distance(self, other: 'GEOSGeometry') -> float: ...
    def hausdorff_distance(self, other: 'GEOSGeometry') -> float: ...
    
    # Coordinate transformation
    def transform(self, srid: int, clone: bool = False) -> Optional['GEOSGeometry']: ...
    def clone(self) -> 'GEOSGeometry': ...
    def normalize(self) -> 'GEOSGeometry': ...

# Specific geometry types
class Point(GEOSGeometry):
    def __init__(self, x: float, y: float, z: Optional[float] = None, srid: Optional[int] = None) -> None: ...

class LineString(GEOSGeometry):
    def __init__(self, coords: Any, srid: Optional[int] = None) -> None: ...
    def interpolate(self, distance: float) -> Point: ...
    def interpolate_normalized(self, distance: float) -> Point: ...

class LinearRing(LineString):
    def __init__(self, coords: Any, srid: Optional[int] = None) -> None: ...

class Polygon(GEOSGeometry):
    def __init__(self, exterior_ring: Any, holes: Optional[List[Any]] = None, srid: Optional[int] = None) -> None: ...
    @property
    def exterior_ring(self) -> LinearRing: ...

class MultiPoint(GEOSGeometry):
    def __init__(self, coords: Any, srid: Optional[int] = None) -> None: ...

class MultiLineString(GEOSGeometry):
    def __init__(self, coords: Any, srid: Optional[int] = None) -> None: ...

class MultiPolygon(GEOSGeometry):
    def __init__(self, coords: Any, srid: Optional[int] = None) -> None: ...

class GeometryCollection(GEOSGeometry):
    def __init__(self, geoms: List[GEOSGeometry], srid: Optional[int] = None) -> None: ...

# GeoDjango Model Fields with enhanced typing
class BaseSpatialField(models.Field):
    geom_type: str
    geom_class: type
    form_class: type
    geography: bool
    
    def __init__(self, verbose_name: Optional[str] = None, srid: int = 4326, 
                 spatial_index: bool = True, **kwargs: Any) -> None: ...

class GeometryField(BaseSpatialField): ...
class PointField(BaseSpatialField): ...
class LineStringField(BaseSpatialField): ...
class PolygonField(BaseSpatialField): ...
class MultiPointField(BaseSpatialField): ...
class MultiLineStringField(BaseSpatialField): ...
class MultiPolygonField(BaseSpatialField): ...
class GeometryCollectionField(BaseSpatialField): ...

# GeoDjango Manager
class GeoManager(models.Manager):
    def area(self, **kwargs: Any) -> Any: ...
    def centroid(self, **kwargs: Any) -> Any: ...
    def difference(self, **kwargs: Any) -> Any: ...
    def distance(self, geom: GEOSGeometry, **kwargs: Any) -> Any: ...
    def envelope(self, **kwargs: Any) -> Any: ...
    def extent(self, **kwargs: Any) -> Any: ...
    def extent3d(self, **kwargs: Any) -> Any: ...
    def force_rhr(self, **kwargs: Any) -> Any: ...
    def geohash(self, precision: int = 5, **kwargs: Any) -> Any: ...
    def geojson(self, **kwargs: Any) -> Any: ...
    def gml(self, **kwargs: Any) -> Any: ...
    def intersection(self, geom: GEOSGeometry, **kwargs: Any) -> Any: ...
    def kml(self, **kwargs: Any) -> Any: ...
    def length(self, **kwargs: Any) -> Any: ...
    def make_valid(self, **kwargs: Any) -> Any: ...
    def num_geom(self, **kwargs: Any) -> Any: ...
    def num_points(self, **kwargs: Any) -> Any: ...
    def perimeter(self, **kwargs: Any) -> Any: ...
    def point_on_surface(self, **kwargs: Any) -> Any: ...
    def reverse(self, **kwargs: Any) -> Any: ...
    def scale(self, x: float, y: float, z: Optional[float] = None, **kwargs: Any) -> Any: ...
    def snap_to_grid(self, *args: Any, **kwargs: Any) -> Any: ...
    def svg(self, **kwargs: Any) -> Any: ...
    def sym_difference(self, geom: GEOSGeometry, **kwargs: Any) -> Any: ...
    def transform(self, srid: int, **kwargs: Any) -> Any: ...
    def translate(self, x: float, y: float, z: Optional[float] = None, **kwargs: Any) -> Any: ...
    def union(self, geom: GEOSGeometry, **kwargs: Any) -> Any: ...
    def within_bbox(self, bbox: Tuple[float, float, float, float], **kwargs: Any) -> Any: ...
