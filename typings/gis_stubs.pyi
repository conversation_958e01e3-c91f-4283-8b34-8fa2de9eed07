# GIS and Spatial library type stubs for CLEAR project
# This file helps <PERSON><PERSON><PERSON> understand GeoDjango, GDAL, GEOS, and PostGIS patterns

from typing import Any, Dict, List, Optional, Tuple, Union
from django.db import models

# GEOS Geometry Types
class GEOSGeometry:
    srid: int
    coords: Any
    geom_type: str
    
    def __init__(self, geo_input: Any, srid: Optional[int] = None) -> None: ...
    def transform(self, srid: int) -> None: ...
    def buffer(self, width: float) -> 'GEOSGeometry': ...
    def intersection(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def union(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def difference(self, other: 'GEOSGeometry') -> 'GEOSGeometry': ...
    def distance(self, other: 'GEOSGeometry') -> float: ...
    def area(self) -> float: ...
    def length(self) -> float: ...
    def centroid(self) -> 'Point': ...
    def envelope(self) -> 'Polygon': ...
    def convex_hull(self) -> 'GEOSGeometry': ...

class Point(GEOSGeometry):
    x: float
    y: float
    z: Optional[float]
    
    def __init__(self, x: float, y: float, z: Optional[float] = None, srid: Optional[int] = None) -> None: ...

class LineString(GEOSGeometry):
    def __init__(self, coords: List[Tuple[float, float]], srid: Optional[int] = None) -> None: ...

class Polygon(GEOSGeometry):
    def __init__(self, exterior_ring: Any, holes: Optional[List[Any]] = None, srid: Optional[int] = None) -> None: ...

class MultiPoint(GEOSGeometry):
    def __init__(self, points: List[Point], srid: Optional[int] = None) -> None: ...

class MultiLineString(GEOSGeometry):
    def __init__(self, linestrings: List[LineString], srid: Optional[int] = None) -> None: ...

class MultiPolygon(GEOSGeometry):
    def __init__(self, polygons: List[Polygon], srid: Optional[int] = None) -> None: ...

# GeoDjango Model Fields
class GeometryField(models.Field):
    srid: int
    spatial_index: bool
    geography: bool
    
    def __init__(self, srid: int = 4326, spatial_index: bool = True, **kwargs: Any) -> None: ...

class PointField(GeometryField): ...
class LineStringField(GeometryField): ...
class PolygonField(GeometryField): ...
class MultiPointField(GeometryField): ...
class MultiLineStringField(GeometryField): ...
class MultiPolygonField(GeometryField): ...
class GeometryCollectionField(GeometryField): ...

# Spatial Database Functions
class Distance:
    def __init__(self, expr1: Any, expr2: Any) -> None: ...

class Area:
    def __init__(self, expression: Any) -> None: ...

class Length:
    def __init__(self, expression: Any) -> None: ...

class Centroid:
    def __init__(self, expression: Any) -> None: ...

class Envelope:
    def __init__(self, expression: Any) -> None: ...

class Intersection:
    def __init__(self, expr1: Any, expr2: Any) -> None: ...

class Union:
    def __init__(self, expression: Any) -> None: ...

class Buffer:
    def __init__(self, expression: Any, distance: float) -> None: ...

class Transform:
    def __init__(self, expression: Any, srid: int) -> None: ...

# Spatial Lookups
class SpatialLookup:
    contains: str
    contained: str
    covers: str
    covered_by: str
    crosses: str
    disjoint: str
    equals: str
    exact: str
    intersects: str
    overlaps: str
    relate: str
    touches: str
    within: str
    left: str
    right: str
    overlaps_left: str
    overlaps_right: str
    overlaps_above: str
    overlaps_below: str
    strictly_above: str
    strictly_below: str
    same_as: str
    bbcontains: str
    bboverlaps: str
    dwithin: str
    distance_gt: str
    distance_gte: str
    distance_lt: str
    distance_lte: str

# GDAL Data Source Types
class DataSource:
    def __init__(self, ds_input: Union[str, bytes]) -> None: ...
    def __len__(self) -> int: ...
    def __getitem__(self, index: int) -> 'Layer': ...

class Layer:
    name: str
    num_feat: int
    geom_type: str
    srs: 'SpatialReference'
    
    def __len__(self) -> int: ...
    def __iter__(self) -> Any: ...
    def __getitem__(self, index: int) -> 'Feature': ...

class Feature:
    fid: int
    geom: GEOSGeometry
    
    def __getitem__(self, field: str) -> Any: ...
    def get(self, field: str) -> Any: ...

class SpatialReference:
    srid: int
    proj4: str
    wkt: str
    
    def __init__(self, srs_input: Union[str, int]) -> None: ...

# PostGIS-specific functions
def ST_Distance(geom1: Any, geom2: Any) -> float: ...
def ST_DWithin(geom1: Any, geom2: Any, distance: float) -> bool: ...
def ST_Intersects(geom1: Any, geom2: Any) -> bool: ...
def ST_Contains(geom1: Any, geom2: Any) -> bool: ...
def ST_Within(geom1: Any, geom2: Any) -> bool: ...
def ST_Buffer(geom: Any, distance: float) -> Any: ...
def ST_Area(geom: Any) -> float: ...
def ST_Length(geom: Any) -> float: ...
def ST_Centroid(geom: Any) -> Point: ...
def ST_Transform(geom: Any, srid: int) -> Any: ...
