# Django-specific type stubs for CLEAR project
# This file helps <PERSON><PERSON><PERSON> understand Django patterns and provides better IntelliSense

from typing import Any, Dict, List, Optional, Union, Type, TypeVar, Generic
from django.db import models
from django.http import HttpRequest, HttpResponse
from django.contrib.auth.models import AbstractUser

# Django Model Field Types
class ModelField(Generic[Any]):
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...

class CharField(ModelField[str]): ...
class TextField(ModelField[str]): ...
class IntegerField(ModelField[int]): ...
class FloatField(ModelField[float]): ...
class BooleanField(ModelField[bool]): ...
class DateTimeField(ModelField[Any]): ...
class DateField(ModelField[Any]): ...
class TimeField(ModelField[Any]): ...
class EmailField(CharField): ...
class URLField(CharField): ...
class SlugField(CharField): ...
class UUIDField(ModelField[Any]): ...
class JSONField(ModelField[Dict[str, Any]]): ...

# GeoDjango Field Types
class GeometryField(ModelField[Any]): ...
class PointField(GeometryField): ...
class LineStringField(GeometryField): ...
class PolygonField(GeometryField): ...
class MultiPointField(GeometryField): ...
class MultiLineStringField(GeometryField): ...
class MultiPolygonField(GeometryField): ...

# Foreign Key and Relationship Fields
class ForeignKey(ModelField[Any]):
    def __init__(self, to: Union[str, Type[models.Model]], on_delete: Any, **kwargs: Any) -> None: ...

class OneToOneField(ForeignKey): ...
class ManyToManyField(ModelField[Any]): ...

# Django Model Manager
class Manager(Generic[Any]):
    def all(self) -> Any: ...
    def filter(self, **kwargs: Any) -> Any: ...
    def get(self, **kwargs: Any) -> Any: ...
    def create(self, **kwargs: Any) -> Any: ...
    def update(self, **kwargs: Any) -> int: ...
    def delete(self) -> tuple[int, Dict[str, int]]: ...

# Django Model Base
class Model:
    objects: Manager[Any]
    pk: Any
    id: Any
    
    def save(self, *args: Any, **kwargs: Any) -> None: ...
    def delete(self) -> tuple[int, Dict[str, int]]: ...
    def clean(self) -> None: ...
    def full_clean(self) -> None: ...

# Django View Types
class View:
    request: HttpRequest
    args: tuple[Any, ...]
    kwargs: Dict[str, Any]
    
    def dispatch(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse: ...
    def get(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse: ...
    def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse: ...

# Django Form Types
class Form:
    def is_valid(self) -> bool: ...
    def clean(self) -> Dict[str, Any]: ...
    def save(self, commit: bool = True) -> Any: ...

class ModelForm(Form):
    instance: Optional[Model]
    
    class Meta:
        model: Type[Model]
        fields: Union[List[str], str]
        exclude: Optional[List[str]]

# Django Admin Types
class ModelAdmin:
    list_display: List[str]
    list_filter: List[str]
    search_fields: List[str]
    readonly_fields: List[str]
    fieldsets: Optional[List[tuple[Optional[str], Dict[str, Any]]]]

# Django URL Types
def path(route: str, view: Any, kwargs: Optional[Dict[str, Any]] = None, name: Optional[str] = None) -> Any: ...
def include(module: Union[str, List[Any]], namespace: Optional[str] = None) -> Any: ...

# Django Template Context
class Context(Dict[str, Any]): ...

# Django Settings
class Settings:
    DEBUG: bool
    SECRET_KEY: str
    ALLOWED_HOSTS: List[str]
    INSTALLED_APPS: List[str]
    DATABASES: Dict[str, Dict[str, Any]]
    STATIC_URL: str
    MEDIA_URL: str
