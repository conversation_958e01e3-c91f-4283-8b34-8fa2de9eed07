"""
Django models type stubs for better Pylance support
"""
from typing import Any, Optional, Type, TypeVar, Generic
from django.db.models import Model, Manager, QuerySet

_M = TypeVar('_M', bound=Model)

class BaseManager(Manager[_M], Generic[_M]):
    def get_queryset(self) -> QuerySet[_M]: ...
    def all(self) -> QuerySet[_M]: ...
    def filter(self, **kwargs: Any) -> QuerySet[_M]: ...
    def exclude(self, **kwargs: Any) -> QuerySet[_M]: ...
    def get(self, **kwargs: Any) -> _M: ...
    def create(self, **kwargs: Any) -> _M: ...
    def get_or_create(self, **kwargs: Any) -> tuple[_M, bool]: ...
    def update_or_create(self, **kwargs: Any) -> tuple[_M, bool]: ...

class ModelBase(type):
    objects: BaseManager[Any]
    DoesNotExist: Type[Exception]
    MultipleObjectsReturned: Type[Exception]
