#!/usr/bin/env python3
"""Fix unclosed docstrings in model files."""

import logging
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

MODELS_DIR = '/workspaces/clear_htmx/CLEAR/models'

def fix_unclosed_docstrings(filepath):
    """Fix unclosed docstrings in a file."""
    try:
        with open(filepath, encoding='utf-8') as f:
            content = f.read()

        lines = content.split('\n')
        fixed_lines = []
        in_docstring = False
        docstring_start = -1

        for i, line in enumerate(lines):
            # Check for triple quotes
            if '"""' in line:
                count = line.count('"""')
                if count == 1:
                    if not in_docstring:
                        in_docstring = True
                        docstring_start = i
                    else:
                        in_docstring = False
                        docstring_start = -1
                elif count == 2:
                    # Complete docstring on one line, no change needed
                    pass

            fixed_lines.append(line)

        # If we're still in a docstring, we need to close it
        if in_docstring:
            # Look for the best place to close it
            # Usually it's after a class or function definition
            for j in range(docstring_start + 1, len(fixed_lines)):
                line = fixed_lines[j].strip()
                if line and not line.startswith('#'):
                    # Found next code line, insert closing before it
                    fixed_lines.insert(j, '    """')
                    logger.info(f"Added closing docstring at line {j+1} in {filepath}")
                    break
            else:
                # No code found after, just close at end
                if fixed_lines and fixed_lines[-1].strip() == '"""':
                    # Already has closing quotes
                    pass
                else:
                    fixed_lines.append('"""')
                    logger.info(f"Added closing docstring at end of {filepath}")

        # Remove any trailing docstrings
        while fixed_lines and fixed_lines[-1].strip() == '"""':
            fixed_lines.pop()
            logger.info(f"Removed trailing docstring from {filepath}")

        # Fix indentation issues
        for i in range(len(fixed_lines)):
            if i > 0:
                prev_line = fixed_lines[i-1].strip()
                curr_line = fixed_lines[i]

                # Fix import indentation
                if prev_line.startswith(('import ', 'from ')) and curr_line.strip().startswith(('import ', 'from ')):
                    if len(curr_line) - len(curr_line.lstrip()) > 0:
                        fixed_lines[i] = curr_line.lstrip()
                        logger.info(f"Fixed import indentation at line {i+1}")

        content = '\n'.join(fixed_lines)

        # Ensure newline at end
        if content and not content.endswith('\n'):
            content += '\n'

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        return True

    except Exception as e:
        logger.error(f"Error fixing {filepath}: {e}")
        return False

def main():
    """Fix all model files."""
    logger.info("Fixing docstrings in all model files...")

    fixed_count = 0

    for filename in os.listdir(MODELS_DIR):
        if filename.endswith('.py') and not filename.startswith('__'):
            filepath = os.path.join(MODELS_DIR, filename)
            if fix_unclosed_docstrings(filepath):
                fixed_count += 1

    logger.info(f"Fixed {fixed_count} files")

if __name__ == "__main__":
    main()
