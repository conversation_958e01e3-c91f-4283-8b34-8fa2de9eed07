#!/usr/bin/env python3
"""
Fix all syntax errors in the CLEAR Django application.
"""

import ast
import logging
import os
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def is_python_file(filepath):
    """Check if file is a Python file."""
    return filepath.endswith('.py')

def fix_unclosed_docstrings(content):
    """Fix unclosed triple-quoted strings."""
    lines = content.split('\n')
    fixed_lines = []
    in_docstring = False
    docstring_char = None
    docstring_start_line = None
    
    for i, line in enumerate(lines):
        # Check for triple quotes
        if '"""' in line or "'''" in line:
            # Count occurrences
            triple_double = line.count('"""')
            triple_single = line.count("'''")
            
            # Handle triple double quotes
            if triple_double % 2 == 1:
                if not in_docstring:
                    in_docstring = True
                    docstring_char = '"""'
                    docstring_start_line = i
                elif docstring_char == '"""':
                    in_docstring = False
                    docstring_char = None
                    docstring_start_line = None
            
            # Handle triple single quotes
            if triple_single % 2 == 1:
                if not in_docstring:
                    in_docstring = True
                    docstring_char = "'''"
                    docstring_start_line = i
                elif docstring_char == "'''":
                    in_docstring = False
                    docstring_char = None
                    docstring_start_line = None
        
        fixed_lines.append(line)
    
    # If we're still in a docstring at the end, close it
    if in_docstring and docstring_char:
        fixed_lines.append(docstring_char)
        logger.info(f"Closed unclosed docstring starting at line {docstring_start_line + 1}")
    
    return '\n'.join(fixed_lines)

def fix_malformed_imports(content):
    """Fix malformed import statements."""
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        # Fix duplicate 'from' keywords
        if 'from' in line:
            line = line.replace('from', 'from')
        
        # Fix imports with multiple 'import' keywords
        if line.strip().startswith('from') and 'import import' in line:
            line = line.replace('import import', 'import')
        
        # Fix imports that have module path issues
        if line.strip().startswith('from .') and ' import' in line:
            # Ensure there's only one dot sequence
            line = re.sub(r'from \.+', 'from .', line)
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_auth_model_file():
    """Specifically fix the auth.py file which is truncated."""
    auth_file = '/workspaces/clear_htmx/CLEAR/models/auth.py'
    
    try:
        with open(auth_file, 'r') as f:
            content = f.read()
        
        # Remove the trailing unclosed docstring
        if content.rstrip().endswith('"""') and content.count('"""') % 2 == 1:
            # Find the last occurrence of """
            last_docstring_pos = content.rfind('"""')
            # Check if it's truly unclosed by counting quotes before it
            before_last = content[:last_docstring_pos]
            if before_last.count('"""') % 2 == 0:
                # This """ is opening a docstring that never closes
                content = content[:last_docstring_pos].rstrip()
                logger.info("Removed unclosed docstring at end of auth.py")
        
        # Ensure the file ends properly
        if not content.rstrip().endswith('\n'):
            content = content.rstrip() + '\n'
        
        with open(auth_file, 'w') as f:
            f.write(content)
        
        logger.info("Fixed auth.py file")
        
    except Exception as e:
        logger.error(f"Error fixing auth.py: {e}")

def fix_file_syntax(filepath):
    """Fix syntax errors in a single file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix unclosed docstrings
        content = fix_unclosed_docstrings(content)
        
        # Fix malformed imports
        content = fix_malformed_imports(content)
        
        # Fix escaped quotes in docstrings
        content = content.replace('\"""', '"""')
        content = content.replace("\\'\\'\\'", "'''")
        
        # Try to parse the file to check for syntax errors
        try:
            ast.parse(content)
        except SyntaxError as e:
            logger.warning(f"Syntax error remains in {filepath}: {e}")
            # Try additional fixes based on the error
            if "unterminated triple-quoted string" in str(e):
                # Add closing quotes at the end
                content = content.rstrip() + '\n"""'
        
        # Only write if content changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Fixed {filepath}")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error processing {filepath}: {e}")
        return False

def fix_all_syntax_errors():
    """Fix syntax errors in all Python files."""
    project_dir = '/workspaces/clear_htmx'
    fixed_count = 0
    error_count = 0
    
    # First, fix the known problematic auth.py file
    fix_auth_model_file()
    
    # Walk through all Python files
    for root, dirs, files in os.walk(project_dir):
        # Skip virtual environments and cache directories
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git', 'env', '.venv']]
        
        for file in files:
            if is_python_file(file):
                filepath = os.path.join(root, file)
                
                # Skip migration files
                if '/migrations/' in filepath:
                    continue
                
                if fix_file_syntax(filepath):
                    fixed_count += 1
    
    # Now check each file for remaining syntax errors
    logger.info("\nChecking for remaining syntax errors...")
    files_with_errors = []
    
    for root, dirs, files in os.walk(project_dir):
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git', 'env', '.venv']]
        
        for file in files:
            if is_python_file(file):
                filepath = os.path.join(root, file)
                
                if '/migrations/' in filepath:
                    continue
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    ast.parse(content)
                except SyntaxError as e:
                    files_with_errors.append((filepath, str(e)))
                    error_count += 1
                except Exception as e:
                    logger.error(f"Error checking {filepath}: {e}")
    
    # Report results
    logger.info(f"\nFixed {fixed_count} files")
    logger.info(f"Remaining files with syntax errors: {error_count}")
    
    if files_with_errors:
        logger.error("\nFiles still containing syntax errors:")
        for filepath, error in files_with_errors[:10]:  # Show first 10
            logger.error(f"  {filepath}: {error}")
        
        if len(files_with_errors) > 10:
            logger.error(f"  ... and {len(files_with_errors) - 10} more files")
    
    return fixed_count, error_count

if __name__ == "__main__":
    logger.info("Starting comprehensive syntax error fix...")
    fixed, errors = fix_all_syntax_errors()
    
    if errors == 0:
        logger.info("\nAll syntax errors have been fixed! Django should now be able to start.")
    else:
        logger.warning(f"\n{errors} files still have syntax errors. Manual intervention may be needed.")
"""
"""