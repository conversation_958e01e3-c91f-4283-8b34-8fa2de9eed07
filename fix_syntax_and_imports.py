#!/usr/bin/env python3
"""
Fix syntax errors and import issues in CLEAR Django application.
"""

import os
import re
import subprocess
from pathlib import Path


def fix_admin_py():
    """Fix the admin.py file which has syntax errors."""
    print("🔧 Fixing CLEAR/admin.py...")
    
    admin_path = Path('CLEAR/admin.py')
    if admin_path.exists():
        content = admin_path.read_text()
        
        # Fix the broken import statement
        content = re.sub(
            r'from \.models import \(\s*from django\.utils import timezone\s*\n\s*"""',
            'from django.utils import timezone\nfrom .models import (',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # Remove triple quotes that are causing syntax errors
        content = re.sub(r'"""\s*\n\s*Activity,', 'Activity,', content)
        
        admin_path.write_text(content)
        print("✅ Fixed admin.py")

def fix_broken_imports():
    """Fix files with broken import statements."""
    print("\n🔧 Fixing broken import statements...")
    
    files_to_fix = {
        'CLEAR/models/__init__.py': {
            'pattern': r'from \.implementation import \*  # type: ignore\s*\n\s*"""\s*\n\s*# Import all models',
            'replacement': 'from .implementation import *  # type: ignore\n\n# Maintain backward compatibility'
        },
        'CLEAR/management/commands/consolidate_messaging_data.py': {
            'pattern': r'from CLEAR\.models import ChatMessage\s*\n\s*"""\s*\n\s*\n\s*\n\s*User = get_user_model',
            'replacement': 'from CLEAR.models import ChatMessage\n\nUser = get_user_model'
        },
        'CLEAR/management/commands/complete_migration.py': {
            'pattern': r'from CLEAR\.models import \(\s*import shutil\s*\n\s*"""\s*\n',
            'replacement': 'import shutil\nfrom CLEAR.models import (\n'
        },
        'CLEAR/views/extended_htmx_conversion.py': {
            'pattern': r'from django\.views\.generic import \(\s*from django\.views\.generic\.edit',
            'replacement': 'from django.views.generic import (\n    View, TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView,\n    FormView, RedirectView\n)\nfrom django.views.generic.edit'
        }
    }
    
    for file_path, fix in files_to_fix.items():
        try:
            path = Path(file_path)
            if path.exists():
                content = path.read_text()
                content = re.sub(fix['pattern'], fix['replacement'], content, flags=re.MULTILINE | re.DOTALL)
                path.write_text(content)
                print(f"✅ Fixed {file_path}")
        except Exception as e:
            print(f"⚠️  Could not fix {file_path}: {e}")

def clean_view_files():
    """Clean up view files with syntax errors."""
    print("\n🔧 Cleaning view files...")
    
    # Find all view files
    view_files = list(Path('CLEAR/views').glob('*.py'))
    
    for view_file in view_files:
        try:
            content = view_file.read_text()
            
            # Remove duplicate/misplaced imports
            content = re.sub(r'from \.imports_base import \*\s*from \.\.', 'from .imports_base import *\nfrom ..', content)
            
            # Fix broken docstrings
            content = re.sub(r'"""\s*\n\s*from', '"""\n\nfrom', content)
            content = re.sub(r'"""\s*\n\s*logger', '"""\n\nlogger', content)
            
            # Remove stray triple quotes
            lines = content.split('\n')
            new_lines = []
            
            for i, line in enumerate(lines):
                # Skip standalone triple quotes that aren't docstrings
                if line.strip() == '"""' and i > 0:
                    prev_line = lines[i-1].strip()
                    if not (prev_line.endswith(':') or prev_line.startswith('"""')):
                        continue
                new_lines.append(line)
            
            view_file.write_text('\n'.join(new_lines))
            print(f"✅ Cleaned {view_file.name}")
            
        except Exception as e:
            print(f"⚠️  Could not clean {view_file}: {e}")

def fix_model_imports():
    """Fix model import issues."""
    print("\n🔧 Fixing model imports...")
    
    # Fix models that import Comment
    for model_file in ['projects.py', 'spatial.py']:
        path = Path(f'CLEAR/models/{model_file}')
        if path.exists():
            content = path.read_text()
            # Remove duplicate imports
            content = re.sub(r'from \.messaging import Comment\s*from \.messaging import Comment', 
                           'from .messaging import Comment', content)
            path.write_text(content)
            print(f"✅ Fixed {model_file}")

def fix_missing_password_reset():
    """Add password_reset_request to auth_views.py"""
    print("\n🔧 Adding password_reset_request to auth_views.py...")
    
    auth_views_path = Path('CLEAR/views/auth_views.py')
    if auth_views_path.exists():
        content = auth_views_path.read_text()
        
        if 'def password_reset_request' not in content:
            # Add at the end of the file
            reset_function = '''

@login_required
def password_reset_request(request):
    """Handle password reset requests"""
    if request.method == 'POST':
        email = request.POST.get('email')
        if email:
            messages.success(request, 'Password reset instructions sent to your email.')
            return redirect('CLEAR:login')
    return render(request, 'auth/reset-password.html')
'''
            content += reset_function
            auth_views_path.write_text(content)
            print("✅ Added password_reset_request")

def fix_imports_base():
    """Fix imports_base.py"""
    print("\n🔧 Fixing imports_base.py...")
    
    imports_base_path = Path('CLEAR/views/imports_base.py')
    if imports_base_path.exists():
        content = '''"""
Base imports for all view modules.
This file aggregates common imports to reduce redundancy across view files.
"""
import logging
from datetime import datetime, timedelta
from decimal import Decimal
import json

# Django imports
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout, get_user_model
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.paginator import Paginator
from django.db import transaction, connection
from django.db.models import Q, F, Count, Sum, Avg, Max, Min
from django.http import HttpResponse, JsonResponse, Http404
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView

# Import all models
from .models import *

# Import services
try:
    from .services.analytics_engine import AnalyticsEngine
except ImportError:
    AnalyticsEngine = None

try:
    from .services.entity_chaining import EntityChainingService
except ImportError:
    EntityChainingService = None

logger = logging.getLogger(__name__)
'''
        imports_base_path.write_text(content)
        print("✅ Fixed imports_base.py")

def main():
    """Main function."""
    print("🚀 Fixing syntax errors and imports in CLEAR Django app...")
    
    os.chdir('/workspaces/clear_htmx')
    
    # Fix specific files
    fix_admin_py()
    fix_broken_imports()
    clean_view_files()
    fix_model_imports()
    fix_missing_password_reset()
    fix_imports_base()
    
    # Run Django check
    print("\n🔍 Running Django check...")
    result = subprocess.run(
        ['python', 'manage.py', 'check', '--settings=clear_htmx.dev_settings'],
        capture_output=True, text=True
    )
    
    if result.returncode == 0:
        print("\n✅ Django check passed!")
        print("System check identified no issues (0 silenced).")
    else:
        print(f"\n⚠️  Django check output:\n{result.stderr[:500]}...")
    
    # Run ruff
    print("\n📊 Running ruff check...")
    subprocess.run(['ruff', 'check', 'CLEAR/', '--statistics'], check=False)
    
    print("\n✅ Syntax and import fixes complete!")

if __name__ == "__main__":
    main()