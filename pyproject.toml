[project]
name = "clear-htmx"
version = "0.1.0"
description = "CLEAR - Utility Coordination Platform (Django + HTMX Migration)"
requires-python = ">=3.12"
dependencies = [
    "django>=5.2.3",
    "django-htmx>=1.19.0",
    "django-extensions>=3.2.3",
    "psycopg2-binary>=2.9.9",
    "django-environ>=0.11.2",
    "django-crispy-forms>=2.1",
    "crispy-bootstrap5>=2024.2",
    "pillow>=10.4.0",
    "django-storages>=1.14.4",
    "django-cors-headers>=4.4.0",
    "djangorestframework>=3.15.2",
    "django-allauth>=65.0.2",
    "celery>=5.4.0",
    "redis>=5.1.1",
    "django-redis>=6.0.0",
    "whitenoise>=6.8.2",
    "gunicorn>=23.0.0",
    "gevent>=24.2.1",
    "dj-config-url>=0.1.1",
    "dj-database-url>=3.0.0",
    "python-dotenv>=1.1.0",
    "GDAL>=3.4.0,<3.9.0",
    "Fiona>=1.9.0",
    "channels>=4.2.2",
    "channels-redis>=4.2.0",
    "django-filter>=25.1",
    "psutil>=6.1.0",
    "playwright>=1.50.0",
    "django-browser-reload>=1.12.1",
    "pytest>=8.4.1",
    "pytest-django>=4.9.0",
    "pytest-asyncio>=0.21.0",
    "pytest-playwright>=0.6.0",
    "certifi>=2025.6.15",
    "urllib3>=2.5.0",
    "docker-mcp>=0.2.0",
    "beautifulsoup4>=4.12.3",
    "daphne>=4.1.2",
]

[project.optional-dependencies]
test = [
    "pytest-cov>=5.0.0",
    "coverage>=7.5.4",
]

[tool.setuptools]
packages = ["CLEAR", "clear_htmx"]

[tool.ruff]
# Exclude intentional import star usage and generated files
exclude = [
    "CLEAR/models/__init__.py",  # Intentionally uses import * to aggregate models
    "CLEAR/views/__init__.py",   # Intentionally uses import * to aggregate views
    "**/migrations/*.py",        # Django migrations
    "__pycache__",
    "staticfiles",
    "media",
    "logs",
    "coty-playground",
    "source-repo",
    "refactor_migration",
]

# Select comprehensive rules for maximum auto-fixing
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "DJ",   # flake8-django
    "PIE",  # flake8-pie
    "SIM",  # flake8-simplify
    "RUF",  # Ruff-specific rules
]

# Ignore these specific rules globally
ignore = [
    "E501",   # line too long (handled by formatter)
    "F403",   # import star usage (we'll handle case by case)
    "F405",   # may be undefined from star imports
    "F401",   # unused import (will be auto-fixed)
    "F841",   # unused variable (will be auto-fixed)
    "E402",   # module level import not at top (Django patterns)
    "DJ001",  # Django model without __str__ method
    "N806",   # Variable should be lowercase (Django patterns)
    "B008",   # Function calls in argument defaults (Django patterns)
]

# Enable auto-fixing for most rules
fixable = ["ALL"]
unfixable = []

# Same as Black
line-length = 88

# Python version
target-version = "py312"

# Per-file ignores
[tool.ruff.per-file-ignores]
"CLEAR/models/__init__.py" = ["F403", "F405"]  # import star is intentional here
"CLEAR/views/__init__.py" = ["F403", "F405"]   # import star is intentional here
"CLEAR/models.py" = ["F403"]                    # backwards compatibility file
"**/__init__.py" = ["F401"]                     # unused imports in __init__ are usually intentional
