{"include": ["CLEAR", "clear_htmx", "tests"], "exclude": ["**/node_modules", "**/__pycache__", "**/.*", "**/migrations", "**/staticfiles", "**/media", "**/logs", "**/coty-playground", "**/source-repo", "**/refactor_migration", "**/*.pyc", "**/*.pyo", "**/fix_*.py", "**/check_*.py", "**/comprehensive_*.py", "**/configure_*.py", "**/run_all_*.py"], "defineConstant": {"DEBUG": true}, "stubPath": "./typings", "venvPath": "/workspaces/clear_htmx/.conda", "venv": ".", "pythonVersion": "3.12", "pythonPlatform": "Linux", "executionEnvironments": [{"root": ".", "pythonVersion": "3.12", "pythonPlatform": "Linux", "extraPaths": ["./", "./CLEAR", "./clear_htmx", "./templates", "./static", "./typings"]}], "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoImportCompletions": true, "autoSearchPaths": true, "reportMissingImports": "information", "reportMissingTypeStubs": "none", "reportImportCycles": "warning", "reportUnusedImport": "information", "reportUnusedClass": "information", "reportUnusedFunction": "information", "reportUnusedVariable": "information", "reportDuplicateImport": "warning", "reportOptionalSubscript": "information", "reportOptionalMemberAccess": "information", "reportOptionalCall": "information", "reportOptionalIterable": "information", "reportOptionalContextManager": "information", "reportOptionalOperand": "information", "reportTypedDictNotRequiredAccess": "information", "reportPrivateImportUsage": "information", "reportConstantRedefinition": "information", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportOverlappingOverloads": "warning", "reportUninitializedInstanceVariable": "information", "reportCallInDefaultInitializer": "information", "reportUnnecessaryIsInstance": "information", "reportUnnecessaryCast": "information", "reportUnnecessaryComparison": "information", "reportAssertAlwaysTrue": "warning", "reportSelfClsParameterName": "warning", "reportImplicitStringConcatenation": "information", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStubStatement": "warning", "reportIncompleteStub": "information", "reportUnsupportedDunderAll": "warning", "reportUnusedCoroutine": "warning", "reportFunctionMemberAccess": "information", "reportArgumentType": "information", "reportAssignmentType": "information", "reportAttributeAccessIssue": "information", "reportCallIssue": "information", "reportIndexIssue": "information", "reportOperatorIssue": "information", "reportRedeclaration": "warning", "reportReturnType": "information", "reportTypeCommentUsage": "information", "reportPrivateUsage": "information", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "information", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "information", "reportInvalidTypeVarUse": "warning", "reportGeneralTypeIssues": "information", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "information", "reportUntypedNamedTuple": "information", "reportUnusedCallResult": "none"}