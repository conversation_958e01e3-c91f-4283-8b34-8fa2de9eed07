# Comprehensive check for unbalanced triple quotes
import re

with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py', 'r') as f:
    lines = f.readlines()

in_docstring = False
docstring_start = None

print("Comprehensive triple quote analysis:")
print("=" * 50)

for i, line in enumerate(lines):
    line_num = i + 1
    
    # Count triple quotes in this line
    triple_quotes = line.count('"""')
    
    if triple_quotes > 0:
        print(f"Line {line_num}: {triple_quotes} quotes -> {repr(line.strip())}")
        
        for quote_idx in range(triple_quotes):
            if not in_docstring:
                # Starting a docstring
                in_docstring = True
                docstring_start = line_num
                print(f"  -> OPENING docstring at line {line_num}")
            else:
                # Ending a docstring
                in_docstring = False
                print(f"  -> CLOSING docstring (opened at line {docstring_start})")
                docstring_start = None

print("=" * 50)
print(f"Final state: in_docstring = {in_docstring}")

if in_docstring:
    print(f"PROBLEM FOUND: Unclosed docstring starting at line {docstring_start}")
    print("Context around that line:")
    start = max(0, docstring_start - 3)
    end = min(len(lines), docstring_start + 10)
    for j in range(start, end):
        marker = " >>> " if j + 1 == docstring_start else "     "
        print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
else:
    print("All docstrings appear to be balanced.")
    print("This suggests the issue might be with quote characters that look like")
    print("triple quotes but are actually different Unicode characters.")