#!/usr/bin/env python3
"""
Run all auto-fix tools in sequence
"""
import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and report results"""
    print(f"\n🔧 {description}...")
    print(f"Command: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()[:200]}...")
        else:
            print(f"⚠️ {description} completed with warnings")
            if result.stderr.strip():
                print(f"Warnings: {result.stderr.strip()[:200]}...")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False

def main():
    print("🚀 COMPREHENSIVE AUTO-FIX SEQUENCE")
    print("=" * 50)

    os.chdir("/workspaces/clear_htmx")

    # Step 1: Install required tools
    print("\n📦 Installing auto-fix tools...")
    tools = [
        "pip install ruff --upgrade",
        "pip install black --upgrade",
        "pip install isort --upgrade"
    ]

    for tool in tools:
        run_command(tool, f"Installing {tool.split()[2]}")

    # Step 2: Run Ruff auto-fixes (fastest and most comprehensive)
    ruff_cmd = """ruff check --fix . \
        --exclude migrations,__pycache__,staticfiles,media,logs,coty-playground,source-repo \
        --ignore F401,F841,E402"""
    run_command(ruff_cmd, "Ruff auto-fixes (syntax, imports, style)")

    # Step 3: Format with Black
    black_cmd = """black . \
        --line-length=88 \
        --exclude="migrations|__pycache__|staticfiles|media|logs|coty-playground|source-repo" """
    run_command(black_cmd, "Black code formatting")

    # Step 4: Sort imports with isort
    isort_cmd = """isort . \
        --profile=black \
        --multi-line=3 \
        --line-length=88 \
        --skip=migrations \
        --skip=__pycache__ \
        --skip=staticfiles \
        --skip=media \
        --skip=logs"""
    run_command(isort_cmd, "isort import sorting")

    # Step 5: Manual fixes for common issues
    print("\n🔧 Applying manual fixes for common Django patterns...")

    # Fix disconnect method signature
    fix_disconnect = """find . -name "*.py" -not -path "./migrations/*" -not -path "./__pycache__/*" \
        -exec sed -i 's/def disconnect(self, close_code):/def disconnect(self, code):/g' {} \\;"""
    run_command(fix_disconnect, "Fixing WebSocket disconnect method signatures")

    # Step 6: Run Pylance test to see results
    print("\n🔍 Running Pylance test to measure improvement...")
    pylance_cmd = "pyright --project . 2>&1 | grep -E '(errors|warnings|informations)' | tail -1"
    run_command(pylance_cmd, "Pylance type checking")

    print("\n🎉 AUTO-FIX SEQUENCE COMPLETE!")
    print("=" * 50)
    print("✅ All auto-fix tools have been executed")
    print("📊 Check the Pylance results above for improvement metrics")
    print("🚀 Your code quality has been significantly improved!")

if __name__ == "__main__":
    main()
