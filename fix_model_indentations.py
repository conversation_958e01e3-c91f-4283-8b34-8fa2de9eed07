#!/usr/bin/env python3
"""Fix indentation issues in model files."""

import os

MODELS_DIR = '/workspaces/clear_htmx/CLEAR/models'

def fix_indentation(filepath):
    """Fix indentation issues in a file."""
    with open(filepath) as f:
        lines = f.readlines()

    fixed_lines = []
    inside_class = False

    for i, line in enumerate(lines):
        stripped = line.lstrip()

        # Detect class definitions
        if stripped.startswith('class ') and ':' in stripped:
            inside_class = True
            fixed_lines.append(line)
            continue

        # If we're inside a class
        if inside_class and stripped:
            # Check if this should be a class member
            if (stripped.startswith(('def ', '"""', "'''", '#', '@')) or
                ('=' in stripped and not stripped.startswith('(')) or
                stripped == 'class Meta:'):

                # Determine proper indentation
                if stripped.startswith('def ') or stripped == 'class Meta:':
                    # Methods and Meta class should be indented 4 spaces
                    fixed_line = '    ' + stripped
                elif '=' in stripped and not stripped.startswith(('def ', '"', "'")):
                    # Field definitions - check if previous line was also a field
                    if i > 0:
                        prev_stripped = lines[i-1].lstrip()
                        if '=' in prev_stripped and 'models.' in prev_stripped:
                            # Match previous field indentation
                            prev_indent = len(lines[i-1]) - len(prev_stripped)
                            fixed_line = ' ' * prev_indent + stripped
                        else:
                            # Default field indentation
                            fixed_line = '    ' + stripped
                    else:
                        fixed_line = '    ' + stripped
                else:
                    # Keep original indentation for other lines
                    fixed_line = line

                # Check if we have an indentation mismatch
                if line != fixed_line:
                    print(f"Fixed indentation at line {i+1} in {os.path.basename(filepath)}")

                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)

        # Reset when we exit a class
        if inside_class and stripped and not stripped.startswith(' ') and not stripped.startswith(('\t', 'def ', 'class ')):
            if '=' not in stripped or stripped.startswith('class '):
                inside_class = False

    with open(filepath, 'w') as f:
        f.writelines(fixed_lines)

def main():
    """Fix all model files."""
    print("Fixing indentations in model files...")

    files_to_fix = [
        'intelligence.py',
        'system.py',
        'user_activity.py',
    ]

    for filename in files_to_fix:
        filepath = os.path.join(MODELS_DIR, filename)
        if os.path.exists(filepath):
            try:
                fix_indentation(filepath)
            except Exception as e:
                print(f"Error fixing {filename}: {e}")

if __name__ == "__main__":
    main()
