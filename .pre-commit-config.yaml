repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-json
      - id: pretty-format-json
        args: ['--autofix']

  - repo: https://github.com/pycqa/autoflake
    rev: v2.2.1
    hooks:
      - id: autoflake
        args:
          - --remove-all-unused-imports
          - --remove-unused-variables
          - --remove-duplicate-keys
          - --in-place
        exclude: ^(migrations/|__pycache__/|\.pyc$|staticfiles/|media/)

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.15
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        exclude: ^(migrations/|__pycache__/|\.pyc$|staticfiles/|media/)

  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: ['--line-length=88']

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ['--profile=black', '--multi-line=3']

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: ['--max-line-length=88', '--extend-ignore=E203,W503,F401,F841']
        exclude: ^(migrations/|__pycache__/|\.pyc$|staticfiles/|media/)

  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-r', 'CLEAR/', 'clear_htmx/']
        exclude: ^tests/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [django-stubs, djangorestframework-stubs, types-requests, types-factory-boy]
        args: ['--ignore-missing-imports', '--no-strict-optional']
        exclude: ^(migrations/|__pycache__/|\.pyc$|staticfiles/|media/|tests/)
