import ast

# Extract just the docstring line and a few lines around it to test
test_code = '''def realtime_notifications_htmx(request):
    """Poll for new notifications (WebSocket fallback)"""
    last_check = request.GET.get('last_check')
'''

print("Testing this code:")
print(repr(test_code))
print()
print("Formatted:")
print(test_code)

try:
    ast.parse(test_code)
    print("SUCCESS: Code parses correctly")
except SyntaxError as e:
    print(f"SYNTAX ERROR: {e}")
    print(f"Line {e.lineno}: {e.text}")
    if e.offset:
        print(' ' * (e.offset - 1) + '^')

# Now test the actual line from the file
with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py', 'r') as f:
    lines = f.readlines()

# Get the actual line 5378 and surrounding context
actual_line = lines[5377]  # 0-indexed
print("\nActual line 5378:")
print(repr(actual_line))

# Try to create a minimal test case with the actual line
actual_test = f'''def realtime_notifications_htmx(request):
{actual_line}    last_check = request.GET.get('last_check')
'''

print("\nTesting actual line:")
print(repr(actual_test))

try:
    ast.parse(actual_test)
    print("SUCCESS: Actual line parses correctly in isolation")
except SyntaxError as e:
    print(f"SYNTAX ERROR with actual line: {e}")
    print(f"Line {e.lineno}: {e.text}")
    if e.offset:
        print(' ' * (e.offset - 1) + '^')