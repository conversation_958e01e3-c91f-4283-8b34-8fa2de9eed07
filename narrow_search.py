#!/usr/bin/env python3

def find_exact_error():
    with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py') as f:
        content = f.read()

    lines = content.split('\n')

    # Test ranges to narrow down the problem
    ranges_to_test = [
        (5140, 5160),  # Around line 5157
        (5160, 5180),  # Around line 5182
        (5180, 5200),  # After 5182
    ]

    for start, end in ranges_to_test:
        print(f"\nTesting lines {start} to {end}:")
        test_content = '\n'.join(lines[start-1:end])  # -1 because lines are 0-indexed

        try:
            compile(test_content, '<string>', 'exec')
            print(f"  Lines {start}-{end}: OK")
        except SyntaxError as e:
            print(f"  Lines {start}-{end}: ERROR - {e}")

            # Show the problematic lines
            error_line = start + e.lineno - 1 if e.lineno else start
            print(f"  Problem around line {error_line}:")

            context_start = max(start, error_line - 5)
            context_end = min(end, error_line + 5)

            for i in range(context_start, context_end):
                marker = " >>> " if i == error_line else "     "
                line_content = lines[i-1] if i <= len(lines) else ""
                print(f"  {marker}{i:4d}: {line_content}")

if __name__ == "__main__":
    find_exact_error()
