
with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py', 'r') as f:
    content = f.read()

# Track opening and closing of docstrings
lines = content.split('\n')
open_docstring = False
docstring_start_line = None

for i, line in enumerate(lines):
    # Look for triple quotes
    triple_quote_count = line.count('"""')
    
    if triple_quote_count > 0:
        print(f"Line {i+1}: {triple_quote_count} triple quotes - {line.strip()}")
        
        # Handle each triple quote on this line
        for _ in range(triple_quote_count):
            if not open_docstring:
                # Opening a docstring
                open_docstring = True
                docstring_start_line = i + 1
                print(f"  -> Opening docstring at line {docstring_start_line}")
            else:
                # Closing a docstring
                open_docstring = False
                print(f"  -> Closing docstring (opened at line {docstring_start_line})")
                docstring_start_line = None

if open_docstring:
    print("\nFOUND UNCLOSED DOCSTRING!")
    print(f"Opened at line {docstring_start_line} but never closed")
    print(f"Context around line {docstring_start_line}:")
    start = max(0, docstring_start_line - 5)
    end = min(len(lines), docstring_start_line + 10)
    for j in range(start, end):
        marker = ' >>> ' if j + 1 == docstring_start_line else '     '
        print(f'{marker}{j+1:4d}: {lines[j]}')
else:
    print("\nAll docstrings appear to be balanced")