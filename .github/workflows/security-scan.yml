name: Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans weekly on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-security-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-security-
          
    - name: Install security scanning tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety semgrep
        pip install -r requirements.txt
        
    - name: Run Bandit security scan
      run: |
        echo "## Bandit Security Scan Results" >> $GITHUB_STEP_SUMMARY
        bandit -r CLEAR/ clear_htmx/ -f json -o bandit-report.json || true
        bandit -r CLEAR/ clear_htmx/ -f txt || true
        
    - name: Run Safety dependency vulnerability scan
      run: |
        echo "## Safety Dependency Scan Results" >> $GITHUB_STEP_SUMMARY
        safety check --json --output safety-report.json || true
        safety check || true
        
    - name: Run Semgrep security scan
      run: |
        echo "## Semgrep Security Scan Results" >> $GITHUB_STEP_SUMMARY
        semgrep --config=auto --json --output=semgrep-report.json CLEAR/ clear_htmx/ || true
        semgrep --config=auto CLEAR/ clear_htmx/ || true
        
    - name: Django security check
      run: |
        echo "## Django Security Check Results" >> $GITHUB_STEP_SUMMARY
        python manage.py check --deploy --settings=clear_htmx.settings || true
        
    - name: Check for hardcoded secrets
      run: |
        echo "## Secret Detection Results" >> $GITHUB_STEP_SUMMARY
        # Check for common secret patterns
        grep -r -n -i "password\s*=" --include="*.py" . || echo "No hardcoded passwords found"
        grep -r -n -i "secret_key\s*=" --include="*.py" . || echo "No hardcoded secret keys found"
        grep -r -n -i "api_key\s*=" --include="*.py" . || echo "No hardcoded API keys found"
        
    - name: Upload security scan results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        path: |
          bandit-report.json
          safety-report.json
          semgrep-report.json
        retention-days: 30
        
    - name: Generate security summary
      if: always()
      run: |
        echo "## Security Scan Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Bandit (Python security): ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Safety (Dependencies): ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Semgrep (Code patterns): ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Django security check: ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Secret detection: ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "📊 **Review the detailed logs above for any security issues**" >> $GITHUB_STEP_SUMMARY

  dependency-review:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Dependency Review
      uses: actions/dependency-review-action@v3
      with:
        fail-on-severity: moderate
        allow-licenses: MIT, Apache-2.0, BSD-2-Clause, BSD-3-Clause, ISC, GPL-3.0
