name: Database Migration Testing

on:
  push:
    branches: [ main, develop ]
    paths:
      - '**/migrations/**'
      - '**/models.py'
      - 'CLEAR/models/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - '**/migrations/**'
      - '**/models.py'
      - 'CLEAR/models/**'

jobs:
  migration-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        postgres-version: [13, 14, 15]
        
    services:
      postgres:
        image: postgis/postgis:${{ matrix.postgres-version }}-3.3
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: clear_migration_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for migration analysis
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          gdal-bin \
          libgdal-dev \
          libgeos-dev \
          libproj-dev \
          libspatialite-dev \
          spatialite-bin \
          postgresql-client \
          libpq-dev
          
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install django-migration-linter
        
    - name: Set up environment variables
      run: |
        echo "DJANGO_SETTINGS_MODULE=clear_htmx.settings" >> $GITHUB_ENV
        echo "DEBUG=False" >> $GITHUB_ENV
        echo "SECRET_KEY=github-actions-migration-test-key" >> $GITHUB_ENV
        echo "DB_NAME=clear_migration_test" >> $GITHUB_ENV
        echo "DB_USER=postgres" >> $GITHUB_ENV
        echo "DB_PASSWORD=postgres" >> $GITHUB_ENV
        echo "DB_HOST=localhost" >> $GITHUB_ENV
        echo "DB_PORT=5432" >> $GITHUB_ENV
        
    - name: Check for migration conflicts
      run: |
        echo "## Migration Conflict Check" >> $GITHUB_STEP_SUMMARY
        python manage.py makemigrations --check --dry-run || {
          echo "❌ Migration conflicts detected!" >> $GITHUB_STEP_SUMMARY
          echo "Run 'python manage.py makemigrations' to resolve conflicts." >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ No migration conflicts found" >> $GITHUB_STEP_SUMMARY
        
    - name: Test forward migrations
      run: |
        echo "## Forward Migration Test" >> $GITHUB_STEP_SUMMARY
        python manage.py migrate --noinput || {
          echo "❌ Forward migrations failed!" >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ Forward migrations successful" >> $GITHUB_STEP_SUMMARY
        
    - name: Test migration rollback
      run: |
        echo "## Migration Rollback Test" >> $GITHUB_STEP_SUMMARY
        # Get the previous migration to test rollback
        PREV_MIGRATION=$(python manage.py showmigrations --plan | grep -E '^\[X\]' | tail -2 | head -1 | awk '{print $2}' || echo "0001")
        if [ "$PREV_MIGRATION" != "0001" ]; then
          python manage.py migrate CLEAR $PREV_MIGRATION || {
            echo "❌ Migration rollback failed!" >> $GITHUB_STEP_SUMMARY
            exit 1
          }
          echo "✅ Migration rollback successful" >> $GITHUB_STEP_SUMMARY
        else
          echo "ℹ️ No previous migration to rollback to" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Re-apply migrations after rollback
      run: |
        echo "## Re-apply Migrations Test" >> $GITHUB_STEP_SUMMARY
        python manage.py migrate --noinput || {
          echo "❌ Re-applying migrations failed!" >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ Re-applying migrations successful" >> $GITHUB_STEP_SUMMARY
        
    - name: Lint migrations
      run: |
        echo "## Migration Linting Results" >> $GITHUB_STEP_SUMMARY
        django-migration-linter CLEAR --ignore-name-contains=0001_initial || {
          echo "⚠️ Migration linting found issues (see logs above)" >> $GITHUB_STEP_SUMMARY
        }
        echo "✅ Migration linting completed" >> $GITHUB_STEP_SUMMARY
        
    - name: Check for dangerous operations
      run: |
        echo "## Dangerous Operations Check" >> $GITHUB_STEP_SUMMARY
        # Check for potentially dangerous migration operations
        if grep -r "DROP TABLE\|DROP COLUMN\|ALTER COLUMN.*DROP" CLEAR/migrations/ 2>/dev/null; then
          echo "⚠️ Potentially dangerous migration operations found!" >> $GITHUB_STEP_SUMMARY
          echo "Please review these operations carefully." >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ No dangerous operations detected" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Generate migration summary
      if: always()
      run: |
        echo "## Migration Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "- PostgreSQL Version: ${{ matrix.postgres-version }}" >> $GITHUB_STEP_SUMMARY
        echo "- Migration Conflicts: ✅ Checked" >> $GITHUB_STEP_SUMMARY
        echo "- Forward Migrations: ✅ Tested" >> $GITHUB_STEP_SUMMARY
        echo "- Migration Rollback: ✅ Tested" >> $GITHUB_STEP_SUMMARY
        echo "- Migration Linting: ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Dangerous Operations: ✅ Checked" >> $GITHUB_STEP_SUMMARY
