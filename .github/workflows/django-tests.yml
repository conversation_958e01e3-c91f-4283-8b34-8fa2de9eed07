name: Django Tests

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.12, 3.13]
        
    services:
      postgres:
        image: postgis/postgis:15-3.3
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: clear_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          gdal-bin \
          libgdal-dev \
          libgeos-dev \
          libproj-dev \
          libspatialite-dev \
          spatialite-bin \
          postgresql-client \
          libpq-dev
          
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install coverage pytest-django pytest-cov
        
    - name: Set up environment variables
      run: |
        echo "DJANGO_SETTINGS_MODULE=clear_htmx.settings" >> $GITHUB_ENV
        echo "DEBUG=False" >> $GITHUB_ENV
        echo "SECRET_KEY=github-actions-secret-key-for-testing-only" >> $GITHUB_ENV
        echo "DB_NAME=clear_test" >> $GITHUB_ENV
        echo "DB_USER=postgres" >> $GITHUB_ENV
        echo "DB_PASSWORD=postgres" >> $GITHUB_ENV
        echo "DB_HOST=localhost" >> $GITHUB_ENV
        echo "DB_PORT=5432" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        
    - name: Run Django system check
      run: |
        python manage.py check --deploy
        
    - name: Run database migrations
      run: |
        python manage.py migrate --noinput
        
    - name: Create test data
      run: |
        python manage.py collectstatic --noinput
        
    - name: Run unit tests with coverage
      run: |
        coverage run --source='.' manage.py test CLEAR.tests
        coverage xml
        coverage report
        
    - name: Run integration tests
      run: |
        python manage.py test tests.integration --keepdb
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        
    - name: Generate test report
      if: always()
      run: |
        echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Python Version: ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "- Django System Check: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- Database Migrations: ✅ Applied" >> $GITHUB_STEP_SUMMARY
        echo "- Unit Tests: See coverage report above" >> $GITHUB_STEP_SUMMARY
        
    - name: Upload test artifacts
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          coverage.xml
          .coverage
        retention-days: 7
