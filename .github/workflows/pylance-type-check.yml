name: Pylance Type Checking

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  type-check:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.12, 3.13]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          gdal-bin \
          libgdal-dev \
          libgeos-dev \
          libproj-dev \
          libspatialite-dev \
          spatialite-bin \
          postgresql-client \
          libpq-dev
          
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install django-stubs[compatible-mypy] djangorestframework-stubs
        pip install pyright
        
    - name: Set up environment variables
      run: |
        echo "DJANGO_SETTINGS_MODULE=clear_htmx.settings" >> $GITHUB_ENV
        echo "DEBUG=True" >> $GITHUB_ENV
        echo "SECRET_KEY=github-actions-secret-key-for-testing" >> $GITHUB_ENV
        echo "USE_MEMORY_CACHE=True" >> $GITHUB_ENV
        echo "USE_MEMORY_CHANNELS=True" >> $GITHUB_ENV
        
    - name: Run Django system check
      run: |
        python manage.py check --deploy
        
    - name: Run Pylance type checking with Pyright
      run: |
        echo "Running Pyright (Pylance engine) type checking..."
        pyright --project .
        
    - name: Run mypy type checking
      run: |
        echo "Running mypy type checking as backup..."
        mypy . --config-file mypy.ini || true
        
    - name: Generate type checking report
      if: always()
      run: |
        echo "## Type Checking Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Python Version: ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "- Django System Check: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- Pylance/Pyright Check: See logs above" >> $GITHUB_STEP_SUMMARY
        
    - name: Upload type checking artifacts
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: type-check-logs-${{ matrix.python-version }}
        path: |
          *.log
          .mypy_cache/
        retention-days: 7
