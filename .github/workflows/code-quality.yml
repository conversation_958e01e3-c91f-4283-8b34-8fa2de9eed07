name: Code Quality

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-quality-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-quality-
          
    - name: Install code quality tools
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 pylint mypy django-stubs
        pip install -r requirements.txt
        
    - name: Check code formatting with Black
      run: |
        echo "## Black Code Formatting Check" >> $GITHUB_STEP_SUMMARY
        black --check --diff CLEAR/ clear_htmx/ || {
          echo "❌ Code formatting issues found. Run 'black CLEAR/ clear_htmx/' to fix." >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ Code formatting is correct" >> $GITHUB_STEP_SUMMARY
        
    - name: Check import sorting with isort
      run: |
        echo "## Import Sorting Check" >> $GITHUB_STEP_SUMMARY
        isort --check-only --diff CLEAR/ clear_htmx/ || {
          echo "❌ Import sorting issues found. Run 'isort CLEAR/ clear_htmx/' to fix." >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ Import sorting is correct" >> $GITHUB_STEP_SUMMARY
        
    - name: Run flake8 linting
      run: |
        echo "## Flake8 Linting Results" >> $GITHUB_STEP_SUMMARY
        flake8 CLEAR/ clear_htmx/ --max-line-length=88 --extend-ignore=E203,W503 --statistics || {
          echo "❌ Linting issues found" >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ No linting issues found" >> $GITHUB_STEP_SUMMARY
        
    - name: Run pylint analysis
      run: |
        echo "## Pylint Analysis Results" >> $GITHUB_STEP_SUMMARY
        pylint CLEAR/ --load-plugins=pylint_django --django-settings-module=clear_htmx.settings --exit-zero || true
        echo "📊 Pylint analysis completed (see logs above)" >> $GITHUB_STEP_SUMMARY
        
    - name: Check Django best practices
      run: |
        echo "## Django Best Practices Check" >> $GITHUB_STEP_SUMMARY
        python manage.py check --deploy || {
          echo "❌ Django deployment checks failed" >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ Django deployment checks passed" >> $GITHUB_STEP_SUMMARY
        
    - name: Generate code quality summary
      if: always()
      run: |
        echo "## Code Quality Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Black formatting: ✅ Checked" >> $GITHUB_STEP_SUMMARY
        echo "- Import sorting: ✅ Checked" >> $GITHUB_STEP_SUMMARY
        echo "- Flake8 linting: ✅ Checked" >> $GITHUB_STEP_SUMMARY
        echo "- Pylint analysis: ✅ Completed" >> $GITHUB_STEP_SUMMARY
        echo "- Django checks: ✅ Passed" >> $GITHUB_STEP_SUMMARY

  pre-commit-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install pre-commit
      run: |
        python -m pip install --upgrade pip
        pip install pre-commit
        
    - name: Run pre-commit hooks
      run: |
        pre-commit run --all-files || {
          echo "❌ Pre-commit hooks failed. Run 'pre-commit run --all-files' locally to fix." >> $GITHUB_STEP_SUMMARY
          exit 1
        }
        echo "✅ All pre-commit hooks passed" >> $GITHUB_STEP_SUMMARY
