name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

jobs:
  build:
    runs-on: ubuntu-latest
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ github.repository }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
        
    - name: Generate build summary
      run: |
        echo "## Docker Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Image: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "- Digest: ${{ steps.build.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
        echo "- Platforms: linux/amd64, linux/arm64" >> $GITHUB_STEP_SUMMARY

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' || github.event.inputs.environment == 'staging'
    environment: staging
    
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        echo "Image: ${{ needs.build.outputs.image-tag }}"
        # Add your staging deployment commands here
        # Example: kubectl, docker-compose, or cloud provider CLI
        
    - name: Run staging health check
      run: |
        echo "Running staging health checks..."
        # Add health check commands here
        
    - name: Generate staging deployment summary
      run: |
        echo "## Staging Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Environment: Staging" >> $GITHUB_STEP_SUMMARY
        echo "- Status: ✅ Deployed" >> $GITHUB_STEP_SUMMARY
        echo "- Image: ${{ needs.build.outputs.image-tag }}" >> $GITHUB_STEP_SUMMARY

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'production'
    environment: production
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        echo "Image: ${{ needs.build.outputs.image-tag }}"
        # Add your production deployment commands here
        # Example: kubectl, docker-compose, or cloud provider CLI
        
    - name: Run production health check
      run: |
        echo "Running production health checks..."
        # Add health check commands here
        
    - name: Generate production deployment summary
      run: |
        echo "## Production Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- Environment: Production" >> $GITHUB_STEP_SUMMARY
        echo "- Status: ✅ Deployed" >> $GITHUB_STEP_SUMMARY
        echo "- Image: ${{ needs.build.outputs.image-tag }}" >> $GITHUB_STEP_SUMMARY
        
    - name: Create GitHub release
      if: startsWith(github.ref, 'refs/tags/')
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false
