# <PERSON><PERSON><PERSON> ignore file for CLEAR Django project
# This file specifies patterns for files and directories that <PERSON><PERSON><PERSON> should ignore

# Python cache and compiled files
**/__pycache__/
**/*.pyc
**/*.pyo
**/*.pyd
**/.Python
**/build/
**/develop-eggs/
**/dist/
**/downloads/
**/eggs/
**/.eggs/
**/lib/
**/lib64/
**/parts/
**/sdist/
**/var/
**/wheels/
**/*.egg-info/
**/.installed.cfg
**/*.egg

# Django specific
**/migrations/
**/staticfiles/
**/media/
**/logs/
**/db.sqlite3
**/dev_db.sqlite3

# Project-specific temporary and generated files
**/fix_*.py
**/check_*.py
**/comprehensive_*.py
**/configure_*.py
**/run_all_*.py
**/final_*.py
**/phase_*.json
**/test_results*.json
**/clear_analysis.log

# Development and testing artifacts
**/coty-playground/
**/source-repo/
**/refactor_migration/
**/reports/
**/scripts/__pycache__/

# Node.js (if any frontend tools are used)
**/node_modules/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# IDE and editor files
**/.vscode/
**/.idea/
**/*.swp
**/*.swo
**/*~

# OS generated files
**/.DS_Store
**/.DS_Store?
**/._*
**/.Spotlight-V100
**/.Trashes
**/ehthumbs.db
**/Thumbs.db

# Virtual environments
**/venv/
**/env/
**/ENV/
**/env.bak/
**/venv.bak/
**/.conda/

# Documentation builds
**/docs/_build/
**/docs/build/

# Jupyter Notebook
**/.ipynb_checkpoints

# pytest
**/.pytest_cache/
**/.coverage
**/htmlcov/

# mypy
**/.mypy_cache/
**/.dmypy.json
**/dmypy.json

# Pyre type checker
**/.pyre/

# Backup files
**/*.backup
**/*.bak
**/*~

# Temporary files
**/tmp/
**/temp/
**/.tmp/

# Large data files that don't need analysis
**/data/
**/datasets/
**/uploads/

# Configuration files that might contain sensitive data
**/.env
**/.env.local
**/.env.*.local
**/secrets.json
**/local_settings.py
