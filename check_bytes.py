with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py', 'rb') as f:
    content = f.read()

# Look for line 5378 and examine the bytes
lines = content.split(b'\n')
line_5378 = lines[5377]  # 0-indexed
print('Line 5378 bytes:', repr(line_5378))
print('Line 5378 decoded:', line_5378.decode('utf-8', errors='replace'))

# Check for any non-standard quote characters
for i, byte in enumerate(line_5378):
    char = chr(byte) if 32 <= byte <= 126 else f'\\x{byte:02x}'
    print(f'  {i:2d}: {byte:3d} {char}')
