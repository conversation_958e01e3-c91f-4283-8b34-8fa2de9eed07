# Let's manually check the quote balance starting from around line 5350

with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py', 'r') as f:
    lines = f.readlines()

# Start checking from line 5350 onward
start_line = 5350
quote_count = 0
in_docstring = False

print("Checking triple quotes from line", start_line)

for i in range(start_line-1, min(len(lines), start_line + 50)):
    line = lines[i]
    line_num = i + 1
    
    # Count triple quotes in this line
    triple_quotes = line.count('"""')
    
    if triple_quotes > 0:
        print(f"Line {line_num}: {triple_quotes} triple quotes -> {repr(line.strip())}")
        
        # Track docstring state
        for _ in range(triple_quotes):
            in_docstring = not in_docstring
            
        print(f"  After this line, in_docstring = {in_docstring}")

print(f"\nFinal state: in_docstring = {in_docstring}")

# If we ended in a docstring, that's the problem
if in_docstring:
    print("FOUND THE ISSUE: There's an unclosed docstring!")