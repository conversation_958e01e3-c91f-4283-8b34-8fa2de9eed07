#!/usr/bin/env python3
"""
Comprehensive Frontend UI/UX Review & Enhancement Test System

This system automatically discovers ALL frontend pages in a Django application and performs
comprehensive UI/UX analysis following the systematic methodology outlined in the comprehensive
UI/UX review prompt. It's designed as a reusable test system for future frontend reviews.

COMPREHENSIVE METHODOLOGY:
1. Dynamic page discovery across entire application
2. Multi-resolution screenshot capture (temporary analysis files)
3. Mathematical scoring system with weighted calculations
4. Extensive internet research for 20+ comparable examples per page
5. Relevance scoring and qualification of examples (>=7.0 threshold)
6. Detailed comparative analysis and improvement prioritization
7. HTMX/Bootstrap/Alpine.js implementation recommendations
8. Performance impact measurement and final scoring
9. Complete cleanup of temporary files

Features:
- Automatic discovery of ALL pages (templates, URLs, dynamic routes)
- Comprehensive screenshot capture across device types
- Internet scraping for comparable UI/UX examples (NOTE: Scraping search results/sites may violate TOS)
- Mathematical scoring with 40/30/30 weighted system (NOTE: Scoring logic is simulated in this version)
- Example relevance scoring with structural + quality assessment (NOTE: Scoring logic is simulated)
- Priority-based improvement recommendations (NOTE: Logic is simulated)
- Technology stack compliance checking (NOTE: Logic is simulated)
- Complete progress tracking and cleanup

Usage:
    python comprehensive_frontend_reviewer.py                    # Full comprehensive review (default)
    python comprehensive_frontend_reviewer.py --discover-all     # Discovery phase only
    python comprehensive_frontend_reviewer.py --quick-test       # Quick test (5 pages)
    python comprehensive_frontend_reviewer.py --pages=10         # Full review, limit to 10 pages
    python comprehensive_frontend_reviewer.py --no-cleanup       # Do not clean up temporary files after run
"""

import os
import sys
import django
import json
import time
import requests
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any, Set
from dataclasses import dataclass, asdict, field
from urllib.parse import urljoin, urlparse, parse_qs
import re
import logging
import tempfile
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
import sqlite3 # Not used in the provided code, kept for completeness based on imports
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
import argparse
from bs4 import BeautifulSoup
import hashlib # Not used
from PIL import Image, ImageDraw, ImageFont # Not used for actual analysis here
import numpy as np # Not used
from sklearn.feature_extraction.text import TfidfVectorizer # Not used
from sklearn.metrics.pairwise import cosine_similarity # Not used

# Setup Django
# NOTE: Ensure 'your_project.settings' matches your actual Django project settings module
# This needs to be called before accessing any Django models, URL resolvers, etc.
try:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'your_project.settings')
    django.setup()
    # Import Django components after setup if needed, e.g.:
    # from django.urls import get_resolver
    # from django.template.loaders.app_directories import Loader as AppDirectoriesLoader
    # from django.template import TemplateDoesNotExist, engines
except ImportError as e:
    print(f"Error setting up Django: {e}")
    print("Please ensure DJANGO_SETTINGS_MODULE is set correctly and Django is installed.")
    sys.exit(1)
except Exception as e:
    print(f"An unexpected error occurred during Django setup: {e}")
    sys.exit(1)

# Configure comprehensive logging
# Log file name includes timestamp for unique runs
log_filename = f'comprehensive_ui_review_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stdout) # Also log to console
    ]
)
logger = logging.getLogger(__name__)

# Dataclass definitions remain largely the same, adding default fields where appropriate
@dataclass
class DiscoveredPage:
    """Comprehensive page discovery data"""
    template_path: str = ""
    url_pattern: str = ""
    actual_url: str = ""
    page_name: str = "Untitled Page"
    page_type: str = "UNKNOWN"
    section_category: str = "GENERAL"
    functional_keywords: List[str] = field(default_factory=list)
    estimated_complexity: int = 5 # Default complexity level
    requires_auth: bool = False
    has_dynamic_content: bool = False
    has_forms: bool = False
    has_tables: bool = False
    has_modals: bool = False
    responsive_priority: int = 5 # Default priority
    last_modified: datetime = field(default_factory=datetime.now)
    discovery_method: str = "UNKNOWN"

@dataclass
class ComprehensiveScreenshot:
    """Screenshot with analysis metadata"""
    device_type: str
    resolution: Tuple[int, int]
    file_path: str = ""
    file_size: int = 0
    capture_timestamp: datetime = field(default_factory=datetime.now)
    page_load_time: float = 0.0
    dom_elements_count: int = 0
    has_errors: bool = False
    error_details: Optional[str] = None
    viewport_analysis: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DetailedScoring:
    """Comprehensive scoring with detailed breakdowns"""
    # Structural Assessment (40% weight)
    layout_organization: int = 5          # 1-10: logical flow, clear hierarchy
    navigation_clarity: int = 5           # 1-10: intuitive, consistent
    content_structure: int = 5            # 1-10: readable, scannable, organized
    responsive_design: int = 5            # 1-10: works across device sizes

    # Visual Design Assessment (30% weight)
    visual_hierarchy: int = 5             # 1-10: clear importance levels
    color_usage: int = 5                  # 1-10: consistent, accessible, purposeful
    typography: int = 5                   # 1-10: readable, consistent, appropriate
    spacing_alignment: int = 5            # 1-10: clean, balanced, professional

    # User Experience Assessment (30% weight)
    ease_of_use: int = 5                  # 1-10: intuitive, efficient workflows
    loading_states: int = 5               # 1-10: clear feedback, no confusion
    error_handling: int = 5               # 1-10: helpful, clear, recoverable
    accessibility: int = 5                # 1-10: keyboard nav, screen readers, contrast

    # Calculated scores (will be calculated based on above scores)
    structural_weighted: float = 0.0
    visual_weighted: float = 0.0
    ux_weighted: float = 0.0
    total_score: float = 0.0
    scoring_rationale: str = "Initial simulated scoring." # Placeholder
    improvement_areas: List[str] = field(default_factory=list) # Placeholder

@dataclass
class ExampleResearchResult:
    """Comprehensive example research with relevance scoring"""
    example_number: int
    source: str = "UNKNOWN"
    source_url: str = ""
    image_url: str = ""
    local_image_path: str = "" # Local path after downloading
    description: str = ""

    # Structural & Contextual Alignment (60% weight)
    functional_similarity: int = 5        # 1-10: performs same/similar tasks
    user_flow_alignment: int = 5          # 1-10: matches app's intended workflow
    information_architecture: int = 5     # 1-10: similar data organization needs
    interaction_patterns: int = 5         # 1-10: compatible with HTMX approach
    complexity_level: int = 5             # 1-10: appropriate for use case

    # UI/UX Quality Assessment (40% weight)
    visual_design_quality: int = 5        # 1-10: professional, modern, polished
    layout_effectiveness: int = 5         # 1-10: clear, organized, efficient
    user_experience_flow: int = 5         # 1-10: intuitive, smooth, logical
    accessibility_considerations: int = 5 # 1-10: inclusive design principles

    # Calculated scores (will be calculated based on above scores)
    structural_alignment_score: float = 0.0
    quality_assessment_score: float = 0.0
    relevance_score: float = 0.0            # (structural × 0.6) + (quality × 0.4)
    qualified: bool = False                   # Score >= 7.0

    # Analysis metadata
    search_query: str = ""
    discovery_timestamp: datetime = field(default_factory=datetime.now)
    analysis_notes: str = ""

@dataclass
class ImprovementSuggestion:
    """Mathematical prioritization of improvements"""
    category: str                     # STRUCTURAL, VISUAL, USER_EXPERIENCE, TECHNICAL
    title: str
    description: str
    source_example: Optional[ExampleResearchResult] = None

    # Priority calculation factors (scored 1-10) - Simulated values
    ux_impact: int = 5                    # 30% weight: How much it improves UX
    business_value: int = 5               # 25% weight: ROI and business benefit
    implementation_feasibility: int = 5   # 20% weight: Realistic with tech stack
    maintenance_benefit: int = 5          # 15% weight: Long-term code quality
    low_effort_bonus: int = 5             # 10% weight: Quick wins bonus

    # Technology compliance - Simulated values
    htmx_bootstrap_compatible: bool = True
    requires_alpine_js: bool = False
    hypermedia_compliant: bool = True

    # Calculated priority (will be calculated based on above scores)
    priority_score: float = 0.0
    priority_level: str = "LOW"              # HIGH, MEDIUM, LOW, DISCARD
    estimated_time_minutes: int = 60 # Default estimate

class ComprehensiveFrontendReviewer:
    """
    Comprehensive Frontend UI/UX Review & Enhancement System

    This system performs exhaustive analysis of ALL frontend pages following
    the systematic methodology from the comprehensive UI/UX review prompt.
    NOTE: This version includes placeholder/simulated logic for core analysis steps.
    """

    def __init__(self,
                 base_url: str = "http://localhost:8000",
                 django_project_path: str = ".",
                 temp_analysis_dir: Optional[str] = None,
                 no_cleanup: bool = False):

        self.base_url = base_url
        # Ensure project path is an absolute path for reliable scanning
        self.django_project_path = Path(django_project_path).resolve()

        # Create temporary analysis directory
        if temp_analysis_dir:
            self.temp_dir = Path(temp_analysis_dir).resolve()
            # Explicitly create the directory if it doesn't exist
            try:
                 self.temp_dir.mkdir(parents=True, exist_ok=True)
                 self.cleanup_temp = False # Don't clean up user-provided dir
            except OSError as e:
                 logger.error(f"Failed to create specified temp directory {self.temp_dir}: {e}")
                 # Fallback to system temp dir if user-provided one fails
                 self.temp_dir = Path(tempfile.mkdtemp(prefix="ui_ux_review_"))
                 self.cleanup_temp = True
                 logger.info(f"Falling back to system temp directory: {self.temp_dir}")

        else:
            self.temp_dir = Path(tempfile.mkdtemp(prefix="ui_ux_review_"))
            self.cleanup_temp = True

        # Allow disabling cleanup via flag, even if using default temp dir
        if no_cleanup:
            self.cleanup_temp = False

        # Analysis subdirectories (all temporary)
        self.screenshots_dir = self.temp_dir / "screenshots"
        self.examples_dir = self.temp_dir / "examples"
        self.analysis_dir = self.temp_dir / "analysis"
        self.reports_dir = self.temp_dir / "reports"

        # Create all necessary subdirectories
        try:
            for dir_path in [self.screenshots_dir, self.examples_dir,
                            self.analysis_dir, self.reports_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)
        except OSError as e:
            logger.critical(f"Failed to create analysis subdirectories under {self.temp_dir}: {e}")
            # Attempt to clean up the base temp dir if creation failed critically
            if self.cleanup_temp:
                 self.cleanup_temporary_files()
            raise # Re-raise the exception

        logger.info(f"🗂️  Analysis directory: {self.temp_dir} (Cleanup: {'Yes' if self.cleanup_temp else 'No'})")

        # WebDriver configuration
        # Configure options for robustness and headless execution
        self.chrome_options = Options()
        self.chrome_options.add_argument("--headless")
        self.chrome_options.add_argument("--no-sandbox")
        self.chrome_options.add_argument("--disable-dev-shm-usage") # Overcomes limited resource problems
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--window-size=1920,1080") # Default window size
        self.chrome_options.add_argument("--disable-extensions")
        self.chrome_options.add_argument("--disable-plugins")
        self.chrome_options.add_argument("--log-level=3") # Suppress verbose logging from Chrome
        self.chrome_options.add_experimental_option('excludeSwitches', ['enable-logging']) # Suppress console logging

        # Multi-resolution testing configuration
        self.device_resolutions = {
            'desktop': (1920, 1080),
            'desktop_large': (2560, 1440),
            'tablet_landscape': (1024, 768),
            'tablet_portrait': (768, 1024),
            'mobile_large': (414, 896),
            'mobile_standard': (375, 667),
            'mobile_small': (320, 568)
        }

        # Scoring system configuration
        self.scoring_weights = {
            'structural': 0.4,
            'visual': 0.3,
            'ux': 0.3
        }

        # Example research configuration (NOTE: Search scraping is unreliable and may violate TOS)
        self.example_sources = [
            ("Dribbble", "site:dribbble.com"),
            ("Behance", "site:behance.net"),
            ("UI Movement", "site:uimovement.com"),
            ("Page Flows", "site:pageflows.com"),
            ("Mobbin", "site:mobbin.design"),
            ("Collect UI", "site:collectui.com"),
            ("UI Garage", "site:uigarage.net"),
            ("Land Book", "site:land-book.com")
        ]

        # Page type classification patterns
        self.page_type_patterns = {
            'AUTHENTICATION': ['login', 'auth', 'signin', 'signup', 'register', 'password', 'mfa', 'verify'],
            'DASHBOARD': ['dashboard', 'home', 'overview', 'main', 'summary'],
            'ADMIN': ['admin', 'management', 'control', 'system', 'settings', 'config'],
            'FORM': ['form', 'create', 'edit', 'update', 'add', 'new'],
            'LIST': ['list', 'index', 'browse', 'search', 'results'],
            'DETAIL': ['detail', 'view', 'show', 'profile', 'info'],
            'COMMUNICATION': ['message', 'chat', 'mail', 'notification', 'communication'],
            'ANALYTICS': ['analytics', 'reports', 'stats', 'metrics', 'chart'],
            'MAPPING': ['map', 'geo', 'location', 'gis'],
            'COMPONENT': ['component', 'partial', 'widget', 'element'] # Components might not be full pages
        }

        # Functional keywords for targeted example research
        self.functional_keywords = {
            'authentication': ['login interface', 'signin form', 'user authentication', 'password reset', 'MFA setup'],
            'dashboard': ['admin dashboard', 'analytics dashboard', 'project dashboard', 'user dashboard'],
            'forms': ['form design', 'input fields', 'form validation', 'multi-step form'],
            'tables': ['data table', 'responsive table', 'sortable table', 'data grid'],
            'navigation': ['navigation menu', 'sidebar nav', 'breadcrumbs', 'mobile menu'],
            'messaging': ['chat interface', 'messaging app', 'notification system', 'inbox design'],
            'analytics': ['chart design', 'data visualization', 'metrics dashboard', 'reporting interface']
        }

        # Progress tracking and results storage
        self.discovered_pages: List[DiscoveredPage] = []
        self.reviewed_pages_results: Dict[str, Dict] = {}
        self.total_examples_researched = 0
        self.total_improvements_suggested = 0
        self.session_start_time = datetime.now()


    def discover_all_pages(self) -> List[DiscoveredPage]:
        """
        PHASE 1: COMPREHENSIVE PAGE DISCOVERY

        Discovers ALL frontend pages across the entire Django application using
        multiple discovery methods as outlined in the comprehensive review prompt.
        NOTE: Implementation uses simple text parsing and crawling.
        A more robust approach would use Django's internal URL/template resolvers.
        """
        logger.info("🔍 PHASE 1: COMPREHENSIVE PAGE DISCOVERY")
        logger.info("=" * 60)

        discovered_pages = []
        discovery_methods = [
            self._discover_via_templates,
            self._discover_via_urls,
            # _discover_via_sitemap requires sitemap implementation
            # _discover_via_static_analysis requires AST parsing of views
            # For this version, only template and url scanning are included
            self._discover_via_crawling, # Crawling is included but limited
        ]

        # Execute discovery methods and collect results
        for method in discovery_methods:
            method_name = method.__name__
            try:
                logger.info(f"▶️ Running {method_name}...")
                method_pages = method()
                discovered_pages.extend(method_pages)
                logger.info(f"✅ {method_name} completed: {len(method_pages)} pages found")
            except Exception as e:
                # Log specific errors during discovery but don't stop the entire process
                logger.error(f"❌ {method_name} failed: {e}", exc_info=True)


        # Deduplicate and classify pages found across different methods
        unique_pages = self._deduplicate_pages(discovered_pages)
        # Classify and enrich page data based on combined information
        classified_pages = self._classify_and_enrich_pages(unique_pages)

        self.discovered_pages = classified_pages
        logger.info(f"🎯 DISCOVERY COMPLETE: {len(classified_pages)} unique pages discovered")

        # Optionally save raw discovery results for debugging/analysis
        # self._save_discovery_results(classified_pages) # Method not fully implemented

        return classified_pages

    # Placeholder/Helper methods for discovery - requires actual implementation
    # These methods need robust logic based on Django internals or advanced parsing
    def _discover_via_templates(self) -> List[DiscoveredPage]:
        """Discover pages by scanning template directories (Placeholder)"""
        logger.info("📁 Scanning template directories... (Simulated)")
        # In a real implementation, this would scan Django's template directories (settings.TEMPLATES, app dirs)
        # and parse template files to identify potential views or rendered pages.
        # This is complex as a template might be included, extended, or used for multiple URLs.
        # Example: Find base templates or templates likely used for full page renders.
        pages: List[DiscoveredPage] = []
        # Simulation: Find some dummy template paths based on common patterns
        template_root_dirs = [self.django_project_path / "templates"]
        # Add app-specific template directories
        template_root_dirs.extend(self.django_project_path.glob("*/templates"))

        potential_templates = []
        for root_dir in template_root_dirs:
             if root_dir.exists():
                 potential_templates.extend(root_dir.rglob("*.html"))

        for template_file in potential_templates:
             if self._is_valid_template(template_file):
                 relative_path = template_file.relative_to(self.django_project_path)
                 page_name = self._extract_page_name_from_template(template_file) or str(relative_path)
                 # Simulate some properties
                 page = DiscoveredPage(
                     template_path=str(relative_path),
                     page_name=page_name,
                     discovery_method="template_scan",
                     estimated_complexity=np.random.randint(1, 10),
                     responsive_priority=np.random.randint(1, 10),
                     last_modified=datetime.fromtimestamp(template_file.stat().st_mtime) if template_file.exists() else datetime.now(),
                     # Classification based on name/path (basic simulation)
                     page_type=self._classify_string_type(page_name, self.page_type_patterns),
                     section_category=self._classify_string_section(page_name),
                     functional_keywords=self._extract_keywords_from_string(page_name)
                 )
                 pages.append(page)

        return pages

    def _discover_via_urls(self) -> List[DiscoveredPage]:
        """Discover pages by analyzing URL patterns (Placeholder)"""
        logger.info("🔗 Analyzing URL patterns... (Simulated/Basic)")
        # In a real implementation, this should use `django.urls.get_resolver()`
        # to programmatically inspect URL patterns and map them to view functions.
        # Identifying the template used by a view requires analyzing the view function code (complex - static analysis).
        # For this simulation, we'll find urls.py files and extract patterns via regex (prone to errors).
        pages: List[DiscoveredPage] = []
        urls_files = list(self.django_project_path.rglob("urls.py"))

        for urls_file in urls_files:
             try:
                 with open(urls_file, 'r', encoding='utf-8') as f:
                     content = f.read()

                 # Basic regex to find `path('...', view, name='...')` or `re_path(r'...', view, name='...')`
                 # This is highly simplified and will miss many patterns.
                 # A robust solution needs AST parsing or using Django's resolver.
                 pattern_regex = r"(path|re_path)\(\s*['\"](?P<pattern>[^'\"]+)['\"],\s*[^)]+,\s*name=['\"](?P<name>[^'\"]+)['\"]"
                 matches = re.finditer(pattern_regex, content)

                 for match in matches:
                     pattern = match.group('pattern')
                     name = match.group('name')
                     actual_url = self._pattern_to_actual_url(pattern) # Attempt to reverse URL if possible (requires app context)
                     page_name = name or self._url_to_page_name(pattern)

                     # Simulate some properties
                     page = DiscoveredPage(
                         url_pattern=pattern,
                         actual_url=actual_url,
                         page_name=page_name,
                         discovery_method="url_analysis",
                         estimated_complexity=np.random.randint(1, 10),
                         responsive_priority=np.random.randint(1, 10),
                         last_modified=datetime.fromtimestamp(urls_file.stat().st_mtime) if urls_file.exists() else datetime.now(),
                         # Classification based on name/pattern (basic simulation)
                         page_type=self._classify_string_type(page_name + pattern, self.page_type_patterns),
                         section_category=self._classify_string_section(page_name + pattern),
                         functional_keywords=self._extract_keywords_from_string(page_name + pattern)
                     )
                     pages.append(page)

             except FileNotFoundError:
                  logger.warning(f"urls.py file not found at {urls_file}")
             except Exception as e:
                  logger.warning(f"Failed to parse {urls_file} for URLs: {e}")

        return pages

    def _discover_via_sitemap(self) -> List[DiscoveredPage]:
        """Discover pages from sitemap.xml (Placeholder)"""
        logger.info("🗺️  Scanning sitemap.xml... (Not implemented)")
        # In a real implementation, this would attempt to fetch sitemap.xml from base_url
        # and parse it to find listed URLs.
        return []

    def _discover_via_crawling(self) -> List[DiscoveredPage]:
        """Discover pages by crawling the live application (Limited Implementation)"""
        logger.info("🕷️  Crawling live application (limited to 100 internal URLs)...")

        pages = []
        visited_urls = set()
        urls_to_visit = [self.base_url] # Start crawling from the base URL

        driver = None
        try:
            # Initialize WebDriver
            driver = webdriver.Chrome(options=self.chrome_options)

            # Use a queue for URLs to visit
            from collections import deque
            url_queue = deque([self.base_url])
            visited_urls.add(self.base_url) # Mark starting URL as visited

            crawl_limit = 100 # Limit the number of pages crawled to avoid infinite loops/long runtimes
            pages_crawled_count = 0

            while url_queue and pages_crawled_count < crawl_limit:
                current_url = url_queue.popleft()

                # Skip external URLs early
                if not self._is_internal_url(current_url):
                    continue

                logger.info(f"     Crawling: {current_url}")

                try:
                    driver.get(current_url)
                    # Wait for body element to be present, indicating page structure is loaded
                    WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    pages_crawled_count += 1

                    # Extract page information
                    page_title = driver.title
                    page_source = driver.page_source # Get the rendered HTML source

                    # Create a DiscoveredPage object from crawled data
                    parsed_url = urlparse(current_url)
                    page = DiscoveredPage(
                        actual_url=current_url,
                        url_pattern=parsed_url.path if parsed_url.path else '/', # Use path as a pattern hint
                        page_name=page_title or self._url_to_page_name(parsed_url.path),
                        discovery_method="live_crawling",
                        last_modified=datetime.now(), # Cannot determine actual last modified date via crawling easily
                        # Classify page characteristics based on rendered source (basic checks)
                        page_type=self._classify_crawled_page_type(page_source),
                        section_category=self._classify_crawled_section(page_source),
                        functional_keywords=self._extract_crawled_keywords(page_source),
                        estimated_complexity=self._estimate_crawled_complexity(page_source),
                        requires_auth=self._crawled_requires_auth(page_source), # Requires checking for login forms, etc.
                        has_dynamic_content=self._crawled_has_dynamic_content(page_source),
                        has_forms=bool(BeautifulSoup(page_source, 'html.parser').find('form')),
                        has_tables=bool(BeautifulSoup(page_source, 'html.parser').find('table')),
                        has_modals=bool(BeautifulSoup(page_source, 'html.parser').find(class_=re.compile(r'modal|dialog', re.IGNORECASE))),
                        responsive_priority=7, # Crawled pages are usually important user-facing pages
                    )
                    pages.append(page)

                    # Find additional internal links on the page to add to the queue
                    soup = BeautifulSoup(page_source, 'html.parser')
                    for link_tag in soup.find_all('a', href=True):
                        href = link_tag['href']
                        # Resolve relative URLs against the current page URL
                        full_url = urljoin(current_url, href)
                        # Normalize URL (remove fragments, sort query params) before checking visited set
                        normalized_url = self._normalize_url(full_url)

                        if self._is_internal_url(normalized_url) and normalized_url not in visited_urls:
                            visited_urls.add(normalized_url)
                            url_queue.append(normalized_url)
                            logger.debug(f"       Found new internal URL: {normalized_url}")


                except TimeoutException:
                    logger.warning(f"     ⚠️  Timeout crawling {current_url}")
                except WebDriverException as e:
                    logger.warning(f"     ⚠️  WebDriver error crawling {current_url}: {e}")
                except Exception as e:
                    # Catch other potential errors during processing the page
                    logger.warning(f"     ⚠️  Error processing {current_url}: {e}")

        except Exception as e:
             # Catch errors during WebDriver initialization or critical issues
            logger.error(f"❌ Critical error during crawling setup or execution: {e}", exc_info=True)
        finally:
            # Ensure the browser is closed even if errors occur
            if driver:
                driver.quit()
                logger.info("     WebDriver quit.")

        logger.info(f"🕷️  Crawling finished. Discovered {len(pages)} pages via crawling.")
        return pages


    def _discover_via_static_analysis(self) -> List[DiscoveredPage]:
        """Discover pages by static analysis of view code (Placeholder)"""
        logger.info("⚙️  Scanning view code... (Not implemented)")
        # In a real implementation, this would use Python's `ast` module
        # to parse views.py files, identify view functions/classes,
        # determine if they render templates (e.g., `render()`, `TemplateView`),
        # and extract template names and potentially URL names. This is complex.
        return []

    def _deduplicate_pages(self, pages: List[DiscoveredPage]) -> List[DiscoveredPage]:
        """Deduplicate pages based on a unique key (e.g., actual_url or template_path/url_pattern combo)"""
        logger.info("🧹 Deduplicating discovered pages...")
        unique_keys = set()
        unique_pages = []

        for page in pages:
            # Create a unique key. Prioritize actual_url if available, otherwise use template_path + url_pattern
            key = page.actual_url if page.actual_url else f"{page.template_path}_{page.url_pattern}"
            if key and key not in unique_keys:
                unique_keys.add(key)
                unique_pages.append(page)
            elif not key:
                 logger.debug(f"Skipping page with no unique key: {page}")

        logger.info(f"🧹 Deduplication complete. Reduced from {len(pages)} to {len(unique_pages)} pages.")
        return unique_pages

    def _classify_and_enrich_pages(self, pages: List[DiscoveredPage]) -> List[DiscoveredPage]:
        """Further classify and enrich page data after initial discovery (Placeholder)"""
        logger.info("📚 Classifying and enriching page data... (Simulated)")
        # This step could involve:
        # - Using Django's URL resolver to confirm URL patterns and potentially map them to views/templates
        # - Performing lightweight static analysis to check views for authentication decorators, forms used, etc.
        # - Grouping pages into sections (e.g., User Account, Product Catalog, Admin Panel)
        # - Estimating complexity more accurately based on template size, view logic
        # - Determining responsive priority based on page type or configuration
        enriched_pages: List[DiscoveredPage] = []
        for page in pages:
            # Simulate enrichment
            page.page_type = page.page_type if page.page_type != "UNKNOWN" else self._classify_string_type(page.page_name + page.actual_url + page.url_pattern, self.page_type_patterns)
            page.section_category = page.section_category if page.section_category != "GENERAL" else self._classify_string_section(page.page_name + page.actual_url)
            # Merge keywords
            page.functional_keywords.extend(self._extract_keywords_from_string(page.page_name + page.actual_url))
            page.functional_keywords = list(set(page.functional_keywords)) # Remove duplicates
            # Simulate authentication check if not already set
            if not page.requires_auth:
                 page.requires_auth = self._string_indicates_auth_required(page.page_name + page.url_pattern) # Basic simulation

            enriched_pages.append(page)

        logger.info("📚 Classification and enrichment complete.")
        return enriched_pages

    # --- Helper methods for classification and data extraction (Basic Simulation) ---
    # These need to be properly implemented based on parsing logic or Django internals
    def _is_valid_template(self, template_file: Path) -> bool:
        """Basic check if template file is likely a full page template"""
        # Avoid common partials, emails, errors, etc.
        exclude_patterns = ['email', 'error', 'partial', 'include', 'snippet', 'macros']
        return template_file.suffix == '.html' and not any(skip in template_file.name.lower() for skip in exclude_patterns)

    def _extract_page_name_from_template(self, template_file: Path) -> str:
        """Extract a potential page name from template path/name"""
        name = template_file.stem # Get filename without extension
        # Replace underscores/hyphens with spaces and capitalize words
        name = name.replace('_', ' ').replace('-', ' ').title()
        # Add parent directory name if it's not a top-level template
        if len(template_file.parts) > 1 and template_file.parts[-2] not in ['templates', self.django_project_path.name]:
             name = f"{template_file.parts[-2].title()} - {name}"
        return name

    def _template_to_url_pattern(self, relative_template_path: Path) -> str:
        """Simulate generating a URL pattern from a template path (Highly Naive)"""
        # This is a very rough guess. A real implementation would use URLConf analysis.
        parts = list(relative_template_path.parts)
        if parts and parts[0] == 'templates':
            parts = parts[1:] # Remove the base 'templates' part if it exists
        parts[-1] = relative_template_path.stem # Use stem for the last part
        # Join parts, possibly add index/home mapping
        pattern = '/'.join(parts).lower()
        if pattern == 'index' or pattern == 'home':
            return '/'
        if pattern.endswith('/index'):
             pattern = pattern[:-6] # Remove /index at the end

        # Add leading slash and trailing slash (common convention)
        pattern = '/' + pattern
        if not pattern.endswith('/') and pattern != '/':
             pattern += '/'
        return pattern

    def _extract_url_patterns(self, content: str) -> List[Tuple[str, Optional[str]]]:
        """Basic extraction of URL patterns and names from urls.py content (Highly Naive/Regex-based)"""
        # This is a placeholder. A real implementation is complex and needs AST parsing.
        patterns = []
        # Example regex: finds path() or re_path() calls with a pattern string
        # Doesn't handle view arguments or complex includes well.
        regex = r"(?:path|re_path)\(\s*r?['\"](?P<pattern>[^'\"]+)['\"],\s*[^)]+\)"
        for match in re.finditer(regex, content):
            pattern = match.group('pattern')
            # Try to find a 'name=' argument if available in the call
            name_match = re.search(r"name=['\"](?P<name>[^'\"]+)['\"]", match.group(0))
            name = name_match.group('name') if name_match else None
            patterns.append((pattern, name))
        return patterns

    def _pattern_to_actual_url(self, pattern: str) -> str:
        """Simulate converting a URL pattern to an actual URL (Naive)"""
        # This is a placeholder. Using Django's `reverse` is the correct way,
        # but requires knowing view arguments and app namespaces.
        # For simulation, replace common parameter patterns with dummies.
        url = pattern
        # Replace common path converters
        url = re.sub(r'<int:\w+>', '1', url)
        url = re.sub(r'<str:\w+>', 'test', url)
        url = re.sub(r'<slug:\w+>', 'test-slug', url)
        url = re.sub(r'<uuid:\w+>', '123e4567-e89b-12d3-a456-************', url)
        # Handle regex patterns (very difficult to reverse generally) - skip or use a default
        if url.startswith('^'):
             return "" # Cannot reliably guess actual URL from complex regex without `reverse` and args
        # Ensure it starts with base_url
        return urljoin(self.base_url, url)

    def _url_to_page_name(self, url_path: str) -> str:
        """Generate a page name from a URL path"""
        # Remove leading/trailing slashes, replace hyphens/underscores, title case
        name = url_path.strip('/').replace('-', ' ').replace('_', ' ').title()
        if not name:
            return "Home" # Default for root path
        return name

    def _is_internal_url(self, url: str) -> bool:
        """Check if a URL is internal to the application base_url"""
        if not url:
             return False
        # Check if the URL scheme and netloc match the base URL
        parsed_base = urlparse(self.base_url)
        parsed_url = urlparse(urljoin(self.base_url, url)) # Join to handle relative URLs

        # Consider URLs internal if they have no netloc or the netloc matches the base URL's
        # Also check if the scheme is http or https (avoid mailto, tel, etc.)
        is_same_domain = (not parsed_url.netloc or parsed_url.netloc == parsed_base.netloc)
        is_web_scheme = parsed_url.scheme in ('http', 'https')
        is_not_file_anchor = not parsed_url.path.endswith(('.zip', '.pdf', '.tar', '.gz')) # Avoid crawling static files if possible
        is_not_fragment_only = not (not parsed_url.path and not parsed_url.query and parsed_url.fragment) # Avoid just anchors on the same page

        return is_same_domain and is_web_scheme and is_not_file_anchor and is_not_fragment_only

    def _normalize_url(self, url: str) -> str:
        """Normalize URL for consistent comparison (remove fragments, sort query params)"""
        parsed = urlparse(urljoin(self.base_url, url))
        # Remove fragment
        parsed = parsed._replace(fragment='')
        # Sort query parameters
        query_params = parse_qs(parsed.query)
        sorted_query = '&'.join(f"{k}={','.join(sorted(v))}" for k, v in sorted(query_params.items()))
        parsed = parsed._replace(query=sorted_query)
        return parsed.geturl()

    def _classify_string_type(self, text: str, patterns: Dict[str, List[str]]) -> str:
        """Classify based on keywords in a string"""
        text_lower = text.lower()
        for page_type, keywords in patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                return page_type
        return "GENERAL"

    def _classify_string_section(self, text: str) -> str:
        """Basic classification of section based on path/name keywords"""
        text_lower = text.lower()
        if any(k in text_lower for k in ['admin', 'manage', 'dashboard', 'settings']):
            return "ADMIN/DASHBOARD"
        if any(k in text_lower for k in ['user', 'account', 'profile', 'settings']):
            return "USER ACCOUNT"
        if any(k in text_lower for k in ['product', 'item', 'catalog', 'shop', 'store']):
            return "PRODUCT/CATALOG"
        if any(k in text_lower for k in ['order', 'cart', 'checkout', 'purchase']):
            return "SALES/COMMERCE"
        return "GENERAL"

    def _extract_keywords_from_string(self, text: str) -> List[str]:
         """Extract functional keywords from string (Placeholder)"""
         # In a real scenario, this would use NLP or keyword matching against a list
         # For simulation, split words and filter common ones
         words = re.findall(r'\b\w+\b', text.lower())
         common_words = set(['the', 'a', 'is', 'in', 'and', 'or', 'of', 'for', 'to'])
         keywords = [word for word in words if word not in common_words and len(word) > 2]
         # Basic mapping to predefined functional keywords
         extracted = []
         for func_group, func_keywords in self.functional_keywords.items():
              if any(k in keywords for k in func_keywords):
                   extracted.append(func_group)
         return extracted

    def _estimate_complexity(self, template_file: Path) -> int:
        """Estimate complexity based on template size (Basic Simulation)"""
        try:
            size = template_file.stat().st_size
            if size < 1000: return 2
            if size < 5000: return 4
            if size < 15000: return 6
            if size < 50000: return 8
            return 10
        except FileNotFoundError:
            return 5 # Default if file not found

    def _requires_authentication(self, template_file: Path) -> bool:
        """Simulate checking if a template requires authentication (Naive)"""
        # In a real app, this depends on the view function decorator/mixin, not the template itself.
        # This is a placeholder assuming templates with 'account', 'profile', 'dashboard' might.
        return any(k in template_file.parts for k in ['account', 'profile', 'dashboard', 'admin'])

    def _has_dynamic_content(self, template_file: Path) -> bool:
        """Simulate checking for dynamic content (Naive)"""
        # In a real app, this depends on the view context and template tags/filters.
        # This placeholder assumes templates containing Django template tags like 'for', 'if' have dynamic content.
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            return any(tag in content for tag in ['{% for', '{% if', '{{ obj.', '{{ item.', '{{ form.'])
        except Exception:
            return True # Assume dynamic if reading fails

    def _has_forms(self, template_file: Path) -> bool:
         """Check for forms in a template file (Basic)"""
         try:
              with open(template_file, 'r', encoding='utf-8') as f:
                  content = f.read()
              return bool(re.search(r'<form', content, re.IGNORECASE))
         except Exception:
              return False

    def _has_tables(self, template_file: Path) -> bool:
         """Check for tables in a template file (Basic)"""
         try:
              with open(template_file, 'r', encoding='utf-8') as f:
                  content = f.read()
              return bool(re.search(r'<table', content, re.IGNORECASE))
         except Exception:
              return False

    def _has_modals(self, template_file: Path) -> bool:
         """Check for modals in a template file (Basic)"""
         try:
              with open(template_file, 'r', encoding='utf-8') as f:
                  content = f.read()
              # Look for common modal class names or structures
              return bool(re.search(r'modal|dialog|popup', content, re.IGNORECASE))
         except Exception:
              return False

    def _calculate_responsive_priority(self, template_file: Path) -> int:
        """Simulate responsive priority based on template name/path (Naive)"""
        # Higher priority for critical pages
        name = str(template_file).lower()
        if any(k in name for k in ['index', 'home', 'dashboard', 'login', 'product', 'detail', 'list']):
            return 8
        if any(k in name for k in ['form', 'edit', 'create', 'settings']):
            return 7
        return 5

    def _classify_crawled_page_type(self, html_source: str) -> str:
        """Classify page type based on crawled HTML source (Basic)"""
        # Look for keywords in title or body content
        soup = BeautifulSoup(html_source, 'html.parser')
        text = (soup.title.string if soup.title else '') + ' ' + soup.body.get_text() if soup.body else ''
        return self._classify_string_type(text, self.page_type_patterns)

    def _classify_crawled_section(self, html_source: str) -> str:
        """Classify section based on crawled HTML source (Basic)"""
        soup = BeautifulSoup(html_source, 'html.parser')
        text = (soup.title.string if soup.title else '') + ' ' + soup.body.get_text() if soup.body else ''
        return self._classify_string_section(text)

    def _extract_crawled_keywords(self, html_source: str) -> List[str]:
        """Extract keywords from crawled HTML source (Basic)"""
        soup = BeautifulSoup(html_source, 'html.parser')
        text = (soup.title.string if soup.title else '') + ' ' + soup.body.get_text() if soup.body else ''
        return self._extract_keywords_from_string(text)

    def _estimate_crawled_complexity(self, html_source: str) -> int:
        """Estimate complexity based on DOM size (Basic Simulation)"""
        soup = BeautifulSoup(html_source, 'html.parser')
        dom_elements = len(soup.find_all())
        if dom_elements < 100: return 2
        if dom_elements < 500: return 4
        if dom_elements < 1500: return 6
        if dom_elements < 3000: return 8
        return 10

    def _crawled_requires_auth(self, html_source: str) -> bool:
        """Simulate checking if crawled page requires auth (Basic)"""
        # Look for login form or redirect patterns (Naive)
        soup = BeautifulSoup(html_source, 'html.parser')
        if soup.select_one('form[action*="login"]'): return True
        # Could also check for specific meta tags or body classes
        return False

    def _crawled_has_dynamic_content(self, html_source: str) -> bool:
        """Simulate checking if crawled page has dynamic content (Naive)"""
        # This is hard from static HTML. Could look for signs of JS rendering,
        # specific libraries (Alpine, HTMX), or complex DOM structures.
        # Placeholder: Assume most crawled pages have some dynamic aspects.
        return True

    def comprehensive_page_review(self, pages: List[DiscoveredPage], limit: Optional[int] = None) -> Dict[str, Any]:
        """
        PHASE 2: COMPREHENSIVE PAGE REVIEW

        Performs systematic review of all discovered pages following the comprehensive
        methodology: screenshots, scoring, example research, comparative analysis,
        and improvement recommendations.
        NOTE: Core analysis steps are simulated/placeholder.
        """
        logger.info("🎯 PHASE 2: COMPREHENSIVE PAGE REVIEW")
        logger.info("=" * 60)

        pages_to_review = pages[:limit] if limit is not None else pages
        logger.info(f"📊 Reviewing {len(pages_to_review)} pages")

        review_results = {}

        # Consider using ThreadPoolExecutor here to review pages in parallel
        # with a limited number of workers (e.g., 4-8) depending on system resources
        # and WebDriver stability with concurrent instances.
        # For simplicity in this corrected code, we'll keep it sequential.

        for i, page in enumerate(pages_to_review, 1):
            page_identifier = page.template_path or page.url_pattern or page.actual_url
            logger.info(f"\n🔍 [{i}/{len(pages_to_review)}] Reviewing: {page.page_name}")
            logger.info(f"   Identifier: {page_identifier}")
            logger.info(f"   URL: {page.actual_url or 'N/A'}")
            logger.info(f"   Type: {page.page_type}, Section: {page.section_category}")


            page_review_start_time = time.time()

            try:
                # Step 1: Capture comprehensive screenshots
                # This step uses WebDriver and can be slow.
                screenshots = self._capture_comprehensive_screenshots(page)
                if not screenshots:
                    logger.warning(f"   ⚠️  No screenshots captured for {page.page_name}. Skipping further analysis for this page.")
                    review_results[page_identifier] = {
                         'page_info': asdict(page),
                         'screenshots': [],
                         'error': 'No screenshots captured',
                         'review_timestamp': datetime.now(),
                         'review_duration_seconds': time.time() - page_review_start_time
                    }
                    continue # Skip analysis if no screenshots

                # Step 2: Perform initial mathematical scoring (Simulated)
                # This involves analyzing screenshots, DOM, accessibility, performance metrics.
                initial_scoring = self._perform_comprehensive_scoring(page, screenshots)

                # Step 3: Extensive example research (20+ examples) (Simulated/Placeholder)
                # This step involves searching external sources and analyzing results.
                example_research = self._perform_extensive_example_research(page)

                # Step 4: Comparative analysis and improvement suggestions (Simulated)
                # This step analyzes the scores, example research, and page characteristics
                # to generate concrete, prioritized recommendations.
                improvements = self._generate_prioritized_improvements(page, example_research, initial_scoring)

                # Store comprehensive results for the page
                review_results[page_identifier] = {
                    'page_info': asdict(page), # Convert dataclass to dict for serialization
                    'screenshots': [asdict(s) for s in screenshots],
                    'initial_scoring': asdict(initial_scoring),
                    'example_research': [asdict(e) for e in example_research],
                    'improvements': [asdict(i) for i in improvements],
                    'review_timestamp': datetime.now(),
                    'review_duration_seconds': time.time() - page_review_start_time
                }

                logger.info(f"   ✅ Review complete - Score: {initial_scoring.total_score:.1f}/10")
                logger.info(f"   📈 {len(example_research)} examples researched, {len(improvements)} improvements suggested")

            except Exception as e:
                # Log specific errors during review but continue with the next page
                logger.error(f"   ❌ Review of {page.page_name} failed: {e}", exc_info=True)
                review_results[page_identifier] = {
                    'page_info': asdict(page),
                    'error': f"Review failed: {e}",
                    'review_timestamp': datetime.now(),
                    'review_duration_seconds': time.time() - page_review_start_time
                }

        # Calculate global statistics from collected results
        global_stats = self._calculate_global_statistics(review_results)

        final_results = {
            'session_info': {
                'start_time': self.session_start_time.isoformat(), # Use ISO format for JSON compatibility
                'end_time': datetime.now().isoformat(),
                'pages_reviewed_count': len(review_results),
                'total_pages_discovered': len(pages), # Total pages discovered, even if not reviewed
            },
            'page_reviews': review_results,
            'global_statistics': global_stats,
            # _verify_methodology_compliance is a placeholder
            # 'methodology_compliance': self._verify_methodology_compliance(review_results)
        }

        # Save comprehensive results to a JSON file
        self._save_comprehensive_results(final_results)

        return final_results

    def _capture_comprehensive_screenshots(self, page: DiscoveredPage) -> List[ComprehensiveScreenshot]:
        """Capture screenshots across all device resolutions with analysis"""
        # Ensure a valid URL is available
        page_url = page.actual_url or urljoin(self.base_url, page.url_pattern)
        if not page_url or not page_url.startswith(('http://', 'https://')):
            logger.warning(f"     ⚠️  Cannot capture screenshots: Invalid or missing URL for page '{page.page_name}': {page_url}")
            return []

        logger.info(f"   📸 Capturing comprehensive screenshots for {page_url}...")

        screenshots: List[ComprehensiveScreenshot] = []
        driver = None # Initialize driver as None

        try:
            # Initialize WebDriver for the current page's screenshots
            driver = webdriver.Chrome(options=self.chrome_options)

            # Navigate to page and measure initial load time
            start_time = time.time()
            driver.get(page_url)

            # Wait for the body element to be present. Could add more specific waits here.
            WebDriverWait(driver, 20).until( # Increased timeout slightly
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            load_time = time.time() - start_time
            logger.info(f"     Page loaded in {load_time:.2f} seconds.")

            # Capture screenshots for each resolution in parallel using ThreadPoolExecutor
            # WebDriver is NOT thread-safe, so this part must be sequential OR each thread gets its own driver (less efficient).
            # We will stick to sequential capture within this method for simplicity and safety.
            # If performance is critical, consider multiprocessing instead of multithreading,
            # with each process handling one resolution capture.

            for device_name, (width, height) in self.device_resolutions.items():
                try:
                    logger.debug(f"       Capturing {device_name} at {width}x{height}")
                    # Set window size - requires sufficient display/virtual display size if not headless
                    driver.set_window_size(width, height)
                    time.sleep(2)  # Give the browser time to adjust layout after resizing

                    # Analyze viewport characteristics (Placeholder)
                    viewport_analysis = self._analyze_viewport(driver, width, height)

                    # Generate screenshot filename and path
                    # Sanitize filename to avoid issues on different OS
                    sanitized_page_name = self._sanitize_filename(page.page_name)
                    screenshot_filename = f"{sanitized_page_name}_{device_name}_{width}x{height}.png"
                    screenshot_path = self.screenshots_dir / screenshot_filename

                    # Capture screenshot and save to the temporary directory
                    # Use the full path string for save_screenshot
                    driver.save_screenshot(str(screenshot_path))

                    # Get file info and basic page metrics
                    file_size = screenshot_path.stat().st_size if screenshot_path.exists() else 0
                    try:
                        # Count all elements in the DOM
                        dom_elements = len(driver.find_elements(By.XPATH, "//*"))
                    except WebDriverException:
                        dom_elements = -1 # Indicate failure to count

                    # Check for console errors or JS errors (Requires executing script in the browser)
                    # Example: logs = driver.get_log("browser")
                    # This requires enabling logging capabilities in options and handling the log output.
                    # Skipping for simplicity in this corrected code.
                    has_errors = False
                    error_details = None

                    # Create ComprehensiveScreenshot object
                    screenshot = ComprehensiveScreenshot(
                        device_type=device_name,
                        resolution=(width, height),
                        file_path=str(screenshot_path),
                        file_size=file_size,
                        capture_timestamp=datetime.now(),
                        page_load_time=load_time, # Note: This is the *initial* page load time, not per resolution
                        dom_elements_count=dom_elements,
                        has_errors=has_errors,
                        error_details=error_details,
                        viewport_analysis=viewport_analysis
                    )
                    screenshots.append(screenshot)
                    logger.debug(f"       Captured {device_name} screenshot.")

                except WebDriverException as e:
                    logger.warning(f"     ⚠️  Failed to capture {device_name} screenshot for {page.page_name}: {e}")
                    # Add a failed screenshot entry
                    screenshots.append(ComprehensiveScreenshot(
                         device_type=device_name,
                         resolution=(width, height),
                         has_errors=True,
                         error_details=f"WebDriver failed: {e}",
                         viewport_analysis={}
                    ))
                except Exception as e:
                    logger.warning(f"     ⚠️  An unexpected error occurred during {device_name} screenshot capture for {page.page_name}: {e}")
                    # Add a failed screenshot entry
                    screenshots.append(ComprehensiveScreenshot(
                         device_type=device_name,
                         resolution=(width, height),
                         has_errors=True,
                         error_details=f"Unexpected error: {e}",
                         viewport_analysis={}
                    ))


        except WebDriverException as e:
            logger.error(f"   ❌ WebDriver initialization or critical error for page {page.page_name}: {e}")
        except Exception as e:
             logger.error(f"   ❌ An unexpected error occurred during screenshot setup for page {page.page_name}: {e}")
        finally:
            # Always quit the driver instance specific to this page's capture
            if driver:
                driver.quit()
                logger.debug("     WebDriver quit after screenshots.")

        logger.info(f"   📸 Finished capturing screenshots. Captured {len(screenshots)} out of {len(self.device_resolutions)} attempts.")
        return screenshots

    # --- Analysis and Scoring Methods (Placeholder/Simulation) ---
    # These methods represent the core analysis engine and need actual implementation.
    def _analyze_screenshots_for_scoring(self, screenshots: List[ComprehensiveScreenshot]) -> Dict[str, Any]:
        """Analyze screenshots visually and technically for scoring data (Placeholder)"""
        logger.debug("     Analyzing screenshots for scoring... (Simulated)")
        # In a real implementation, this would involve:
        # - Using image analysis libraries (PIL, OpenCV) to check for visual elements, layout consistency.
        # - Integrating with accessibility testing tools (e.g., Axe-core via Selenium) to find issues.
        # - Analyzing the DOM structure from the browser (e.g., `driver.execute_script("return document.body.innerHTML")`).
        # - Checking CSS properties for responsive patterns, typography, color contrast.
        # - Performance metrics analysis (already have load time, could add more).
        # - Detecting common UI patterns (forms, tables, navs).
        analysis_data: Dict[str, Any] = {
            'layout_issues_detected': np.random.randint(0, 5),
            'accessibility_violations': np.random.randint(0, 10),
            'visual_consistency_score': np.random.uniform(5.0, 9.5),
            'responsive_break_points_handled': np.random.randint(2, len(self.device_resolutions)),
            'forms_found': any(s.viewport_analysis.get('has_forms', False) for s in screenshots), # Needs actual analysis in viewport
            'tables_found': any(s.viewport_analysis.get('has_tables', False) for s in screenshots), # Needs actual analysis in viewport
            'avg_load_time': np.mean([s.page_load_time for s in screenshots if s.page_load_time > 0]) if screenshots else 0,
        }
        # Simulate viewport analysis results if they weren't populated
        if not screenshots or not screenshots[0].viewport_analysis:
             analysis_data['forms_found'] = True if np.random.random() > 0.5 else False
             analysis_data['tables_found'] = True if np.random.random() > 0.5 else False

        logger.debug("     Screenshot analysis simulated.")
        return analysis_data

    def _perform_comprehensive_scoring(self, page: DiscoveredPage, screenshots: List[ComprehensiveScreenshot]) -> DetailedScoring:
        """Perform comprehensive mathematical scoring using the weighted system (Simulated)"""
        logger.info(f"   📊 Performing comprehensive scoring... (Simulated)")

        # Analyze screenshots and page data to get inputs for scoring (using the simulated helper)
        automated_analysis = self._analyze_screenshots_for_scoring(screenshots)

        # Simulate detailed scores based on analysis and page characteristics
        # This logic is highly complex in reality and requires careful design
        # based on thresholds, weighted criteria derived from analysis data, etc.
        layout_score = max(1, min(10, 10 - automated_analysis.get('layout_issues_detected', 0) * 2)) # Higher issues -> lower score
        accessibility_score = max(1, min(10, 10 - automated_analysis.get('accessibility_violations', 0))) # Higher violations -> lower score
        visual_score = automated_analysis.get('visual_consistency_score', 5.0)
        responsive_score = min(10, automated_analysis.get('responsive_break_points_handled', 0) * (10 / len(self.device_resolutions))) # Score based on breakpoints handled

        # Simulate other scores, potentially influenced by page type, complexity, etc.
        scoring_instance = DetailedScoring(
            layout_organization=int(layout_score),
            navigation_clarity=int(np.random.uniform(max(5, layout_score-2), 10)), # Nav score related to layout
            content_structure=int(np.random.uniform(max(5, layout_score-1), 10)), # Content score related to layout
            responsive_design=int(responsive_score),

            visual_hierarchy=int(np.random.uniform(max(5, visual_score-1), 10)), # Visual scores related to overall visual score
            color_usage=int(np.random.uniform(max(5, visual_score-1), 10)),
            typography=int(np.random.uniform(max(5, visual_score-1), 10)),
            spacing_alignment=int(np.random.uniform(max(5, visual_score-1), 10)),

            ease_of_use=int(np.random.uniform(max(5, accessibility_score-2), 10)), # UX scores related to accessibility, page type
            loading_states=int(max(1, min(10, 10 - int(automated_analysis.get('avg_load_time', 5)/2)))), # Higher load time -> lower score
            error_handling=int(np.random.uniform(5, 10)), # Not directly measurable from screenshots easily
            accessibility=int(accessibility_score),
        )

        # Calculate weighted scores
        scoring_instance.structural_weighted = (
            scoring_instance.layout_organization +
            scoring_instance.navigation_clarity +
            scoring_instance.content_structure +
            scoring_instance.responsive_design
        ) / 4 * self.scoring_weights['structural']

        scoring_instance.visual_weighted = (
            scoring_instance.visual_hierarchy +
            scoring_instance.color_usage +
            scoring_instance.typography +
            scoring_instance.spacing_alignment
        ) / 4 * self.scoring_weights['visual']

        scoring_instance.ux_weighted = (
            scoring_instance.ease_of_use +
            scoring_instance.loading_states +
            scoring_instance.error_handling +
            scoring_instance.accessibility
        ) / 4 * self.scoring_weights['ux']

        scoring_instance.total_score = (
            scoring_instance.structural_weighted +
            scoring_instance.visual_weighted +
            scoring_instance.ux_weighted
        )

        # Simulate improvement areas based on low scores
        improvement_areas = []
        if scoring_instance.layout_organization < 7: improvement_areas.append("Layout & Organization")
        if scoring_instance.responsive_design < 7: improvement_areas.append("Responsive Design")
        if scoring_instance.visual_hierarchy < 7 or scoring_instance.color_usage < 7 or scoring_instance.typography < 7: improvement_areas.append("Visual Design")
        if scoring_instance.ease_of_use < 7: improvement_areas.append("Ease of Use")
        if scoring_instance.accessibility < 7: improvement_areas.append("Accessibility")
        scoring_instance.improvement_areas = list(set(improvement_areas)) # Remove duplicates

        # Simulate rationale
        scoring_instance.scoring_rationale = f"Simulated score based on automated analysis findings. Key areas for improvement: {', '.join(scoring_instance.improvement_areas) if improvement_areas else 'None identified'}"

        logger.info(f"   📊 Scoring complete: {scoring_instance.total_score:.1f}/10")
        return scoring_instance

    def _analyze_viewport(self, driver: webdriver.Chrome, width: int, height: int) -> Dict[str, Any]:
        """Analyze viewport details using browser scripts (Placeholder)"""
        logger.debug(f"     Analyzing viewport {width}x{height}... (Simulated)")
        # In a real implementation, this would use `driver.execute_script` to get:
        # - `window.innerWidth`, `window.innerHeight`
        # - `document.body.scrollWidth`, `document.body.scrollHeight` (to check for unexpected scrolling)
        # - Positions and sizes of key elements
        # - Check for elements extending horizontally beyond the viewport
        viewport_data: Dict[str, Any] = {
            'window_width': width,
            'window_height': height,
            'document_scroll_width': width, # Assume no horizontal scroll in simulation
            'document_scroll_height': height * np.random.uniform(1.0, 3.0), # Simulate vertical scroll
            'elements_overflowing_x': np.random.random() < 0.1, # Simulate occasional horizontal overflow
            # Add checks for forms, tables, modals if not already done via static analysis/crawling
            'has_forms_in_viewport': bool(driver.find_elements(By.TAG_NAME, 'form')), # Check in current DOM
            'has_tables_in_viewport': bool(driver.find_elements(By.TAG_NAME, 'table')),
            'has_modals_in_viewport': bool(driver.find_elements(By.CLASS_NAME, 'modal') or driver.find_elements(By.CLASS_NAME, 'dialog')),
        }
        return viewport_data

    def _perform_extensive_example_research(self, page: DiscoveredPage) -> List[ExampleResearchResult]:
        """
        Perform extensive internet research for 20+ comparable examples (Simulated/Placeholder)
        NOTE: The search engine scraping approach is unreliable and may violate TOS.
        A real implementation needs a different strategy (e.g., APIs, curated datasets).
        """
        logger.info(f"   🔎 Performing extensive example research... (Simulated)")

        examples: List[ExampleResearchResult] = []
        target_examples = 25
        qualified_threshold = 7.0 # Minimum relevance score to be qualified

        # Generate search queries based on page characteristics
        # Placeholder - needs actual logic to combine page type, section, keywords effectively
        base_query = f"{page.page_type} {page.section_category} UI UX design examples"
        queries = [base_query] + [f"{k} UI UX examples" for k in page.functional_keywords[:3]] # Add some keyword-specific queries

        example_counter = 0
        # Simulate finding examples without actual scraping
        for query in queries:
            if example_counter >= target_examples * 2: # Simulate finding more than needed before qualification
                break
            for source_name, _ in self.example_sources:
                if example_counter >= target_examples * 2:
                    break
                # Simulate finding a few examples per source/query combination
                num_simulated_per_source = np.random.randint(2, 6)
                for i in range(num_simulated_per_source):
                     example_counter += 1
                     relevance_score = np.random.uniform(5.0, 9.0) # Simulate a range of scores
                     qualified = relevance_score >= qualified_threshold

                     example = ExampleResearchResult(
                         example_number=example_counter,
                         source=source_name,
                         source_url=f"http://example.com/{source_name.lower()}/mock-example-{example_counter}", # Mock URL
                         image_url=f"http://example.com/images/mock-example-{example_counter}.png", # Mock image URL
                         local_image_path=str(self.examples_dir / f"mock-example-{example_counter}.png"), # Mock local path
                         description=f"Simulated example for '{query}' from {source_name}",
                         search_query=query,
                         relevance_score=relevance_score,
                         qualified=qualified,
                         # Simulate detailed scores for the example
                         functional_similarity=int(max(1, min(10, relevance_score * 1.1 - np.random.uniform(0, 3)))),
                         user_flow_alignment=int(max(1, min(10, relevance_score * 0.9 - np.random.uniform(0, 2)))),
                         information_architecture=int(max(1, min(10, relevance_score * 0.8 - np.random.uniform(0, 2)))),
                         interaction_patterns=int(max(1, min(10, relevance_score * 0.7 - np.random.uniform(0, 2)))),
                         complexity_level=int(max(1, min(10, relevance_score * 0.6 - np.random.uniform(0, 2)))),
                         visual_design_quality=int(max(1, min(10, relevance_score * 1.2 - np.random.uniform(0, 3)))),
                         layout_effectiveness=int(max(1, min(10, relevance_score * 1.1 - np.random.uniform(0, 2)))),
                         user_experience_flow=int(max(1, min(10, relevance_score * 1.0 - np.random.uniform(0, 2)))),
                         accessibility_considerations=int(max(1, min(10, relevance_score * 0.8 - np.random.uniform(0, 2)))),
                         structural_alignment_score = (example.functional_similarity + example.user_flow_alignment + example.information_architecture + example.interaction_patterns + example.complexity_level) / 5,
                         quality_assessment_score = (example.visual_design_quality + example.layout_effectiveness + example.user_experience_flow + example.accessibility_considerations) / 4,
                         analysis_notes="Simulated analysis notes."
                     )
                     examples.append(example)

        # Filter for qualified examples and sort by relevance
        qualified_examples = [ex for ex in examples if ex.qualified]
        qualified_examples.sort(key=lambda x: x.relevance_score, reverse=True)

        # Keep top N qualified examples
        final_examples = qualified_examples[:20] # Keep max 20 for analysis as per prompt

        self.total_examples_researched += len(examples) # Count total simulated finds
        logger.info(f"   🔎 Research complete: {len(examples)} simulated found, {len(qualified_examples)} qualified, {len(final_examples)} selected for analysis.")

        # In a real implementation, you would download example images here.
        # For sim: create dummy files
        for ex in final_examples:
            try:
                 with open(ex.local_image_path, 'w') as f:
                     f.write("Simulated image file content")
            except Exception as e:
                 logger.warning(f"Could not create dummy example file {ex.local_image_path}: {e}")


        return final_examples

    def _search_and_analyze_examples(self, query: str, source_name: str, page: DiscoveredPage) -> List[ExampleResearchResult]:
        """Search a source and analyze examples (Placeholder - Dangerous if actually implemented via scraping)"""
        # THIS METHOD IS A PLACEHOLDER. IMPLEMENTING WEB SCRAPING OF SEARCH RESULTS
        # OR SITES LIKE DRIBBBLE/BEHANCE IS LIKELY AGAINST THEIR TERMS OF SERVICE
        # AND PRONE TO FAILURE. DO NOT IMPLEMENT ACTUAL SCRAPING HERE.
        logger.debug(f"     Simulating search for '{query}' on {source_name}...")

        simulated_results = []
        num_results = np.random.randint(1, 5) # Simulate finding a few results

        for i in range(num_results):
            relevance = np.random.uniform(6.0, 9.5) # Simulate some relevance scores
            simulated_results.append(
                ExampleResearchResult(
                    example_number=len(simulated_results) + 1,
                    source=source_name,
                    source_url=f"http://mock.{source_name.lower()}.com/example/{query.replace(' ', '-')}-{i}",
                    image_url=f"http://mock.imagehosting.com/{source_name.lower()}/{i}.png",
                    description=f"Simulated result {i+1} for '{query}'",
                    search_query=query,
                    relevance_score=relevance,
                    qualified=relevance >= 7.0,
                    # Fill in other fields with simulated/default data
                    functional_similarity=int(max(1, min(10, relevance * 0.8))),
                    user_flow_alignment=int(max(1, min(10, relevance * 0.7))),
                    information_architecture=int(max(1, min(10, relevance * 0.6))),
                    interaction_patterns=int(max(1, min(10, relevance * 0.7))),
                    complexity_level=int(max(1, min(10, page.estimated_complexity + np.random.randint(-2, 2)))), # Relate to page complexity
                    visual_design_quality=int(max(1, min(10, relevance * 1.1))),
                    layout_effectiveness=int(max(1, min(10, relevance * 1.0))),
                    user_experience_flow=int(max(1, min(10, relevance * 1.0))),
                    accessibility_considerations=int(max(1, min(10, relevance * 0.9))),
                    structural_alignment_score = (relevance * 0.8 + relevance * 0.7 + relevance * 0.6 + relevance * 0.7 + (page.estimated_complexity + np.random.randint(-2, 2))) / 5, # Example calculation
                    quality_assessment_score = (relevance * 1.1 + relevance * 1.0 + relevance * 1.0 + relevance * 0.9) / 4, # Example calculation
                    analysis_notes="Simulated analysis."
                )
            )

        # Calculate relevance scores based on simulated detailed scores
        for res in simulated_results:
             res.relevance_score = (res.structural_alignment_score * 0.6) + (res.quality_assessment_score * 0.4)
             res.qualified = res.relevance_score >= 7.0

        logger.debug(f"     Simulated {len(simulated_results)} results from {source_name}.")
        return simulated_results

    def _analyze_examples_for_improvements(self, examples: List[ExampleResearchResult], scoring: DetailedScoring) -> Dict[str, Any]:
        """Analyze qualified examples to identify common patterns and best practices (Placeholder)"""
        logger.debug("     Analyzing qualified examples for improvement patterns... (Simulated)")
        # In a real implementation, this would involve:
        # - Analyzing the visual characteristics of high-scoring examples (e.g., using image analysis, comparing layouts).
        # - Analyzing the structure (DOM) of examples if possible.
        # - Cross-referencing example features with low-scoring areas of the target page.
        # - Identifying common UI patterns (e.g., filter designs, card layouts, form structures) in examples.
        patterns: Dict[str, Any] = {
            'common_layouts': ['Grid', 'List', 'Card'], # Example detected patterns
            'effective_navigation_patterns': ['Sidebar', 'Top Nav', 'Breadcrumbs'],
            'successful_form_design_elements': ['Clear labels', 'Inline validation', 'Field grouping'],
            'visual_trends': ['Minimalist design', 'Bold typography', 'Consistent spacing'],
            'accessibility_patterns': ['Skip links', 'ARIA attributes used', 'Keyboard navigation friendly'],
            'relevant_examples': [ex for ex in examples if ex.relevance_score > 8.5] # Highlight highly relevant ones
        }
        logger.debug("     Example analysis simulated.")
        return patterns

    def _generate_prioritized_improvements(self,
                                         page: DiscoveredPage,
                                         examples: List[ExampleResearchResult],
                                         scoring: DetailedScoring) -> List[ImprovementSuggestion]:
        """
        Generate and mathematically prioritize improvement suggestions (Simulated)
        based on example research and scoring analysis
        """
        logger.info(f"   💡 Generating prioritized improvements... (Simulated)")

        improvements: List[ImprovementSuggestion] = []

        # Analyze examples for improvement patterns (using the simulated helper)
        improvement_patterns = self._analyze_examples_for_improvements(examples, scoring)

        # Generate suggestions based on low scores and identified patterns
        # This logic needs to be sophisticated, mapping low scores/analysis findings
        # to concrete, actionable UI/UX/Technical suggestions, potentially referencing examples.
        if scoring.structural_weighted / self.scoring_weights['structural'] < 7: # Check raw structural score average
            improvements.extend(self._generate_structural_improvements(page, scoring, improvement_patterns, examples))

        if scoring.visual_weighted / self.scoring_weights['visual'] < 7: # Check raw visual score average
            improvements.extend(self._generate_visual_improvements(page, scoring, improvement_patterns, examples))

        if scoring.ux_weighted / self.scoring_weights['ux'] < 7: # Check raw UX score average
             improvements.extend(self._generate_ux_improvements(page, scoring, improvement_patterns, examples))

        # Add technical suggestions based on initial page analysis (e.g., performance, tech stack)
        improvements.extend(self._generate_technical_improvements(page, scoring, improvement_patterns))

        # Filter out duplicate suggestions based on title/description
        unique_improvements: Dict[Tuple[str, str], ImprovementSuggestion] = {}
        for imp in improvements:
             key = (imp.category, imp.title)
             if key not in unique_improvements:
                  unique_improvements[key] = imp
             # Optionally, merge descriptions or pick the best example reference

        prioritized_improvements = list(unique_improvements.values())

        # Calculate mathematical priorities for all unique suggestions
        for suggestion in prioritized_improvements:
            # Simulate priority factors based on the type of suggestion, score, complexity etc.
            suggestion.ux_impact = int(max(1, min(10, 12 - (scoring.total_score / 10 * 10) - np.random.randint(0, 3)))) # Lower current score -> higher impact
            suggestion.business_value = np.random.randint(3, 10) # Varies greatly by suggestion type and page
            suggestion.implementation_feasibility = int(max(1, min(10, 12 - page.estimated_complexity - np.random.randint(0, 3)))) # Higher page complexity -> lower feasibility
            suggestion.maintenance_benefit = np.random.randint(3, 9)
            suggestion.low_effort_bonus = 10 if suggestion.estimated_time_minutes < 120 else np.random.randint(1, 5) # Higher bonus for quick wins

            # Calculate the final priority score using the defined formula
            priority_score = self._calculate_mathematical_priority(suggestion, page, scoring)
            suggestion.priority_score = priority_score
            suggestion.priority_level = self._score_to_priority_level(priority_score)

            # Simulate estimated time
            suggestion.estimated_time_minutes = np.random.randint(30, 480) # 0.5 to 8 hours

            # Simulate tech compliance based on suggestion type
            suggestion.htmx_bootstrap_compatible = np.random.random() > 0.1 # Assume most things are compatible
            suggestion.requires_alpine_js = 'interactive component' in suggestion.description.lower() or np.random.random() < 0.2
            suggestion.hypermedia_compliant = suggestion.htmx_bootstrap_compatible # Often related


        # Sort by priority score (descending)
        prioritized_improvements.sort(key=lambda x: x.priority_score, reverse=True)

        # Filter to high and medium priority only for the final report (as per prompt)
        final_improvements = [imp for imp in prioritized_improvements
                            if imp.priority_level in ['HIGH', 'MEDIUM']]

        logger.info(f"   💡 Generated {len(improvements)} raw suggestions, {len(unique_improvements)} unique, {len(final_improvements)} prioritized (HIGH/MEDIUM).")

        self.total_improvements_suggested += len(final_improvements)

        return final_improvements

    # --- Helper methods for generating specific types of improvements (Simulated) ---
    # These should analyze scores, page data, and example patterns to create suggestions.
    def _generate_structural_improvements(self, page: DiscoveredPage, scoring: DetailedScoring, patterns: Dict[str, Any], examples: List[ExampleResearchResult]) -> List[ImprovementSuggestion]:
         """Simulate generating structural improvement suggestions"""
         suggestions: List[ImprovementSuggestion] = []
         if scoring.layout_organization < 7:
              suggestions.append(ImprovementSuggestion(
                  category="STRUCTURAL",
                  title="Refine Page Layout",
                  description="The current layout feels cluttered or lacks clear hierarchy. Consider adopting a grid system or clearer sectioning based on common patterns like " + ", ".join(patterns.get('common_layouts', ['Grid', 'Flexbox'])),
                  source_example=next((ex for ex in examples if ex.layout_effectiveness > 8), None),
                  ux_impact=8, business_value=7, implementation_feasibility=7, maintenance_benefit=6, low_effort_bonus=4, estimated_time_minutes=240
              ))
         if scoring.navigation_clarity < 7:
              suggestions.append(ImprovementSuggestion(
                  category="STRUCTURAL",
                  title="Improve Navigation Clarity",
                  description="Navigation is not intuitive. Review common navigation patterns like " + ", ".join(patterns.get('effective_navigation_patterns', ['Sidebar', 'Top Nav'])),
                  source_example=next((ex for ex in examples if ex.user_flow_alignment > 8), None),
                  ux_impact=9, business_value=8, implementation_feasibility=8, maintenance_benefit=7, low_effort_bonus=5, estimated_time_minutes=120
              ))
         # Add more structural suggestions based on other criteria
         return suggestions

    def _generate_visual_improvements(self, page: DiscoveredPage, scoring: DetailedScoring, patterns: Dict[str, Any], examples: List[ExampleResearchResult]) -> List[ImprovementSuggestion]:
         """Simulate generating visual design improvement suggestions"""
         suggestions: List[ImprovementSuggestion] = []
         if scoring.visual_hierarchy < 7 or scoring.color_usage < 7 or scoring.typography < 7:
              suggestions.append(ImprovementSuggestion(
                  category="VISUAL",
                  title="Enhance Visual Consistency and Hierarchy",
                  description="Improve use of color, typography, and spacing to create a clearer visual hierarchy and more polished look, following trends like " + ", ".join(patterns.get('visual_trends', ['Consistent palette', 'Readable fonts'])),
                  source_example=next((ex for ex in examples if ex.visual_design_quality > 8.5), None),
                  ux_impact=7, business_value=6, implementation_feasibility=7, maintenance_benefit=7, low_effort_bonus=4, estimated_time_minutes=180
              ))
         if scoring.spacing_alignment < 7:
              suggestions.append(ImprovementSuggestion(
                  category="VISUAL",
                  title="Refine Spacing and Alignment",
                  description="Adjust padding, margins, and alignment for a cleaner, more balanced appearance.",
                  source_example=next((ex for ex in examples if ex.layout_effectiveness > 8), None),
                  ux_impact=6, business_value=5, implementation_feasibility=9, maintenance_benefit=8, low_effort_bonus=8, estimated_time_minutes=60
              ))
         return suggestions

    def _generate_ux_improvements(self, page: DiscoveredPage, scoring: DetailedScoring, patterns: Dict[str, Any], examples: List[ExampleResearchResult]) -> List[ImprovementSuggestion]:
         """Simulate generating UX improvement suggestions"""
         suggestions: List[ImprovementSuggestion] = []
         if scoring.ease_of_use < 7:
              suggestions.append(ImprovementSuggestion(
                  category="USER_EXPERIENCE",
                  title="Simplify User Workflows",
                  description="Identify friction points in key user flows (e.g., form submission, data filtering) and simplify steps.",
                  source_example=next((ex for ex in examples if ex.user_experience_flow > 8), None),
                  ux_impact=9, business_value=9, implementation_feasibility=6, maintenance_benefit=7, low_effort_bonus=3, estimated_time_minutes=360
              ))
         if scoring.accessibility < 7:
             suggestions.append(ImprovementSuggestion(
                 category="USER_EXPERIENCE",
                 title="Address Accessibility Issues",
                 description="Improve keyboard navigation, contrast ratios, screen reader compatibility (e.g., add ARIA attributes) based on WCAG guidelines and detected patterns like " + ", ".join(patterns.get('accessibility_patterns', ['Skip links', 'ARIA labels'])),
                 source_example=None, # Accessibility might not need a visual example
                 ux_impact=10, business_value=8, implementation_feasibility=7, maintenance_benefit=8, low_effort_bonus=4, estimated_time_minutes=240
             ))
         return suggestions

    def _generate_technical_improvements(self, page: DiscoveredPage, scoring: DetailedScoring, patterns: Dict[str, Any]) -> List[ImprovementSuggestion]:
        """Simulate generating technical improvement suggestions"""
        suggestions: List[ImprovementSuggestion] = []
        if scoring.loading_states < 7:
             suggestions.append(ImprovementSuggestion(
                 category="TECHNICAL",
                 title="Optimize Page Load Performance",
                 description="Identify and optimize assets (images, CSS, JS), reduce DOM complexity, or implement lazy loading for faster initial rendering.",
                 source_example=None,
                 ux_impact=8, business_value=7, implementation_feasibility=6, maintenance_benefit=7, low_effort_bonus=3, estimated_time_minutes=180
             ))
        # Add suggestions related to HTMX/Bootstrap/Alpine.js adoption/improvement if relevant to the page type/complexity
        if page.has_forms and not page.has_dynamic_content: # Example: A static form page that could benefit from HTMX
             suggestions.append(ImprovementSuggestion(
                 category="TECHNICAL",
                 title="Implement HTMX for Form Submissions",
                 description="Refactor form submission using HTMX to enable partial page updates and improve responsiveness without full page reloads.",
                 source_example=None,
                 htmx_bootstrap_compatible=True, requires_alpine_js=False, hypermedia_compliant=True,
                 ux_impact=7, business_value=6, implementation_feasibility=8, maintenance_benefit=8, low_effort_bonus=7, estimated_time_minutes=120
             ))
        return suggestions

    def _generate_example_inspired_improvements(self, examples: List[ExampleResearchResult], scoring: DetailedScoring) -> List[ImprovementSuggestion]:
        """Simulate generating improvements directly inspired by highly relevant examples (Placeholder)"""
        logger.debug("     Generating example-inspired improvements... (Simulated)")
        suggestions: List[ImprovementSuggestion] = []
        # Look at the top 3-5 most relevant examples (score > 8.5)
        relevant_examples = [ex for ex in examples if ex.relevance_score > 8.5][:5]

        for ex in relevant_examples:
            # Simulate suggestions based on example strength vs page weakness
            if ex.visual_design_quality > scoring.visual_weighted / self.scoring_weights['visual'] + 1.0:
                 suggestions.append(ImprovementSuggestion(
                     category="VISUAL",
                     title=f"Adopt Visual Style from Example #{ex.example_number}",
                     description=f"Example #{ex.example_number} ({ex.source}) demonstrates a cleaner visual style. Focus on improving color palette, typography, and use of whitespace.",
                     source_example=ex,
                     ux_impact=7, business_value=6, implementation_feasibility=7, maintenance_benefit=7, low_effort_bonus=4, estimated_time_minutes=180 # Simulate factors
                 ))
            if ex.layout_effectiveness > scoring.layout_organization + 1.0:
                suggestions.append(ImprovementSuggestion(
                    category="STRUCTURAL",
                    title=f"Incorporate Layout Pattern from Example #{ex.example_number}",
                    description=f"Example #{ex.example_number} ({ex.source}) uses an effective layout for {ex.description}. Adapt its grid/flex structure for better organization.",
                    source_example=ex,
                    ux_impact=8, business_value=7, implementation_feasibility=7, maintenance_benefit=6, low_effort_bonus=4, estimated_time_minutes=240 # Simulate factors
                ))
            # Add more based on functional_similarity, user_experience_flow etc.
        return suggestions


    def _calculate_mathematical_priority(self, suggestion: ImprovementSuggestion, page: DiscoveredPage, scoring: DetailedScoring) -> float:
        """
        Calculate mathematical priority using the weighted formula:
        Priority = (UX Impact × 0.3) + (Business Value × 0.25) + (Implementation Feasibility × 0.2) +
                  (Maintenance Benefit × 0.15) + (Low Effort Bonus × 0.1)
        Scores are 1-10.
        """
        # Use the simulated scores already populated in the Suggestion dataclass
        priority_score = (
            suggestion.ux_impact * 0.3 +
            suggestion.business_value * 0.25 +
            suggestion.implementation_feasibility * 0.2 +
            suggestion.maintenance_benefit * 0.15 +
            suggestion.low_effort_bonus * 0.1
        )

        # Apply penalty if tech stack compliance check fails (simulated)
        # If a suggestion is not compatible with HTMX/Bootstrap/Alpine.js and it requires it (hypothetical check), penalize it.
        # In this simulation, we'll just add a small random penalty if incompatible flag is False.
        if not suggestion.htmx_bootstrap_compatible:
             priority_score *= 0.8 # Apply 20% penalty for incompatibility

        return round(priority_score, 2) # Round to 2 decimal places

    def _score_to_priority_level(self, score: float) -> str:
        """Convert priority score (calculated 1-10) to priority level"""
        if score >= 8.0:
            return "HIGH"
        elif score >= 6.0:
            return "MEDIUM"
        elif score >= 4.0:
            return "LOW"
        else:
            return "DISCARD"

    def _calculate_global_statistics(self, review_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate and return global statistics from review results"""
        logger.info("📊 Calculating global statistics...")
        total_scores = []
        total_improvement_count = 0
        total_example_count = 0
        total_review_duration = 0

        for page_result in review_results.values():
            if 'initial_scoring' in page_result:
                total_scores.append(page_result['initial_scoring'].get('total_score', 0))
            if 'improvements' in page_result:
                total_improvement_count += len(page_result['improvements'])
            if 'example_research' in page_result:
                total_example_count += len(page_result['example_research'])
            if 'review_duration_seconds' in page_result:
                 total_review_duration += page_result['review_duration_seconds']


        average_initial_score = np.mean(total_scores) if total_scores else 0

        stats = {
            'average_initial_score': round(average_initial_score, 2),
            'total_improvements_suggested': total_improvement_count,
            'total_examples_researched_qualified': total_example_count, # This counts qualified examples per page
            'total_examples_researched_attempted_simulated': self.total_examples_researched, # This counts all simulated finds
            'total_review_duration_seconds': round(total_review_duration, 2),
            'pages_with_high_priority_improvements': sum(1 for pr in review_results.values() if any(imp['priority_level'] == 'HIGH' for imp in pr.get('improvements', []))),
            'pages_with_low_scores': sum(1 for pr in review_results.values() if 'initial_scoring' in pr and pr['initial_scoring'].get('total_score', 10) < 6.0),
        }
        logger.info("📊 Global statistics calculated.")
        return stats

    def _save_discovery_results(self, pages: List[DiscoveredPage]):
        """Save discovered pages list to a JSON file (Placeholder)"""
        logger.debug("💾 Saving discovery results... (Not implemented)")
        # Save list of DiscoveredPage objects (as dicts) to a JSON file in analysis_dir
        # path = self.analysis_dir / "discovered_pages.json"
        # try:
        #     with open(path, 'w', encoding='utf-8') as f:
        #         json.dump([asdict(p) for p in pages], f, indent=4)
        #     logger.debug(f"💾 Discovery results saved to {path}")
        # except Exception as e:
        #      logger.warning(f"Failed to save discovery results: {e}")

    def _save_comprehensive_results(self, results: Dict[str, Any]):
        """Save full comprehensive results to a JSON file"""
        results_path = self.analysis_dir / f"comprehensive_review_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        logger.info(f"💾 Saving comprehensive results to {results_path}")
        try:
            # Use json.dumps with sort_keys for consistent output if needed
            # Ensure all objects are serializable (dataclasses are converted to dicts)
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, default=str) # Use default=str to handle datetime objects
            logger.info(f"💾 Comprehensive results saved.")
        except Exception as e:
            logger.error(f"❌ Failed to save comprehensive results: {e}")

    def _verify_methodology_compliance(self, results: Dict[str, Any]) -> Dict[str, bool]:
        """Verify if all steps of the methodology were attempted for each reviewed page (Placeholder)"""
        logger.debug("🔬 Verifying methodology compliance... (Not implemented)")
        # Check if screenshots, scoring, research, and improvements were attempted for each page in results.
        # Due to simulation, assume compliance if the review completed without critical errors.
        return {"all_steps_attempted_per_page": True} # Simulated success

    def generate_comprehensive_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive analysis report in Markdown format"""
        logger.info("📋 Generating comprehensive report...")

        report_path = self.reports_dir / f"comprehensive_ui_ux_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(self._generate_report_content(results))
            logger.info(f"📋 Report saved: {report_path}")
            return str(report_path)
        except Exception as e:
            logger.error(f"❌ Failed to generate report: {e}")
            return "" # Return empty string or raise exception on failure

    def _generate_report_content(self, results: Dict[str, Any]) -> str:
        """Construct the markdown report content"""
        report_content = f"# Comprehensive Frontend UI/UX Review Report\n\n"
        report_content += "## Session Summary\n\n"
        session_info = results.get('session_info', {})
        global_stats = results.get('global_statistics', {})

        report_content += f"- **Start Time:** {session_info.get('start_time', 'N/A')}\n"
        report_content += f"- **End Time:** {session_info.get('end_time', 'N/A')}\n"
        report_content += f"- **Total Pages Discovered:** {session_info.get('total_pages_discovered', 0)}\n"
        report_content += f"- **Pages Reviewed:** {session_info.get('pages_reviewed_count', 0)}\n"
        report_content += f"- **Total Review Duration:** {timedelta(seconds=global_stats.get('total_review_duration_seconds', 0))}\n"
        report_content += f"- **Average Initial Score:** {global_stats.get('average_initial_score', 0):.2f}/10\n"
        report_content += f"- **Total Examples Researched (Simulated):** {global_stats.get('total_examples_researched_attempted_simulated', 0)}\n"
        report_content += f"- **Qualified Examples Used:** {global_stats.get('total_examples_researched_qualified', 0)}\n"
        report_content += f"- **Total Improvements Suggested:** {global_stats.get('total_improvements_suggested', 0)}\n"
        report_content += f"- **Pages with High Priority Improvements:** {global_stats.get('pages_with_high_priority_improvements', 0)}\n"
        report_content += f"- **Pages with Low Scores (< 6.0):** {global_stats.get('pages_with_low_scores', 0)}\n"
        report_content += f"- **Analysis Directory:** {self.temp_dir}\n\n"

        report_content += "## Methodology Overview\n"
        report_content += self.__doc__.split("COMPREHENSIVE METHODOLOGY:", 1)[1].split("Features:", 1)[0].strip() + "\n\n"

        report_content += "## Detailed Page Reviews\n\n"

        page_reviews = results.get('page_reviews', {})
        if not page_reviews:
            report_content += "No pages were successfully reviewed.\n"
        else:
            for page_id, review_data in page_reviews.items():
                page_info = review_data.get('page_info', {})
                initial_scoring = review_data.get('initial_scoring')
                improvements = review_data.get('improvements', [])
                error = review_data.get('error')

                report_content += f"### Page: {page_info.get('page_name', page_id)}\n\n"
                report_content += f"- **Identifier:** `{page_id}`\n"
                report_content += f"- **Actual URL:** <{page_info.get('actual_url', 'N/A')}>\n"
                report_content += f"- **Type:** {page_info.get('page_type', 'N/A')}, **Section:** {page_info.get('section_category', 'N/A')}\n"
                report_content += f"- **Discovery Method:** {page_info.get('discovery_method', 'N/A')}\n"
                report_content += f"- **Review Duration:** {timedelta(seconds=review_data.get('review_duration_seconds', 0))}\n\n"

                if error:
                    report_content += f"**Review Status:** ❌ Failed\n"
                    report_content += f"**Error Details:** {error}\n\n"
                else:
                    report_content += "**Review Status:** ✅ Completed\n\n"

                    if initial_scoring:
                        report_content += f"#### Initial Scoring: {initial_scoring.get('total_score', 0):.2f}/10\n\n"
                        report_content += f"- **Structural (40%):** {initial_scoring.get('structural_weighted', 0):.2f} (Raw Avg: {(initial_scoring['structural_weighted'] / self.scoring_weights['structural']):.2f} if structural_weighted > 0 else 'N/A')\n"
                        report_content += f"- **Visual (30%):** {initial_scoring.get('visual_weighted', 0):.2f} (Raw Avg: {(initial_scoring['visual_weighted'] / self.scoring_weights['visual']):.2f} if visual_weighted > 0 else 'N/A')\n"
                        report_content += f"- **UX (30%):** {initial_scoring.get('ux_weighted', 0):.2f} (Raw Avg: {(initial_scoring['ux_weighted'] / self.scoring_weights['ux']):.2f} if ux_weighted > 0 else 'N/A')\n"
                        report_content += f"- **Key Improvement Areas:** {', '.join(initial_scoring.get('improvement_areas', ['None']))}\n"
                        report_content += f"- **Rationale:** {initial_scoring.get('scoring_rationale', 'N/A')}\n\n"

                        # Optional: Include detailed raw scores if helpful
                        # report_content += "Detailed Scores:\n"
                        # report_content += f"  - Layout: {initial_scoring.get('layout_organization', '-')}/10, Navigation: {initial_scoring.get('navigation_clarity', '-')}/10, Content Structure: {initial_scoring.get('content_structure', '-')}/10, Responsive: {initial_scoring.get('responsive_design', '-')}/10\n"
                        # report_content += f"  - Visual Hierarchy: {initial_scoring.get('visual_hierarchy', '-')}/10, Color: {initial_scoring.get('color_usage', '-')}/10, Typography: {initial_scoring.get('typography', '-')}/10, Spacing: {initial_scoring.get('spacing_alignment', '-')}/10\n"
                        # report_content += f"  - Ease of Use: {initial_scoring.get('ease_of_use', '-')}/10, Loading States: {initial_scoring.get('loading_states', '-')}/10, Error Handling: {initial_scoring.get('error_handling', '-')}/10, Accessibility: {initial_scoring.get('accessibility', '-')}/10\n\n"


                    report_content += "#### Screenshots Captured:\n\n"
                    screenshots = review_data.get('screenshots', [])
                    if screenshots:
                         for screenshot in screenshots:
                             report_content += f"- {screenshot.get('device_type', 'N/A')} ({screenshot.get('resolution', 'N/A')}) - Status: {'Error' if screenshot.get('has_errors') else 'OK'}\n"
                             if screenshot.get('file_path'):
                                 # Reference temporary file path - might not be accessible externally
                                 # Consider copying key images to a permanent report folder if needed
                                 report_content += f"  (File: `{screenshot.get('file_path', 'N/A').replace(str(self.temp_dir), 'temp_analysis_dir')}`)\n"
                             if screenshot.get('error_details'):
                                report_content += f"  Error: {screenshot.get('error_details')}\n"
                         report_content += "\n"
                    else:
                        report_content += "No screenshots captured for this page.\n\n"


                    report_content += "#### Qualified Examples Researched:\n\n"
                    examples = review_data.get('example_research', [])
                    if examples:
                        examples.sort(key=lambda x: x.get('relevance_score', 0), reverse=True) # Sort by relevance for report
                        for example in examples:
                            report_content += f"- **Example #{example.get('example_number', 'N/A')}**: {example.get('source', 'N/A')} ([Link]({example.get('source_url', '#')}))\n"
                            report_content += f"  - **Relevance Score:** {example.get('relevance_score', 0):.2f}/10\n"
                            report_content += f"  - **Description:** {example.get('description', 'N/A')}\n"
                            report_content += f"  - **Local Image:** `{example.get('local_image_path', 'N/A').replace(str(self.temp_dir), 'temp_analysis_dir')}`\n"
                            # Optional: Include detailed example scores
                            # report_content += f"  - Structural Alignment: {example.get('structural_alignment_score', 0):.2f}, Quality Assessment: {example.get('quality_assessment_score', 0):.2f}\n"
                            report_content += "\n"
                    else:
                        report_content += "No qualified examples found for this page.\n\n"

                    report_content += "#### Prioritized Improvement Suggestions (HIGH/MEDIUM):\n\n"
                    if improvements:
                        # Ensure improvements are sorted by priority for the report
                        improvements.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
                        for suggestion in improvements:
                            report_content += f"- **[{suggestion.get('priority_level', 'N/A')}]** {suggestion.get('title', 'Untitled Suggestion')} ({suggestion.get('category', 'GENERAL')})\n"
                            report_content += f"  - **Description:** {suggestion.get('description', 'N/A')}\n"
                            report_content += f"  - **Priority Score:** {suggestion.get('priority_score', 0):.2f}/10\n"
                            report_content += f"  - **Estimated Time:** {suggestion.get('estimated_time_minutes', '?')} minutes\n"
                            tech_compat = []
                            if suggestion.get('htmx_bootstrap_compatible'): tech_compat.append('HTMX/Bootstrap compatible')
                            if suggestion.get('requires_alpine_js'): tech_compat.append('Requires Alpine.js')
                            if suggestion.get('hypermedia_compliant'): tech_compat.append('Hypermedia compliant')
                            report_content += f"  - **Tech Compliance:** {', '.join(tech_compat) if tech_compat else 'Not specified'}\n"

                            source_example = suggestion.get('source_example')
                            if source_example:
                                report_content += f"  - **Inspired by Example #**: {source_example.get('example_number', 'N/A')} ([Link]({source_example.get('source_url', '#')}))\n"
                            report_content += "\n"
                    else:
                        report_content += "No high or medium priority improvements suggested for this page.\n\n"

                report_content += "---\n\n" # Separator for pages

        return report_content


    def cleanup_temporary_files(self):
        """Clean up all temporary files and directories if cleanup is enabled."""
        if self.cleanup_temp and self.temp_dir.exists():
            logger.info(f"🧹 Cleaning up temporary analysis directory: {self.temp_dir}")
            try:
                shutil.rmtree(self.temp_dir)
                logger.info("🧹 Cleanup complete.")
            except OSError as e:
                logger.error(f"❌ Failed to remove temporary directory {self.temp_dir}: {e}")
        else:
            logger.info("⏭️  Temporary file cleanup skipped.")
            if self.temp_dir.exists():
                 logger.info(f"Analysis files remain in: {self.temp_dir}")


    # --- Utility methods ---
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for file system compatibility across OS"""
        # Remove characters that are not letters, numbers, underscores, hyphens, or periods
        sanitized = re.sub(r'[^\w\-_.]', '_', filename)
        # Limit length (optional, but good for long page names)
        sanitized = sanitized[:100]
        # Ensure it's not empty
        if not sanitized:
             sanitized = "untitled"
        return sanitized


    def run_comprehensive_test(self, quick_test: bool = False, page_limit: Optional[int] = None):
        """
        Main entry point for comprehensive frontend review test
        """
        logger.info("🚀 STARTING COMPREHENSIVE FRONTEND UI/UX REVIEW TEST")
        logger.info("=" * 80)

        # Store start time explicitly
        self.session_start_time = datetime.now()

        # Phase 1: Discovery
        # Discovery runs fully regardless of quick_test or page_limit
        discovered_pages = self.discover_all_pages()

        if not discovered_pages:
            logger.error("❌ No pages discovered - aborting review")
            # Still proceed to cleanup if configured
            return

        # Apply quick_test or page_limit for the review phase
        review_limit = page_limit if page_limit is not None else (5 if quick_test else None)

        # Phase 2: Comprehensive Review
        # The comprehensive_page_review method now handles the limit
        results = self.comprehensive_page_review(discovered_pages, limit=review_limit)

        # Phase 3: Report Generation
        report_path = self.generate_comprehensive_report(results)

        # Phase 4: Summary
        self._print_comprehensive_summary(results)

        logger.info("🎉 COMPREHENSIVE REVIEW COMPLETE!")
        if report_path:
            logger.info(f"📋 Full report: {report_path}")
        logger.info(f"🗂️  Analysis files location: {self.temp_dir}")


    def _print_comprehensive_summary(self, results: Dict[str, Any]):
        """Print comprehensive summary of results to console"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 COMPREHENSIVE REVIEW SUMMARY")
        logger.info("=" * 60)

        session_info = results.get('session_info', {})
        global_stats = results.get('global_statistics', {})

        logger.info(f"🎯 Pages Reviewed: {session_info.get('pages_reviewed_count', 0)} / {session_info.get('total_pages_discovered', 0)}")
        logger.info(f"⏱️  Total Duration: {timedelta(seconds=global_stats.get('total_review_duration_seconds', 0))}")
        logger.info(f"📊 Average Score: {global_stats.get('average_initial_score', 0):.2f}/10")
        # Use the total count from the reviewer instance for consistency
        logger.info(f"🔍 Examples Researched (Simulated Attempts): {self.total_examples_researched}")
        logger.info(f"💡 Improvements Suggested (HIGH/MEDIUM): {self.total_improvements_suggested}")
        logger.info("=" * 60)
        logger.info(f"Analysis files located in: {self.temp_dir}")
        if not self.cleanup_temp:
             logger.info("Temporary file cleanup disabled.")


def main():
    """Command line interface for comprehensive frontend reviewer"""
    parser = argparse.ArgumentParser(
        description='Comprehensive Frontend UI/UX Review System - Default: Full Review',
        formatter_class=argparse.RawTextHelpFormatter # Helps preserve the usage formatting
    )
    # Use mutually exclusive group for command types
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--discover-all', action='store_true', help='Run discovery phase only and print found pages.')
    group.add_argument('--full-review', action='store_true', help='Run complete comprehensive review (same as default).')
    group.add_argument('--quick-test', action='store_true', help='Run quick test: discovery + review of first 5 discovered pages.')

    parser.add_argument('--pages', type=int, help='Limit the number of pages to review in full/quick review mode.')
    parser.add_argument('--base-url', default='http://localhost:8000', help='Base URL of the running Django application.')
    parser.add_argument('--project-path', default='.', help='Path to the Django project root directory.')
    parser.add_argument('--temp-dir', help='Specify a directory for analysis files instead of using a temporary one. Prevents automatic cleanup.')
    parser.add_argument('--no-cleanup', action='store_true', help='Do not clean up temporary analysis files upon completion or error.')


    args = parser.parse_args()

    # Ensure quick-test limit is applied if --pages is not specified
    page_limit = args.pages
    if args.quick_test and page_limit is None:
         page_limit = 5 # Default quick test limit

    # Create the reviewer instance
    reviewer = None
    try:
        reviewer = ComprehensiveFrontendReviewer(
            base_url=args.base_url,
            django_project_path=args.project_path,
            temp_analysis_dir=args.temp_dir,
            no_cleanup=args.no_cleanup # Pass the no_cleanup flag to the reviewer
        )

        if args.discover_all:
            pages = reviewer.discover_all_pages()
            print("\n--- Discovered Pages ---")
            if pages:
                for i, page in enumerate(pages):
                    print(f"{i+1}. Name: {page.page_name}")
                    print(f"   URL: {page.actual_url or page.url_pattern or 'N/A'}")
                    print(f"   Template: {page.template_path or 'N/A'}")
                    print(f"   Type: {page.page_type}, Section: {page.section_category}")
                    print(f"   Method: {page.discovery_method}, Auth: {page.requires_auth}")
                    print("-" * 20)
            else:
                print("No pages discovered.")
            print("------------------------")

        elif args.quick_test or args.full_review:
            # run_comprehensive_test handles the quick_test logic and page_limit
            reviewer.run_comprehensive_test(quick_test=args.quick_test, page_limit=page_limit)

        else:
            # Default behavior: run full comprehensive review
            reviewer.run_comprehensive_test(quick_test=False, page_limit=page_limit)

    except KeyboardInterrupt:
        logger.info("\n⚠️  Review interrupted by user.")
    except Exception as e:
        logger.critical(f"❌ A critical error occurred during execution: {e}", exc_info=True)
        sys.exit(1) # Exit with a non-zero code on critical failure
    finally:
        # Cleanup is handled here in main's finally block using the reviewer instance
        if reviewer:
            reviewer.cleanup_temporary_files()
        else:
            # If reviewer creation failed, temp_dir might not be set, but let's try to log
            print("Reviewer instance not created, skipping cleanup.")
            # Potentially try to clean up a default temp dir if creation failed early,
            # but this adds complexity. Relying on the OS for temp dir cleanup might be acceptable here.


if __name__ == "__main__":
    main()