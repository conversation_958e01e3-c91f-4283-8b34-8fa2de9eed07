#!/usr/bin/env python3
"""
Script to automatically fix import errors in test files.
This will:
1. Replace 'Message' with 'ChatMessage' 
2. Remove 'UserSettings' imports and usage
3. Fix relative imports to absolute imports
4. Remove non-existent model imports
"""

import re
from pathlib import Path


def fix_file_imports(file_path):
    """Fix imports in a single file."""
    try:
        with open(file_path, encoding='utf-8') as f:
            content = f.read()

        original_content = content
        changes_made = []

        # 1. Replace Message with ChatMessage in imports
        if 'from CLEAR.models import' in content and 'Message,' in content:
            content = re.sub(r'\bMessage,', 'ChatMessage,', content)
            changes_made.append("Replaced Message with ChatMessage in imports")

        # 2. Remove UserSettings from imports
        content = re.sub(r',\s*UserSettings', '', content)
        content = re.sub(r'UserSettings,\s*', '', content)
        content = re.sub(r'from CLEAR\.models import.*UserSettings.*',
                        lambda m: m.group(0).replace(', UserSettings', '').replace('UserSettings, ', '').replace('UserSettings', ''),
                        content)

        if 'UserSettings' in original_content and 'UserSettings' not in content:
            changes_made.append("Removed UserSettings from imports")

        # 3. Remove UserSettings.objects.create() lines
        content = re.sub(r'\s*UserSettings\.objects\.create\([^)]*\)\s*\n', '\n', content)
        if 'UserSettings.objects.create' in original_content and 'UserSettings.objects.create' not in content:
            changes_made.append("Removed UserSettings.objects.create() calls")

        # 4. Fix relative imports to absolute imports
        content = re.sub(r'from \.\.models import', 'from CLEAR.models import', content)
        if '..models' in original_content:
            changes_made.append("Fixed relative imports to absolute imports")

        # 5. Remove MessageAttachment imports
        content = re.sub(r',\s*MessageAttachment', '', content)
        content = re.sub(r'MessageAttachment,\s*', '', content)
        if 'MessageAttachment' in original_content and 'MessageAttachment' not in content:
            changes_made.append("Removed MessageAttachment from imports")

        # 6. Replace MessageAttachment usage with Document
        content = re.sub(r'MessageAttachment\.objects', 'Document.objects', content)
        if 'MessageAttachment.objects' in original_content:
            changes_made.append("Replaced MessageAttachment usage with Document")
            # Also need to ensure Document is imported
            if 'from CLEAR.models import' in content and 'Document' not in content:
                content = re.sub(r'(from CLEAR\.models import[^)]*)', r'\1, Document', content)
                changes_made.append("Added Document to imports")

        # 7. Fix CLEAR.tests.base imports
        content = re.sub(r'from CLEAR\.tests\.base import', 'from tests.utils.base import', content)
        if 'CLEAR.tests.base' in original_content:
            changes_made.append("Fixed CLEAR.tests.base imports")

        # 8. Remove SpatialData and UserProfile from imports (non-existent models)
        for model in ['SpatialData', 'UserProfile']:
            content = re.sub(rf',\s*{model}', '', content)
            content = re.sub(rf'{model},\s*', '', content)
            if model in original_content and model not in content:
                changes_made.append(f"Removed {model} from imports")

        # 9. Replace Message with ChatMessage in code (not just imports)
        # Be careful to only replace when it's clearly the model
        content = re.sub(r'\bMessage\.objects', 'ChatMessage.objects', content)
        content = re.sub(r'= Message\.objects', '= ChatMessage.objects', content)
        content = re.sub(r'Message\(', 'ChatMessage(', content)

        # 10. Fix conversation parameter issues
        content = re.sub(r'conversation=self\.conversation', 'project=self.project', content)

        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        else:
            return []

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def main():
    """Main function to fix all test files."""
    test_dir = Path('tests')
    if not test_dir.exists():
        print("Tests directory not found!")
        return

    total_files = 0
    total_changes = 0

    # Find all Python test files
    for test_file in test_dir.rglob('*.py'):
        if test_file.name.startswith('test_') or test_file.name.endswith('_test.py'):
            total_files += 1
            changes = fix_file_imports(test_file)
            if changes:
                total_changes += len(changes)
                print(f"\n✅ Fixed {test_file}:")
                for change in changes:
                    print(f"   - {change}")
            else:
                print(f"⚪ No changes needed for {test_file}")

    print("\n🎉 Summary:")
    print(f"   Files processed: {total_files}")
    print(f"   Changes made: {total_changes}")
    print("   Ready to run tests!")

if __name__ == '__main__':
    main()
