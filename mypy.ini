[mypy]
python_version = 3.13
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
disallow_subclassing_any = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_return_any = true
warn_unreachable = true
strict_optional = true

# Django-specific settings
plugins = mypy_django_plugin.main

[mypy.plugins.django-stubs]
django_settings_module = clear_htmx.settings

# Ignore certain modules that are problematic
[mypy-migrations.*]
ignore_errors = true

[mypy-*.migrations.*]
ignore_errors = true

[mypy-manage]
ignore_errors = true

# Third-party libraries without stubs
[mypy-crispy_forms.*]
ignore_missing_imports = true

[mypy-django_htmx.*]
ignore_missing_imports = true

[mypy-channels.*]
ignore_missing_imports = true

[mypy-channels_redis.*]
ignore_missing_imports = true

[mypy-celery.*]
ignore_missing_imports = true

[mypy-redis.*]
ignore_missing_imports = true

[mypy-osgeo.*]
ignore_missing_imports = true

[mypy-gdal.*]
ignore_missing_imports = true

[mypy-geos.*]
ignore_missing_imports = true

[mypy-fiona.*]
ignore_missing_imports = true

[mypy-shapely.*]
ignore_missing_imports = true

[mypy-whitenoise.*]
ignore_missing_imports = true

[mypy-corsheaders.*]
ignore_missing_imports = true

[mypy-django_extensions.*]
ignore_missing_imports = true

[mypy-debug_toolbar.*]
ignore_missing_imports = true
