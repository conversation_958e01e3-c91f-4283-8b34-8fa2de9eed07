#!/usr/bin/env python3
"""
Django Template Syntax Checker

Scans Django template files for common syntax issues including:
- Unclosed template tags
- Malformed template variables
- Invalid HTMX attributes
- Missing CSRF tokens in forms
- Broken HTML structure
"""

import json
import re
from pathlib import Path
from typing import Any, Dict, List


class TemplateSyntaxChecker:
    def __init__(self, templates_dir: str):
        self.templates_dir = Path(templates_dir)
        self.errors = []
        self.warnings = []
        self.files_checked = 0

        # Django template tag patterns
        self.template_tag_patterns = {
            'if': r'{%\s*if\s+.*?%}',
            'endif': r'{%\s*endif\s*%}',
            'for': r'{%\s*for\s+.*?%}',
            'endfor': r'{%\s*endfor\s*%}',
            'block': r'{%\s*block\s+.*?%}',
            'endblock': r'{%\s*endblock\s*.*?%}',
            'with': r'{%\s*with\s+.*?%}',
            'endwith': r'{%\s*endwith\s*%}',
            'comment': r'{%\s*comment\s*%}',
            'endcomment': r'{%\s*endcomment\s*%}',
            'load': r'{%\s*load\s+.*?%}',
            'extends': r'{%\s*extends\s+.*?%}',
            'include': r'{%\s*include\s+.*?%}',
            'csrf_token': r'{%\s*csrf_token\s*%}',
            'url': r'{%\s*url\s+.*?%}',
        }

        # Common HTMX attributes
        self.htmx_attributes = [
            'hx-get', 'hx-post', 'hx-put', 'hx-patch', 'hx-delete',
            'hx-trigger', 'hx-target', 'hx-swap', 'hx-include',
            'hx-indicator', 'hx-boost', 'hx-confirm', 'hx-ext'
        ]

    def check_template_tags(self, content: str, filepath: str) -> list[dict[str, Any]]:
        """Check for unclosed template tags."""
        issues = []
        lines = content.split('\n')

        # Stack to track opening tags
        tag_stack = []

        for line_num, line in enumerate(lines, 1):
            # Find all template tags in this line
            tags = re.findall(r'{%\s*(\w+).*?%}', line)

            for tag in tags:
                if tag in ['if', 'for', 'block', 'with', 'comment']:
                    tag_stack.append({'tag': tag, 'line': line_num, 'content': line.strip()})
                elif tag in ['endif', 'endfor', 'endblock', 'endwith', 'endcomment']:
                    expected_tag = tag[3:]  # Remove 'end' prefix
                    if not tag_stack:
                        issues.append({
                            'type': 'error',
                            'issue': f'Unexpected closing tag {{% {tag} %}} without opening tag',
                            'line': line_num,
                            'content': line.strip(),
                            'file': filepath
                        })
                    else:
                        last_tag = tag_stack.pop()
                        if last_tag['tag'] != expected_tag:
                            issues.append({
                                'type': 'error',
                                'issue': f'Mismatched tags: {{% {last_tag["tag"]} %}} opened at line {last_tag["line"]} but {{% {tag} %}} found',
                                'line': line_num,
                                'content': line.strip(),
                                'file': filepath
                            })

        # Check for unclosed tags
        for unclosed_tag in tag_stack:
            issues.append({
                'type': 'error',
                'issue': f'Unclosed tag {{% {unclosed_tag["tag"]} %}}',
                'line': unclosed_tag['line'],
                'content': unclosed_tag['content'],
                'file': filepath
            })

        return issues

    def check_template_variables(self, content: str, filepath: str) -> list[dict[str, Any]]:
        """Check for malformed template variables."""
        issues = []
        lines = content.split('\n')

        for line_num, line in enumerate(lines, 1):
            # Check for incomplete variable tags
            incomplete_vars = re.findall(r'{{[^}]*$|^[^{]*}}', line)
            for var in incomplete_vars:
                if '{{' in var and '}}' not in var:
                    issues.append({
                        'type': 'error',
                        'issue': 'Incomplete template variable (missing closing }})',
                        'line': line_num,
                        'content': line.strip(),
                        'file': filepath
                    })
                elif '}}' in var and '{{' not in var:
                    issues.append({
                        'type': 'error',
                        'issue': 'Incomplete template variable (missing opening {{)',
                        'line': line_num,
                        'content': line.strip(),
                        'file': filepath
                    })

            # Check for single braces that might be typos
            single_braces = re.findall(r'(?<!\{){(?!\{)|(?<!\})}(?!\})', line)
            if single_braces and ('{{' in line or '}}' in line):
                issues.append({
                    'type': 'warning',
                    'issue': 'Single braces near template variables - possible typo',
                    'line': line_num,
                    'content': line.strip(),
                    'file': filepath
                })

        return issues

    def check_htmx_attributes(self, content: str, filepath: str) -> list[dict[str, Any]]:
        """Check for invalid HTMX attributes."""
        issues = []
        lines = content.split('\n')

        for line_num, line in enumerate(lines, 1):
            # Check for malformed HTMX attributes
            for attr in self.htmx_attributes:
                # Look for attributes without quotes
                pattern = rf'{attr}=([^"\s][^\s>]*)'
                matches = re.findall(pattern, line)
                for match in matches:
                    issues.append({
                        'type': 'warning',
                        'issue': f'HTMX attribute {attr} value should be quoted: {attr}="{match}"',
                        'line': line_num,
                        'content': line.strip(),
                        'file': filepath
                    })

                # Check for empty HTMX attributes
                empty_pattern = rf'{attr}=""'
                if re.search(empty_pattern, line):
                    issues.append({
                        'type': 'warning',
                        'issue': f'Empty HTMX attribute: {attr}',
                        'line': line_num,
                        'content': line.strip(),
                        'file': filepath
                    })

        return issues

    def check_csrf_tokens(self, content: str, filepath: str) -> list[dict[str, Any]]:
        """Check for missing CSRF tokens in forms."""
        issues = []
        lines = content.split('\n')

        in_form = False
        form_start_line = 0
        form_method = None
        has_csrf_token = False

        for line_num, line in enumerate(lines, 1):
            # Check for form start
            form_match = re.search(r'<form[^>]*method=[\'"](post|put|patch|delete)[\'"]', line, re.IGNORECASE)
            if form_match:
                in_form = True
                form_start_line = line_num
                form_method = form_match.group(1).lower()
                has_csrf_token = False

            # Check for CSRF token
            if in_form and re.search(r'{%\s*csrf_token\s*%}', line):
                has_csrf_token = True

            # Check for form end
            if in_form and '</form>' in line.lower():
                if form_method in ['post', 'put', 'patch', 'delete'] and not has_csrf_token:
                    issues.append({
                        'type': 'error',
                        'issue': f'Form with method="{form_method}" missing CSRF token',
                        'line': form_start_line,
                        'content': f'Form started at line {form_start_line}',
                        'file': filepath
                    })
                in_form = False

        return issues

    def check_html_structure(self, content: str, filepath: str) -> list[dict[str, Any]]:
        """Check for basic HTML structure issues."""
        issues = []
        lines = content.split('\n')

        # Track HTML tags
        tag_stack = []

        for line_num, line in enumerate(lines, 1):
            # Find HTML tags (simplified check)
            html_tags = re.findall(r'<(/?)(\w+)(?:\s[^>]*)?\s*/?>', line)

            for is_closing, tag_name in html_tags:
                tag_name = tag_name.lower()

                # Skip self-closing tags
                if tag_name in ['img', 'br', 'hr', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr']:
                    continue

                if is_closing:  # Closing tag
                    if not tag_stack:
                        issues.append({
                            'type': 'warning',
                            'issue': f'Unexpected closing HTML tag </{tag_name}>',
                            'line': line_num,
                            'content': line.strip(),
                            'file': filepath
                        })
                    else:
                        last_tag = tag_stack.pop()
                        if last_tag['tag'] != tag_name:
                            issues.append({
                                'type': 'warning',
                                'issue': f'Mismatched HTML tags: <{last_tag["tag"]}> opened at line {last_tag["line"]} but </{tag_name}> found',
                                'line': line_num,
                                'content': line.strip(),
                                'file': filepath
                            })
                else:  # Opening tag
                    tag_stack.append({'tag': tag_name, 'line': line_num})

        return issues

    def check_file(self, filepath: Path) -> list[dict[str, Any]]:
        """Check a single template file for syntax issues."""
        try:
            with open(filepath, encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return [{
                'type': 'error',
                'issue': f'Could not read file: {e!s}',
                'line': 0,
                'content': '',
                'file': str(filepath)
            }]

        issues = []
        relative_path = str(filepath.relative_to(self.templates_dir))

        # Run all checks
        issues.extend(self.check_template_tags(content, relative_path))
        issues.extend(self.check_template_variables(content, relative_path))
        issues.extend(self.check_htmx_attributes(content, relative_path))
        issues.extend(self.check_csrf_tokens(content, relative_path))
        issues.extend(self.check_html_structure(content, relative_path))

        return issues

    def scan_templates(self) -> dict[str, Any]:
        """Scan all template files in the templates directory."""
        if not self.templates_dir.exists():
            return {
                'error': f'Templates directory not found: {self.templates_dir}',
                'files_checked': 0,
                'issues': []
            }

        all_issues = []

        # Find all HTML template files
        template_files = list(self.templates_dir.rglob('*.html'))

        for template_file in template_files:
            self.files_checked += 1
            issues = self.check_file(template_file)
            all_issues.extend(issues)

        # Categorize issues
        errors = [issue for issue in all_issues if issue['type'] == 'error']
        warnings = [issue for issue in all_issues if issue['type'] == 'warning']

        return {
            'files_checked': self.files_checked,
            'total_issues': len(all_issues),
            'errors': len(errors),
            'warnings': len(warnings),
            'issues': all_issues,
            'summary': self.generate_summary(errors, warnings)
        }

    def generate_summary(self, errors: list[dict], warnings: list[dict]) -> dict[str, Any]:
        """Generate a summary of issues found."""
        error_types = {}
        warning_types = {}

        for error in errors:
            issue_type = error['issue'].split(':')[0] if ':' in error['issue'] else error['issue']
            error_types[issue_type] = error_types.get(issue_type, 0) + 1

        for warning in warnings:
            issue_type = warning['issue'].split(':')[0] if ':' in warning['issue'] else warning['issue']
            warning_types[issue_type] = warning_types.get(issue_type, 0) + 1

        return {
            'error_types': error_types,
            'warning_types': warning_types,
            'most_common_errors': sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5],
            'most_common_warnings': sorted(warning_types.items(), key=lambda x: x[1], reverse=True)[:5]
        }


def main():
    """Main function to run the template syntax checker."""
    templates_dir = '/workspaces/clear_htmx/templates'

    print("🔍 Django Template Syntax Checker")
    print("=" * 50)
    print(f"Scanning templates in: {templates_dir}")
    print()

    checker = TemplateSyntaxChecker(templates_dir)
    results = checker.scan_templates()

    if 'error' in results:
        print(f"❌ Error: {results['error']}")
        return

    # Print summary
    print("📊 Scan Results:")
    print(f"   Files checked: {results['files_checked']}")
    print(f"   Total issues: {results['total_issues']}")
    print(f"   Errors: {results['errors']}")
    print(f"   Warnings: {results['warnings']}")
    print()

    # Print most common issues
    if results['summary']['most_common_errors']:
        print("🚨 Most Common Errors:")
        for issue_type, count in results['summary']['most_common_errors']:
            print(f"   • {issue_type}: {count} occurrences")
        print()

    if results['summary']['most_common_warnings']:
        print("⚠️  Most Common Warnings:")
        for issue_type, count in results['summary']['most_common_warnings']:
            print(f"   • {issue_type}: {count} occurrences")
        print()

    # Print detailed issues (limit to first 20 for readability)
    if results['issues']:
        print("📋 Detailed Issues (first 20):")
        print("-" * 80)
        for i, issue in enumerate(results['issues'][:20], 1):
            icon = "🚨" if issue['type'] == 'error' else "⚠️"
            print(f"{i:2d}. {icon} {issue['file']}:{issue['line']}")
            print(f"     Issue: {issue['issue']}")
            if issue['content']:
                print(f"     Content: {issue['content'][:100]}{'...' if len(issue['content']) > 100 else ''}")
            print()

        if len(results['issues']) > 20:
            print(f"... and {len(results['issues']) - 20} more issues")

    # Save detailed results to file
    output_file = '/workspaces/clear_htmx/template_syntax_check_results.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"💾 Detailed results saved to: {output_file}")

    # Return appropriate exit code
    return 1 if results['errors'] > 0 else 0


if __name__ == '__main__':
    exit(main())
