import re

with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py') as f:
    content = f.read()

# Find all triple quotes with their positions
matches = list(re.finditer(r'"""', content))
print(f'Total triple quotes found: {len(matches)}')

# Check if we have an even number
if len(matches) % 2 != 0:
    print('UNBALANCED: Odd number of triple quotes found!')
    # Find line numbers for last few matches
    for i, match in enumerate(matches[-10:], len(matches)-9):
        line_num = content[:match.start()].count('\n') + 1
        print(f'Match {i}: Line {line_num}')

    # Show the last unmatched opening quote
    last_match = matches[-1]
    line_num = content[:last_match.start()].count('\n') + 1
    print(f'\nLast (unmatched) triple quote at line {line_num}')

    # Show context around it
    lines = content.split('\n')
    start_line = max(0, line_num - 5)
    end_line = min(len(lines), line_num + 5)

    print(f'\nContext around line {line_num}:')
    for i in range(start_line, end_line):
        marker = ' >>> ' if i + 1 == line_num else '     '
        print(f'{marker}{i+1:4d}: {lines[i]}')
