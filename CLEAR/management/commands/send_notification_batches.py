"""
Management command to send notification batches.

This command should be run periodically (e.g., via cron job) to send
pending email notification batches.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from ...services.notifications import notification_service
from ...models import NotificationBatch


class Command(BaseCommand):
    help = 'Send pending notification batches'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending',
        )
        parser.add_argument(
            '--batch-type',
            type=str,
            choices=['immediate', 'daily', 'weekly', 'all'],
            default='all',
            help='Type of batches to send',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        batch_type = options['batch_type']
        
        self.stdout.write(
            self.style.SUCCESS('Starting notification batch sending process...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No emails will be sent')
            )
        
        # Import here to avoid circular imports
        
        # Get pending batches
        batches = NotificationBatch.objects.filter(
            batch_status='pending',
            scheduled_for__lte=timezone.now()
        )
        
        if batch_type != 'all':
            batches = batches.filter(batch_type=batch_type)
        
        if not batches.exists():
            self.stdout.write(
                self.style.SUCCESS('No pending batches found.')
            )
            return
        
        self.stdout.write(f'Found {batches.count()} pending batches')
        
        sent_count = 0
        failed_count = 0
        
        for batch in batches:
            try:
                if dry_run:
                    self.stdout.write(
                        f'Would send {batch.batch_type} batch to {batch.user.email} '
                        f'with {batch.notification_count} notifications'
                    )
                else:
                    notification_service._send_notification_batch(batch)
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Sent {batch.batch_type} batch to {batch.user.email} '
                            f'with {batch.notification_count} notifications'
                        )
                    )
                sent_count += 1
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to send batch {batch.id} to {batch.user.email}: {e}'
                    )
                )
                failed_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Batch sending complete. Sent: {sent_count}, Failed: {failed_count}'
            )
        )