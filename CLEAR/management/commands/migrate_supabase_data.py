"""
Django management command to migrate data from Supabase export files to Django models.

This command handles the migration of CLEAR platform data from Supabase exports
to the new Django + HTMX structure with simplified messaging system.

Usage:
    python manage.py migrate_supabase_data --export-dir /path/to/exports
    python manage.py migrate_supabase_data --export-dir /path/to/exports --table-name chat_messages
    python manage.py migrate_supabase_data --export-dir /path/to/exports --dry-run
"""

import csv
import json
import os
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils.dateparse import parse_date, parse_datetime
from CLEAR.models import ChatMessage, Project, ProjectTemplate, User, WhisperMessage



class Command(BaseCommand):
    help = 'Migrate data from Supabase export files to Django models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--export-dir',
            type=str,
            required=True,
            help='Directory containing Supabase export files'
        )
        parser.add_argument(
            '--table-name',
            type=str,
            help='Specific table to migrate (optional, migrates all if not specified)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes'
        )
        parser.add_argument(
            '--format',
            choices=['json', 'csv'],
            default='json',
            help='Format of export files (default: json)'
        )

    def handle(self, *args, **options):
        export_dir = options['export_dir']
        table_name = options.get('table_name')
        dry_run = options['dry_run']
        file_format = options['format']

        if not os.path.exists(export_dir):
            raise CommandError(f'Export directory does not exist: {export_dir}')

        self.stdout.write(f'Starting migration from {export_dir}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Define migration order to handle foreign key dependencies
        migration_order = [
            'coordinate_systems',
            'users',
            'permissions',
            'role_permissions',
            'password_reset_tokens',
            'project_templates',
            'stakeholders',
            'projects',
            'utilities',
            'line_styles',
            'utility_line_data',
            'conflicts',
            'tasks',
            'comments',
            'chat_messages',
            'team_messages',  # Will be merged into chat_messages
            'temporary_messages',  # Will become whisper_messages
            'activities',
            'notifications',
        ]

        # If specific table requested, only migrate that table
        if table_name:
            if table_name in migration_order:
                migration_order = [table_name]
            else:
                raise CommandError(f'Unknown table: {table_name}')

        total_migrated = 0

        for table in migration_order:
            file_path = os.path.join(export_dir, f'{table}.{file_format}')
            
            if not os.path.exists(file_path):
                self.stdout.write(f'Skipping {table} - file not found: {file_path}')
                continue

            try:
                count = self.migrate_table(table, file_path, file_format, dry_run)
                total_migrated += count
                self.stdout.write(
                    self.style.SUCCESS(f'Migrated {count} records from {table}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to migrate {table}: {str(e)}')
                )
                if not dry_run:
                    raise

        self.stdout.write(
            self.style.SUCCESS(f'Migration complete! Total records migrated: {total_migrated}')
        )

    def load_data_file(self, file_path: str, file_format: str) -> List[Dict[str, Any]]:
        """Load data from export file"""
        if file_format == 'json':
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif file_format == 'csv':
            with open(file_path, 'r', encoding='utf-8') as f:
                return list(csv.DictReader(f))
        else:
            raise CommandError(f'Unsupported file format: {file_format}')

    def migrate_table(self, table_name: str, file_path: str, file_format: str, dry_run: bool) -> int:
        """Migrate specific table data"""
        data = self.load_data_file(file_path, file_format)
        
        if not data:
            return 0

        migration_method = getattr(self, f'migrate_{table_name}', None)
        if not migration_method:
            self.stdout.write(f'No migration method for {table_name}, using generic migration')
            return self.migrate_generic(table_name, data, dry_run)

        return migration_method(data, dry_run)

    def safe_parse_datetime(self, value: Any) -> Optional[datetime]:
        """Safely parse datetime string"""
        if not value:
            return None
        if isinstance(value, str):
            parsed = parse_datetime(value)
            if parsed and parsed.tzinfo is None:
                parsed = parsed.replace(tzinfo=timezone.utc)
            return parsed
        return value

    def safe_parse_date(self, value: Any):
        """Safely parse date string"""
        if not value:
            return None
        if isinstance(value, str):
            return parse_date(value)
        return value

    def safe_decimal(self, value: Any) -> Optional[Decimal]:
        """Safely convert to Decimal"""
        if not value:
            return None
        try:
            return Decimal(str(value))
        except Exception:
            return None

    # Table-specific migration methods
    
    def migrate_users(self, data: List[Dict], dry_run: bool) -> int:
        """Migrate users table"""
        count = 0
        for row in data:
            if dry_run:
                self.stdout.write(f'Would migrate user: {row.get("email", "unknown")}')
                count += 1
                continue

            try:
                with transaction.atomic():
                    user, created = User.objects.update_or_create(
                        email=row['email'],
                        defaults={
                            'username': row.get('username', row['email']),
                            'first_name': row.get('first_name', ''),
                            'last_name': row.get('last_name', ''),
                            'role': row.get('role', 'Utility Coordinator'),
                            'avatar_url': row.get('avatar_url'),
                            'unit_preference': row.get('unit_preference', 'imperial'),
                            'custom_settings': row.get('custom_settings', {}),
                            'is_active': row.get('is_active', True),
                            'reset_password_on_login': row.get('reset_password_on_login', False),
                            'is_admin': row.get('is_admin', False),
                            'created_by_saml': row.get('created_by_saml', False),
                            'dashboard_layout': row.get('dashboard_layout', {}),
                            'ui_preferences': row.get('ui_preferences', {}),
                            'created_at': self.safe_parse_datetime(row.get('created_at')),
                            'updated_at': self.safe_parse_datetime(row.get('updated_at')),
                        }
                    )
                    count += 1
            except Exception as e:
                self.stdout.write(f'Error migrating user {row.get("email")}: {e}')

        return count

    def migrate_chat_messages(self, data: List[Dict], dry_run: bool) -> int:
        """Migrate chat_messages table"""
        count = 0
        for row in data:
            if dry_run:
                self.stdout.write(f'Would migrate chat message from user {row.get("user_id")}')
                count += 1
                continue

            try:
                with transaction.atomic():
                    user = User.objects.get(id=row['user_id'])
                    
                    ChatMessage.objects.create(
                        user=user,
                        content=row['content'],
                        channel='general',  # Default channel for migrated messages
                        timestamp=self.safe_parse_datetime(row.get('timestamp')),
                        created_at=self.safe_parse_datetime(row.get('created_at')),
                        updated_at=self.safe_parse_datetime(row.get('updated_at')),
                    )
                    count += 1
            except Exception as e:
                self.stdout.write(f'Error migrating chat message: {e}')

        return count

    def migrate_team_messages(self, data: List[Dict], dry_run: bool) -> int:
        """Migrate team_messages into chat_messages with team channel"""
        count = 0
        for row in data:
            if dry_run:
                self.stdout.write(f'Would migrate team message from user {row.get("user_id")}')
                count += 1
                continue

            try:
                with transaction.atomic():
                    # team_messages uses UUID user_id that references user_profiles
                    # We need to find the corresponding user in our users table
                    # For now, skip team_messages as they use a different user reference system
                    self.stdout.write('Skipping team message - UUID user reference not supported yet')
                    continue
                    
            except Exception as e:
                self.stdout.write(f'Error migrating team message: {e}')

        return count

    def migrate_temporary_messages(self, data: List[Dict], dry_run: bool) -> int:
        """Migrate TemporaryMessage table to WhisperMessage"""
        count = 0
        for row in data:
            if dry_run:
                self.stdout.write(f'Would migrate whisper from {row.get("fromUserId")} to {row.get("toUserId")}')
                count += 1
                continue

            try:
                with transaction.atomic():
                    from_user = User.objects.get(id=row['fromUserId'])
                    to_user = User.objects.get(id=row['toUserId'])
                    
                    WhisperMessage.objects.create(
                        from_user=from_user,
                        to_user=to_user,
                        message=row['message'],
                        created_at=self.safe_parse_datetime(row.get('createdAt')),
                        expires_at=self.safe_parse_datetime(row['expiresAt']),
                    )
                    count += 1
            except Exception as e:
                self.stdout.write(f'Error migrating whisper message: {e}')

        return count

    def migrate_projects(self, data: List[Dict], dry_run: bool) -> int:
        """Migrate projects table"""
        count = 0
        for row in data:
            if dry_run:
                self.stdout.write(f'Would migrate project: {row.get("name", "unknown")}')
                count += 1
                continue

            try:
                with transaction.atomic():
                    # Handle template reference
                    template = None
                    if row.get('template_id'):
                        try:
                            template = ProjectTemplate.objects.get(id=row['template_id'])
                        except ProjectTemplate.DoesNotExist:
                            pass

                    project, created = Project.objects.update_or_create(
                        id=row['id'],
                        defaults={
                            'monday_id': row.get('monday_id'),
                            'name': row['name'],
                            'client': row['client'],
                            'description': row.get('description'),
                            'start_date': self.safe_parse_date(row.get('start_date')),
                            'end_date': self.safe_parse_date(row.get('end_date')),
                            'manager_id': row.get('manager_id'),
                            'high_priority_items': row.get('high_priority_items'),
                            'medium_priority_items': row.get('medium_priority_items'),
                            'low_priority_items': row.get('low_priority_items'),
                            'created_at': self.safe_parse_datetime(row.get('created_at')),
                            'updated_at': self.safe_parse_datetime(row.get('updated_at')),
                            'template': template,
                            # Add other project fields...
                        }
                    )
                    count += 1
            except Exception as e:
                self.stdout.write(f'Error migrating project {row.get("name")}: {e}')

        return count

    def migrate_generic(self, table_name: str, data: List[Dict], dry_run: bool) -> int:
        """Generic migration for tables without specific migration methods"""
        if dry_run:
            self.stdout.write(f'Would migrate {len(data)} records from {table_name}')
            return len(data)
        
        self.stdout.write(f'Generic migration not implemented for {table_name}')
        return 0