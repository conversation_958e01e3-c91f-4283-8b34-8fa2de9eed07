"""
Django management command to consolidate messaging data after Supabase migration.

This command handles the consolidation of chat_messages and team_messages tables
into the new unified ChatMessage model with channel-based organization.

Usage:
    python manage.py consolidate_messaging_data
    python manage.py consolidate_messaging_data --dry-run
    python manage.py consolidate_messaging_data --source-schema supabase_import
"""

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.core.management.color import Style
from django.db import connection, transaction
from CLEAR.models import ChatMessage

User = get_user_model()


class Command(BaseCommand):
    help = 'Consolidate chat_messages and team_messages into unified ChatMessage model'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes'
        )
        parser.add_argument(
            '--source-schema',
            type=str,
            default='public',
            help='Source schema containing Supabase tables (default: public)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Number of records to process per batch (default: 1000)'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        source_schema = options['source_schema']
        batch_size = options['batch_size']

        if dry_run:
            self.stdout.write(getattr(self.style, "WARNING", "")('DRY RUN MODE - No changes will be made'))

        self.stdout.write('Starting messaging data consolidation...')

        # Step 1: Migrate chat_messages to general channel
        chat_count = self.migrate_chat_messages(source_schema, dry_run, batch_size)
        
        # Step 2: Migrate team_messages to team channel  
        team_count = self.migrate_team_messages(source_schema, dry_run, batch_size)

        total_migrated = chat_count + team_count

        self.stdout.write(
            getattr(self.style, "SUCCESS", "")(
                f'Consolidation complete! Migrated {total_migrated} messages '
                f'({chat_count} chat + {team_count} team)'
            )
        )

        # Step 3: Cleanup old tables (if not dry run)
        if not dry_run:
            self.cleanup_old_tables(source_schema)

    def migrate_chat_messages(self, source_schema: str, dry_run: bool, batch_size: int) -> int:
        """Migrate chat_messages to ChatMessage with general channel"""
        
        # Check if source table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'chat_messages'
                );
            """, [source_schema])
            
            if not cursor.fetchone()[0]:
                self.stdout.write('No chat_messages table found, skipping...')
                return 0

            # Count total records
            # Use identifier quoting for schema/table names
            cursor.execute(
                f'SELECT COUNT(*) FROM "{source_schema}"."chat_messages"'
            )
            total_count = cursor.fetchone()[0]

        if total_count == 0:
            self.stdout.write('No chat messages to migrate')
            return 0

        self.stdout.write(f'Migrating {total_count} chat messages...')

        if dry_run:
            self.stdout.write(f'Would migrate {total_count} chat messages to general channel')
            return total_count

        migrated_count = 0
        offset = 0

        while offset < total_count:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Fetch batch of chat messages
                    cursor.execute("""
                        SELECT cm.id, cm.user_id, cm.content, cm.timestamp, 
                               cm.created_at, cm.updated_at
                        FROM public.chat_messages cm
                        ORDER BY cm.id
                        LIMIT %s OFFSET %s
                    """, [batch_size, offset])

                    batch_messages = []
                    for row in cursor.fetchall():
                        msg_id, user_id, content, timestamp, created_at, updated_at = row
                        
                        try:
                            # Get Django user (assuming user IDs match)
                            user = User.objects.get(id=user_id)
                            
                            batch_messages.append(ChatMessage(
                                user=user,
                                content=content,
                                channel='general',
                                project=None,  # General chat not tied to specific project
                                timestamp=timestamp,
                                created_at=created_at,
                                updated_at=updated_at,
                            ))
                        except User.DoesNotExist:
                            self.stdout.write(f'Warning: User {user_id} not found, skipping message {msg_id}')
                            continue

                    # Bulk create batch
                    if batch_messages:
                        ChatMessage.objects.bulk_create(batch_messages)
                        migrated_count += len(batch_messages)

                self.stdout.write(f'Migrated {migrated_count}/{total_count} chat messages')
                offset += batch_size

        return migrated_count

    def migrate_team_messages(self, source_schema: str, dry_run: bool, batch_size: int) -> int:
        """Migrate team_messages to ChatMessage with team channel"""
        
        # Check if source table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'team_messages'
                );
            """, [source_schema])
            
            if not cursor.fetchone()[0]:
                self.stdout.write('No team_messages table found, skipping...')
                return 0

            # Count total records
            cursor.execute('SELECT COUNT(*) FROM "{}"."team_messages"'.format(source_schema))
            total_count = cursor.fetchone()[0]

        if total_count == 0:
            self.stdout.write('No team messages to migrate')
            return 0

        self.stdout.write(f'Migrating {total_count} team messages...')

        if dry_run:
            self.stdout.write(f'Would migrate {total_count} team messages to team channel')
            return total_count

        migrated_count = 0
        offset = 0

        while offset < total_count:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Fetch batch of team messages with user profile lookup
                    cursor.execute("""
                        SELECT tm.id, tm.user_id, tm.content, tm.created_at, tm.updated_at,
                               up.email
                        FROM public.team_messages tm
                        LEFT JOIN public.user_profiles up ON tm.user_id = up.id
                        ORDER BY tm.id
                        LIMIT %s OFFSET %s
                    """, [batch_size, offset])

                    batch_messages = []
                    for row in cursor.fetchall():
                        msg_id, user_id, content, created_at, updated_at, email = row
                        
                        try:
                            # Find Django user by email since team_messages use UUID refs
                            if email:
                                user = User.objects.get(email=email)
                            else:
                                self.stdout.write(f'Warning: No email found for team message {msg_id}, skipping')
                                continue
                            
                            batch_messages.append(ChatMessage(
                                user=user,
                                content=content,
                                channel='team',
                                project=None,  # Team chat not tied to specific project
                                timestamp=created_at,  # team_messages doesn't have separate timestamp
                                created_at=created_at,
                                updated_at=updated_at,
                            ))
                        except User.DoesNotExist:
                            self.stdout.write(f'Warning: User with email {email} not found, skipping message {msg_id}')
                            continue

                    # Bulk create batch
                    if batch_messages:
                        ChatMessage.objects.bulk_create(batch_messages)
                        migrated_count += len(batch_messages)

                self.stdout.write(f'Migrated {migrated_count}/{total_count} team messages')
                offset += batch_size

        return migrated_count

    def cleanup_old_tables(self, source_schema: str):
        """Optionally cleanup old messaging tables after successful migration"""
        
        self.stdout.write('Cleaning up old messaging tables...')
        
        cleanup_commands = [
            f'DROP TABLE IF EXISTS {source_schema}.chat_messages_backup;',
            f'ALTER TABLE {source_schema}.chat_messages RENAME TO chat_messages_backup;',
            f'DROP TABLE IF EXISTS {source_schema}.team_messages_backup;', 
            f'ALTER TABLE {source_schema}.team_messages RENAME TO team_messages_backup;',
        ]

        with connection.cursor() as cursor:
            for cmd in cleanup_commands:
                try:
                    cursor.execute(cmd)
                    self.stdout.write(f'Executed: {cmd}')
                except Exception as e:
                    self.stdout.write(f'Warning: Failed to execute {cmd}: {e}')

        self.stdout.write('Cleanup complete. Old tables renamed to *_backup.')