"""
Management command to toggle HTMX endpoints feature flag.

Usage:
    python manage.py toggle_htmx_endpoints --enable
    python manage.py toggle_htmx_endpoints --disable
    python manage.py toggle_htmx_endpoints --status
"""

from django.core.management.base import BaseCommand
from CLEAR.feature_flags import FeatureFlags


class Command(BaseCommand):
    help = 'Toggle the USE_HTMX_ENDPOINTS feature flag'

    def add_arguments(self, parser):
        group = parser.add_mutually_exclusive_group(required=True)
        group.add_argument(
            '--enable',
            action='store_true',
            help='Enable HTMX endpoints (disable JSON APIs)'
        )
        group.add_argument(
            '--disable', 
            action='store_true',
            help='Disable HTMX endpoints (enable JSON APIs)'
        )
        group.add_argument(
            '--status',
            action='store_true',
            help='Show current status of feature flags'
        )
        
        parser.add_argument(
            '--timeout',
            type=int,
            default=3600,
            help='Cache timeout in seconds (default: 3600)'
        )

    def handle(self, *args, **options):
        if options['status']:
            self.show_status()
        elif options['enable']:
            self.enable_htmx(options['timeout'])
        elif options['disable']:
            self.disable_htmx(options['timeout'])

    def show_status(self):
        """Show current status of all feature flags."""
        self.stdout.write(
            self.style.SUCCESS('Current Feature Flag Status:')
        )
        
        flags = FeatureFlags.get_all_flags()
        for flag_name, enabled in flags.items():
            status = self.style.SUCCESS('ENABLED') if enabled else self.style.ERROR('DISABLED')
            self.stdout.write(f'  {flag_name}: {status}')
        
        # Show specific HTMX status
        htmx_enabled = FeatureFlags.is_enabled('USE_HTMX_ENDPOINTS')
        self.stdout.write('')
        if htmx_enabled:
            self.stdout.write(
                self.style.SUCCESS('✅ HTMX endpoints are ACTIVE')
            )
            self.stdout.write('   All /htmx/api/* endpoints will serve HTML fragments')
        else:
            self.stdout.write(
                self.style.WARNING('❌ HTMX endpoints are INACTIVE')
            )
            self.stdout.write('   All /api/* endpoints will serve JSON responses')

    def enable_htmx(self, timeout):
        """Enable HTMX endpoints."""
        FeatureFlags.set_flag('USE_HTMX_ENDPOINTS', True, timeout)
        
        self.stdout.write(
            self.style.SUCCESS('✅ HTMX endpoints ENABLED')
        )
        self.stdout.write(f'   Cache timeout: {timeout} seconds')
        self.stdout.write('')
        self.stdout.write('HTMX endpoints are now active:')
        self.stdout.write('  • /htmx/api/users/ - User management')
        self.stdout.write('  • /htmx/api/utilities/ - Utility data')
        self.stdout.write('  • /htmx/api/conflicts/ - Conflict management')
        self.stdout.write('  • /htmx/api/documents/ - Document handling')
        self.stdout.write('  • /htmx/api/projects/ - Project management')
        self.stdout.write('  • /htmx/api/tasks/ - Task management')
        self.stdout.write('  • ... and 75+ other endpoints')
        self.stdout.write('')
        self.stdout.write(
            self.style.WARNING('NOTE: Update frontend to use HTMX endpoints for full benefits')
        )

    def disable_htmx(self, timeout):
        """Disable HTMX endpoints."""
        FeatureFlags.set_flag('USE_HTMX_ENDPOINTS', False, timeout)
        
        self.stdout.write(
            self.style.SUCCESS('❌ HTMX endpoints DISABLED')
        )
        self.stdout.write(f'   Cache timeout: {timeout} seconds')
        self.stdout.write('')
        self.stdout.write('JSON API endpoints are now active:')
        self.stdout.write('  • /api/projects/ - Project ViewSet')
        self.stdout.write('  • /api/utilities/ - Utility ViewSet')
        self.stdout.write('  • /api/conflicts/ - Conflict ViewSet')
        self.stdout.write('  • /api/tasks/ - Task ViewSet')
        self.stdout.write('  • ... and other DRF ViewSets')
        self.stdout.write('')
        self.stdout.write(
            self.style.WARNING('NOTE: Frontend will continue using JSON API responses')
        )