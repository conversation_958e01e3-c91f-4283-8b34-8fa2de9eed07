"""
Django management command to complete the full CLEAR platform migration.

This command orchestrates the complete migration process from Supabase to Django,
including schema setup, data migration, consolidation, and validation.

Usage:
    python manage.py complete_migration --supabase-url "postgresql://..." 
    python manage.py complete_migration --supabase-url "postgresql://..." --dry-run
    python manage.py complete_migration --help
"""

import os
import subprocess
from datetime import datetime
from django.core.management import call_command
from django.core.management.color import Style
from django.core.management.base import BaseCommand, CommandError
from django.db import connection
import shutil
from CLEAR.models import (
    ChatMessage,
    Conflict,
    Organization,
    Project,
    Task,
    User,
    Utility,
    WhisperMessage,
)


class Command(BaseCommand):
    help = 'Complete migration from Supabase to Django HTMX platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--supabase-url',
            type=str,
            required=True,
            help='Supabase PostgreSQL connection URL'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate migration without making changes'
        )
        parser.add_argument(
            '--skip-export',
            action='store_true',
            help='Skip Supabase export (use existing files)'
        )
        parser.add_argument(
            '--export-dir',
            type=str,
            default='./migration_exports',
            help='Directory for export files (default: ./migration_exports)'
        )

    def handle(self, *args, **options):
        supabase_url = options['supabase_url']
        dry_run = options['dry_run']
        skip_export = options['skip_export']
        export_dir = options['export_dir']

        # Validation
        if not self.validate_prerequisites():
            raise CommandError('Prerequisites not met')

        self.stdout.write(
            getattr(self.style, "SUCCESS", "")('🚀 Starting CLEAR Platform Migration')
        )
        self.stdout.write('=' * 60)

        if dry_run:
            self.stdout.write(getattr(self.style, "WARNING", "")('DRY RUN MODE - No changes will be made'))

        # Step 1: Setup Django schema
        self.stdout.write('\n📋 Step 1: Setting up Django schema...')
        if not dry_run:
            self.setup_django_schema()
        else:
            self.stdout.write('Would run Django migrations')

        # Step 2: Export from Supabase
        if not skip_export:
            self.stdout.write('\n📤 Step 2: Exporting data from Supabase...')
            if not dry_run:
                self.export_supabase_data(supabase_url, export_dir)
            else:
                self.stdout.write(f'Would export Supabase data to {export_dir}')

        # Step 3: Import to temporary schema
        self.stdout.write('\n📥 Step 3: Importing to temporary schema...')
        if not dry_run:
            self.import_to_temp_schema(export_dir)
        else:
            self.stdout.write('Would import data to temporary schema')

        # Step 4: Transform and consolidate data
        self.stdout.write('\n🔄 Step 4: Transforming and consolidating data...')
        self.consolidate_data(dry_run)

        # Step 5: Validation
        self.stdout.write('\n✅ Step 5: Validating migration...')
        self.validate_migration(dry_run)

        # Step 6: Cleanup
        self.stdout.write('\n🧹 Step 6: Cleanup...')
        if not dry_run:
            self.cleanup_migration(export_dir)
        else:
            self.stdout.write('Would cleanup temporary files')

        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(
            getattr(self.style, "SUCCESS", "")('🎉 Migration Complete!')
        )
        self.show_migration_summary()

    def validate_prerequisites(self) -> bool:
        """Validate that all prerequisites are met"""
        prerequisites = [
            ('pg_dump', 'PostgreSQL client tools'),
            ('psql', 'PostgreSQL client tools'),
        ]

        missing = []
        for cmd, description in prerequisites:
            if subprocess.run(['which', cmd], capture_output=True).returncode != 0:
                missing.append(f'{cmd} ({description})')

        if missing:
            self.stdout.write(
                getattr(self.style, "ERROR", "")('Missing prerequisites:')
            )
            for item in missing:
                self.stdout.write(f'  - {item}')
            return False

        return True

    def setup_django_schema(self):
        """Run Django migrations to set up the schema"""
        self.stdout.write('  Running Django migrations...')
        
        try:
            call_command('makemigrations', 'CLEAR', verbosity=0)
            call_command('migrate', verbosity=0)
            self.stdout.write('  ✓ Django schema created')
        except Exception as e:
            raise CommandError(f'Failed to setup Django schema: {e}')

    def export_supabase_data(self, supabase_url: str, export_dir: str):
        """Export data from Supabase"""
        os.makedirs(export_dir, exist_ok=True)

        # Core tables to export
        table_groups = {
            'users': ['users', 'user_profiles'],
            'projects': ['projects', 'project_templates', 'stakeholders'],
            'utilities': ['utilities', 'conflicts', 'coordinate_systems', 'line_styles'],
            'tasks': ['tasks', 'comments', 'notifications'],
            'messaging': ['chat_messages', 'team_messages', 'temporary_messages'],
        }

        for group_name, tables in table_groups.items():
            self.stdout.write(f'  Exporting {group_name}...')
            
            export_file = os.path.join(export_dir, f'{group_name}.sql')
            
            # Build pg_dump command
            cmd = [
                'pg_dump', supabase_url,
                '--schema=public',
                '--data-only',
                '--inserts',
                '--file=' + export_file
            ]
            
            # Add table specifications
            for table in tables:
                cmd.extend(['--table', table])

            try:
                subprocess.run(cmd, check=True, capture_output=True)
                self.stdout.write(f'  ✓ Exported {group_name} to {export_file}')
            except subprocess.CalledProcessError as e:
                self.stdout.write(f'  ⚠ Warning: Failed to export {group_name}: {e}')

    def import_to_temp_schema(self, export_dir: str):
        """Import exported data to temporary schema"""
        self.stdout.write('  Creating temporary schema...')
        
        with connection.cursor() as cursor:
            cursor.execute('CREATE SCHEMA IF NOT EXISTS supabase_import;')

        # Import each export file
        for filename in os.listdir(export_dir):
            if filename.endswith('.sql'):
                filepath = os.path.join(export_dir, filename)
                self.stdout.write(f'  Importing {filename}...')
                
                try:
                    # Read SQL file and replace schema references
                    with open(filepath, 'r') as f:
                        sql_content = f.read()
                    
                    # Replace public schema with supabase_import
                    sql_content = sql_content.replace('public.', 'supabase_import.')
                    
                    # Execute modified SQL
                    with connection.cursor() as cursor:
                        cursor.execute(sql_content)
                    
                    self.stdout.write(f'  ✓ Imported {filename}')
                except Exception as e:
                    self.stdout.write(f'  ⚠ Warning: Failed to import {filename}: {e}')

    def consolidate_data(self, dry_run: bool):
        """Run data consolidation commands"""
        consolidation_commands = [
            ('consolidate_messaging_data', 'Consolidating messaging data'),
            ('migrate_whispers', 'Migrating whisper messages'),
        ]

        for cmd, description in consolidation_commands:
            self.stdout.write(f'  {description}...')
            
            try:
                call_command(
                    cmd,
                    source_schema='supabase_import',
                    dry_run=dry_run,
                    verbosity=0
                )
                self.stdout.write(f'  ✓ {description} complete')
            except Exception as e:
                self.stdout.write(f'  ⚠ Warning: {description} failed: {e}')

    def validate_migration(self, dry_run: bool):
        """Validate the migrated data"""
        if dry_run:
            self.stdout.write('  Would validate migrated data')
            return

        validation_checks = [
            ('Users', User.objects.count()),
            ('Projects', Project.objects.count()),
            ('Utilities', Utility.objects.count()),
            ('Conflicts', Conflict.objects.count()),
            ('Tasks', Task.objects.count()),
            ('Chat Messages', ChatMessage.objects.count()),
            ('Whisper Messages', WhisperMessage.objects.count()),
        ]

        self.stdout.write('  Data counts after migration:')
        for item, count in validation_checks:
            status = '✓' if count > 0 else '⚠'
            self.stdout.write(f'    {status} {item}: {count}')

        # Check for referential integrity
        self.stdout.write('  Checking referential integrity...')
        
        integrity_checks = [
            ('Projects with missing managers', 
             Project.objects.filter(egis_project_manager__isnull=True).count()),
            ('Chat messages without users', 
             ChatMessage.objects.filter(user__isnull=True).count()),
            ('Tasks without projects', 
             Task.objects.filter(project__isnull=True).count()),
        ]

        issues_found = 0
        for check_name, issue_count in integrity_checks:
            if issue_count > 0:
                self.stdout.write(f'    ⚠ {check_name}: {issue_count}')
                issues_found += 1
            else:
                self.stdout.write(f'    ✓ {check_name}: OK')

        if issues_found == 0:
            self.stdout.write('  ✓ All integrity checks passed')
        else:
            self.stdout.write(f'  ⚠ {issues_found} integrity issues found')

    def cleanup_migration(self, export_dir: str):
        """Cleanup temporary files and schema"""
        # Prompt before cleanup
        response = input('  Remove temporary import schema and export files? (y/N): ')
        
        if response.lower() == 'y':
            # Remove temporary schema
            with connection.cursor() as cursor:
                cursor.execute('DROP SCHEMA IF EXISTS supabase_import CASCADE;')
            
            # Remove export directory
            if os.path.exists(export_dir):
                shutil.rmtree(export_dir)
            
            self.stdout.write('  ✓ Cleanup complete')
        else:
            self.stdout.write('  ℹ Temporary data retained:')
            self.stdout.write('    - Schema: supabase_import')
            self.stdout.write(f'    - Files: {export_dir}')

    def show_migration_summary(self):
        """Show migration summary and next steps"""
        self.stdout.write('\n📊 Migration Summary:')
        
        try:
            summary_stats = [
                ('Total Users', User.objects.count()),
                ('Total Projects', Project.objects.count()),
                ('Total Messages', ChatMessage.objects.count() + WhisperMessage.objects.count()),
                ('Organizations', Organization.objects.count()),
            ]

            for label, count in summary_stats:
                self.stdout.write(f'  {label}: {count}')

        except Exception as e:
            self.stdout.write(f'  Unable to generate summary: {e}')

        self.stdout.write('\n🚀 Next Steps:')
        self.stdout.write('  1. Test Django application: python manage.py runserver')
        self.stdout.write('  2. Create superuser: python manage.py createsuperuser')
        self.stdout.write('  3. Access admin panel: /admin/')
        self.stdout.write('  4. Test messaging features')
        self.stdout.write('  5. Deploy to production')
        
        # Save migration log
        log_filename = f'migration_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
        self.stdout.write(f'\n📝 Migration log saved to: {log_filename}')