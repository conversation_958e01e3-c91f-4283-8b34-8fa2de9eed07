"""
Management command to create test notifications for development and testing.
"""

from CLEAR.models import Notification

"""




User = get_user_model()


class Command(BaseCommand):
    help = 'Create test notifications for a user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Username to create notifications for',
            required=True
        )
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of notifications to create (default: 5)'
        )
        parser.add_argument(
            '--type',
            type=str,
            help='Specific notification type to create'
        )

    def handle(self, *args, **options):
        username = options['username']
        count = options['count']
        specific_type = options.get('type')

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'User "{username}" not found'))
            return

        notification_types = [
            ('message', 'New Message', 'You have a new message from {sender}'),
            ('mention', 'Mentioned in Comment', '{sender} mentioned you in a comment'),
            ('comment', 'New Comment', 'New comment on your project'),
            ('task_assigned', 'Task Assigned', 'You have been assigned to task: {task}'),
            ('task_due', 'Task Due Soon', 'Task "{task}" is due in 2 hours'),
            ('document_shared', 'Document Shared', '{sender} shared a document with you'),
            ('project_updated', 'Project Updated', 'Project "{project}" has been updated'),
            ('conflict_detected', 'Conflict Detected', 'New spatial conflict detected in {project}'),
            ('reminder', 'Reminder', "Don't forget about your meeting at 3 PM"),
            ('approval_needed', 'Approval Required', 'Your approval is needed for {item}'),
            ('system', 'System Update', 'System maintenance scheduled for tonight'),
        ]

        if specific_type:
            notification_types = [(t, title, msg) for t, title, msg in notification_types if t == specific_type]
            if not notification_types:
                self.stdout.write(self.style.ERROR(f'Invalid notification type: {specific_type}'))
                return

        created_count = 0
        for i in range(count):
            notif_type, title_template, message_template = random.choice(notification_types)
            
            # Create dynamic content
            context = {
                'sender': random.choice(['John Doe', 'Jane Smith', 'Bob Johnson']),
                'task': f'Task #{random.randint(100, 999)}',
                'project': f'Project {random.choice(["Alpha", "Beta", "Gamma"])}',
                'item': f'Invoice #{random.randint(1000, 9999)}'
            }
            
            title = title_template
            message = message_template.format(**context)
            
            # Random priority
            priority = random.choice(['low', 'normal', 'high', 'urgent'])
            
            # Create notification
            Notification.objects.create(
                recipient=user,
                notification_type=notif_type,
                title=title,
                message=message,
                priority=priority,
                action_url=f'/projects/{random.randint(1, 10)}/' if random.random() > 0.5 else None,
                action_label='View' if random.random() > 0.5 else None,
            )
            
            created_count += 1
            self.stdout.write(f'Created {notif_type} notification: {title}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} notifications for user "{username}"'
            )
        )
"""