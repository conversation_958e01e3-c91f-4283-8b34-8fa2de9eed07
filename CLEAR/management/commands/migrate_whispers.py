"""
Django management command to migrate temporary_messages to WhisperMessage format.

This command transforms the Supabase temporary_messages table into the new
WhisperMessage model with enhanced features.

Usage:
    python manage.py migrate_whispers
    python manage.py migrate_whispers --dry-run
    python manage.py migrate_whispers --source-schema supabase_import
"""

from datetime import datetime, timedelta, timezone
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import connection, transaction
from CLEAR.models import WhisperMessage
from django.db.models import Count


User = get_user_model()


class Command(BaseCommand):
    help = 'Migrate temporary_messages to WhisperMessage model with enhanced features'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes'
        )
        parser.add_argument(
            '--source-schema',
            type=str,
            default='public',
            help='Source schema containing Supabase tables (default: public)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=500,
            help='Number of records to process per batch (default: 500)'
        )
        parser.add_argument(
            '--extend-expiry',
            type=int,
            default=0,
            help='Hours to add to expiry times (for migration buffer)'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        source_schema = options['source_schema']
        batch_size = options['batch_size']
        extend_expiry = options['extend_expiry']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        self.stdout.write('Starting whisper messages migration...')

        # Migrate temporary_messages to WhisperMessage
        migrated_count = self.migrate_temporary_messages(
            source_schema, dry_run, batch_size, extend_expiry
        )

        self.stdout.write(
            self.style.SUCCESS(f'Migration complete! Migrated {migrated_count} whisper messages')
        )

        # Show statistics about migrated whispers
        if not dry_run and migrated_count > 0:
            self.show_whisper_statistics()

    def migrate_temporary_messages(self, source_schema: str, dry_run: bool, 
                                 batch_size: int, extend_expiry: int) -> int:
        """Migrate temporary_messages to WhisperMessage"""
        
        # Check if source table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'temporary_messages'
                );
            """, [source_schema])
            
            if not cursor.fetchone()[0]:
                self.stdout.write('No temporary_messages table found, skipping...')
                return 0

            # Count total records (excluding already expired messages)
            cursor.execute(f"""
                SELECT COUNT(*) FROM {source_schema}.temporary_messages 
                WHERE expires_at > NOW()
            """)
            total_count = cursor.fetchone()[0]

            # Also count expired messages for information
            cursor.execute(f"""
                SELECT COUNT(*) FROM {source_schema}.temporary_messages 
                WHERE expires_at <= NOW()
            """)
            expired_count = cursor.fetchone()[0]

        if expired_count > 0:
            self.stdout.write(f'Found {expired_count} expired messages (will be skipped)')

        if total_count == 0:
            self.stdout.write('No active temporary messages to migrate')
            return 0

        self.stdout.write(f'Migrating {total_count} active whisper messages...')

        if dry_run:
            # Show sample of what would be migrated
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT tm.id, tm.from_user_id, tm.to_user_id, tm.message, 
                           tm.created_at, tm.expires_at,
                           u1.email as from_email, u2.email as to_email
                    FROM {source_schema}.temporary_messages tm
                    LEFT JOIN {source_schema}.users u1 ON tm.from_user_id = u1.id
                    LEFT JOIN {source_schema}.users u2 ON tm.to_user_id = u2.id
                    WHERE tm.expires_at > NOW()
                    ORDER BY tm.created_at DESC
                    LIMIT 5
                """)
                
                sample_messages = cursor.fetchall()
                self.stdout.write('\nSample whisper messages to migrate:')
                for row in sample_messages:
                    msg_id, from_id, to_id, message, created_at, expires_at, from_email, to_email = row
                    self.stdout.write(
                        f'  ID: {msg_id}, From: {from_email}, To: {to_email}, '
                        f'Created: {created_at}, Expires: {expires_at}'
                    )
                    self.stdout.write(f'    Message: {message[:50]}...')

            return total_count

        migrated_count = 0
        offset = 0

        while offset < total_count:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Fetch batch of active temporary messages
                    cursor.execute(f"""
                        SELECT tm.id, tm.from_user_id, tm.to_user_id, tm.message, 
                               tm.created_at, tm.expires_at
                        FROM {source_schema}.temporary_messages tm
                        WHERE tm.expires_at > NOW()
                        ORDER BY tm.id
                        LIMIT %s OFFSET %s
                    """, [batch_size, offset])

                    batch_whispers = []
                    for row in cursor.fetchall():
                        msg_id, from_user_id, to_user_id, message, created_at, expires_at = row
                        
                        try:
                            # Get Django users
                            from_user = User.objects.get(id=from_user_id)
                            to_user = User.objects.get(id=to_user_id)
                            
                            # Extend expiry if requested
                            if extend_expiry > 0:
                                expires_at = expires_at + timedelta(hours=extend_expiry)
                            
                            batch_whispers.append(WhisperMessage(
                                from_user=from_user,
                                to_user=to_user,
                                message=message,
                                created_at=created_at,
                                expires_at=expires_at,
                                read_at=None,  # New field - not available in original
                            ))
                        except User.DoesNotExist:
                            self.stdout.write(
                                f'Warning: User not found for message {msg_id} '
                                f'(from_user_id: {from_user_id}, to_user_id: {to_user_id}), skipping'
                            )
                            continue

                    # Bulk create batch
                    if batch_whispers:
                        WhisperMessage.objects.bulk_create(batch_whispers)
                        migrated_count += len(batch_whispers)

                self.stdout.write(f'Migrated {migrated_count}/{total_count} whisper messages')
                offset += batch_size

        return migrated_count

    def show_whisper_statistics(self):
        """Show statistics about migrated whisper messages"""
        
        total_whispers = WhisperMessage.objects.count()
        
        # Count by expiry status
        now = datetime.now(timezone.utc)
        active_whispers = WhisperMessage.objects.filter(expires_at__gt=now).count()
        expired_whispers = WhisperMessage.objects.filter(expires_at__lte=now).count()
        
        # Count unread messages
        unread_whispers = WhisperMessage.objects.filter(read_at__isnull=True, expires_at__gt=now).count()
        
        # Find most active users
        top_senders = WhisperMessage.objects.values('from_user__username').annotate(
            count=Count('id')
        ).order_by('-count')[:5]
        
        self.stdout.write('\n--- Whisper Message Statistics ---')
        self.stdout.write(f'Total whisper messages: {total_whispers}')
        self.stdout.write(f'Active (not expired): {active_whispers}')
        self.stdout.write(f'Expired: {expired_whispers}')
        self.stdout.write(f'Unread active messages: {unread_whispers}')
        
        if top_senders:
            self.stdout.write('\nTop whisper senders:')
            for sender in top_senders:
                self.stdout.write(f'  {sender["from_user__username"]}: {sender["count"]} messages')
        
        # Show expiry timeline
        next_24h = now + timedelta(hours=24)
        next_week = now + timedelta(days=7)
        
        expiring_24h = WhisperMessage.objects.filter(
            expires_at__gt=now, expires_at__lte=next_24h
        ).count()
        
        expiring_week = WhisperMessage.objects.filter(
            expires_at__gt=next_24h, expires_at__lte=next_week
        ).count()
        
        self.stdout.write('\nExpiry timeline:')
        self.stdout.write(f'  Expiring in next 24 hours: {expiring_24h}')
        self.stdout.write(f'  Expiring in next week: {expiring_week}')
        
        if expired_whispers > 0:
            self.stdout.write(
                self.style.WARNING(
                    f'\nNote: {expired_whispers} expired messages found. '
                    'Run "python manage.py cleanup_whispers" to remove them.'
                )
            )