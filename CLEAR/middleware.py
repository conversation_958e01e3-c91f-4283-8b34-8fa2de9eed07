"""
Middleware for CLEAR application
"""

import json
import logging
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.signals import (
    user_logged_in,
    user_logged_out,
    user_login_failed,
)
from django.core.cache import cache
from django.dispatch import receiver
from django.http import HttpResponse
from django.utils import timezone
from .models import LoginAttempt, SecurityEvent

logger = logging.getLogger(__name__)
User = get_user_model()


class UserActivityMiddleware:
    """
    Middleware to track user activity for online status in team chat
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Update last_activity for authenticated users
        if request.user.is_authenticated:
            try:
                # Only update every 5 minutes to reduce database hits
                if (not request.user.last_activity or 
                    (timezone.now() - request.user.last_activity).total_seconds() > 300):
                    User.objects.filter(id=request.user.id).update(
                        last_activity=timezone.now()
                    )
            except Exception:
                # Silently fail to avoid breaking the request
                pass
        
        return response


class SecurityMiddleware:
    """
    Enhanced security middleware for authentication protection and monitoring
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Configuration
        self.max_login_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        self.suspicious_patterns = [
            'sql', 'script', 'javascript:', 'vbscript:', 'onload', 'onerror'
        ]

    def __call__(self, request):
        # Pre-request security checks
        if self.is_suspicious_request(request):
            logger.warning(f"Suspicious request detected from {self.get_client_ip(request)}: {request.path}")
        
        # Check for rate limiting on auth endpoints
        if request.path in ['/auth/login/', '/login/'] and request.method == 'POST':
            if self.is_rate_limited(request):
                messages.error(request, 'Too many login attempts. Please wait before trying again.')
                return HttpResponse("Rate limited", status=429)
        
        response = self.get_response(request)
        
        # Post-request security logging
        if response.status_code == 403:
            self.log_security_event(request, 'access_denied', {
                'status_code': response.status_code,
                'path': request.path
            })
        
        return response

    def get_client_ip(self, request):
        """Get the real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def is_suspicious_request(self, request):
        """Check for suspicious request patterns"""
        # Check for suspicious patterns in query parameters and form data
        query_string = request.GET.urlencode().lower()
        
        for pattern in self.suspicious_patterns:
            if pattern in query_string:
                return True
        
        # Check POST data if it exists
        if request.method == 'POST':
            try:
                post_data = str(request.POST).lower()
                for pattern in self.suspicious_patterns:
                    if pattern in post_data:
                        return True
            except Exception:
                pass
        
        return False

    def is_rate_limited(self, request):
        """Check if the request should be rate limited"""
        client_ip = self.get_client_ip(request)
        cache_key = f"login_attempts_{client_ip}"
        
        attempts = cache.get(cache_key, 0)
        if attempts >= self.max_login_attempts:
            return True
        
        return False

    def log_security_event(self, request, event_type, extra_data=None):
        """Log security events for monitoring"""
        event_data = {
            'event_type': event_type,
            'ip_address': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'path': request.path,
            'method': request.method,
            'timestamp': timezone.now().isoformat(),
            'user': request.user.username if request.user.is_authenticated else 'anonymous'
        }
        
        if extra_data:
            event_data.update(extra_data)
        
        logger.warning(f"Security Event: {json.dumps(event_data)}")


# Signal handlers for authentication events
@receiver(user_logged_in)
def log_successful_login(sender, request, user, **kwargs):
    """Log successful login attempts"""
    client_ip = get_client_ip_from_request(request)
    
    # Clear any existing rate limiting for this IP
    cache_key = f"login_attempts_{client_ip}"
    cache.delete(cache_key)
    
    # Log the successful login
    logger.info(f"Successful login for user {user.username} from IP {client_ip}")
    
    # Update user's login tracking
    user.last_login_at = timezone.now()
    user.last_activity = timezone.now()
    user.save(update_fields=['last_login_at', 'last_activity'])
    
    # Log security event
    try:
        SecurityEvent.log_event(
            event_type='login_success',
            ip_address=client_ip,
            user=user,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            session_key=request.session.session_key,
            request_path=request.path,
            request_method=request.method,
            success=True
        )
        
        # Log successful login attempt
        LoginAttempt.objects.create(
            username=user.username,
            ip_address=client_ip,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            success=True
        )
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log and track failed login attempts"""
    client_ip = get_client_ip_from_request(request)
    username = credentials.get('username', 'unknown')
    
    # Increment failed attempts counter
    cache_key = f"login_attempts_{client_ip}"
    attempts = cache.get(cache_key, 0) + 1
    cache.set(cache_key, attempts, 300)  # 5 minutes timeout
    
    # Log the failed attempt
    logger.warning(f"Failed login attempt for username '{username}' from IP {client_ip} (attempt #{attempts})")
    
    # Add warning messages based on attempt count
    if attempts >= 3:
        messages.warning(request, f"Multiple failed login attempts detected. {5 - attempts} attempts remaining before temporary lockout.")
    
    if attempts >= 5:
        logger.error(f"IP {client_ip} temporarily locked out after {attempts} failed attempts")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout events"""
    if user:
        client_ip = get_client_ip_from_request(request)
        logger.info(f"User {user.username} logged out from IP {client_ip}")


def get_client_ip_from_request(request):
    """Helper function to get client IP from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip