import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.7 on 2024-11-20 15:30




class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0015_auto_20250616_1915'),
    ]

    operations = [
        migrations.AddField(
            model_name='systemmetric',
            name='aggregation_period',
            field=models.CharField(choices=[('minute', 'Minute'), ('hour', 'Hour'), ('day', 'Day'), ('week', 'Week'), ('month', 'Month')], default='hour', max_length=20),
        ),
        migrations.AddField(
            model_name='systemmetric',
            name='metadata',
            field=models.JSONField(default=dict, help_text='Additional metric metadata for analytics'),
        ),
        migrations.AddField(
            model_name='systemmetric',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='CLEAR.organization'),
        ),
        migrations.AddField(
            model_name='systemmetric',
            name='project',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='CLEAR.project'),
        ),
        migrations.AddField(
            model_name='systemmetric',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='BusinessMetric',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('metric_type', models.CharField(choices=[('revenue', 'Revenue'), ('project_count', 'Project Count'), ('conflict_count', 'Conflict Count'), ('resolution_time', 'Conflict Resolution Time'), ('budget_savings', 'Budget Savings'), ('team_productivity', 'Team Productivity'), ('customer_satisfaction', 'Customer Satisfaction'), ('utilization_rate', 'Utilization Rate')], max_length=50)),
                ('value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('unit', models.CharField(blank=True, max_length=20, null=True)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('department', models.CharField(blank=True, max_length=50, null=True)),
                ('additional_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_metrics', to='CLEAR.organization')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='business_metrics', to='CLEAR.project')),
            ],
            options={
                'indexes': [models.Index(fields=['metric_type', '-period_start'], name='CLEAR_busin_metric__dd5a40_idx'), models.Index(fields=['organization', 'metric_type', '-period_start'], name='CLEAR_busin_organiz_fb5c76_idx'), models.Index(fields=['project', 'metric_type', '-period_start'], name='CLEAR_busin_project_dddcdf_idx')],
            },
        ),
        migrations.CreateModel(
            name='AnalyticsReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('report_type', models.CharField(choices=[('project_summary', 'Project Summary'), ('conflict_analysis', 'Conflict Analysis'), ('financial_analysis', 'Financial Analysis'), ('team_performance', 'Team Performance'), ('custom', 'Custom Report')], max_length=50)),
                ('config', models.JSONField(default=dict, help_text='Report configuration including filters, metrics, and visualizations')),
                ('is_scheduled', models.BooleanField(default=False)),
                ('schedule_config', models.JSONField(default=dict, help_text='Scheduling configuration (frequency, recipients, etc.)')),
                ('last_run_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_reports', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics_reports', to='CLEAR.organization')),
            ],
            options={
                'indexes': [models.Index(fields=['organization', 'report_type'], name='CLEAR_analy_organiz_e5e9b8_idx'), models.Index(fields=['created_by', '-created_at'], name='CLEAR_analy_created_bda7a0_idx'), models.Index(fields=['is_scheduled', '-last_run_at'], name='CLEAR_analy_is_sche_2d0a66_idx')],
            },
        ),
        migrations.CreateModel(
            name='ReportExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('execution_time', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='running', max_length=20)),
                ('export_format', models.CharField(blank=True, choices=[('csv', 'CSV'), ('xlsx', 'Excel'), ('pdf', 'PDF'), ('json', 'JSON')], max_length=10, null=True)),
                ('file_path', models.CharField(blank=True, max_length=500, null=True)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('execution_duration', models.DurationField(blank=True, null=True)),
                ('executed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_executions', to=settings.AUTH_USER_MODEL)),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='CLEAR.analyticsreport')),
            ],
            options={
                'indexes': [models.Index(fields=['report', '-execution_time'], name='CLEAR_repor_report__5a5a9b_idx'), models.Index(fields=['executed_by', '-execution_time'], name='CLEAR_repor_execute_e05c3e_idx'), models.Index(fields=['status', '-execution_time'], name='CLEAR_repor_status_84f7a7_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='systemmetric',
            index=models.Index(fields=['aggregation_period', '-timestamp'], name='CLEAR_syste_aggrega_4b8c8d_idx'),
        ),
        migrations.AddIndex(
            model_name='systemmetric',
            index=models.Index(fields=['project', 'metric_name', '-timestamp'], name='CLEAR_syste_project_bb2f37_idx'),
        ),
        migrations.AddIndex(
            model_name='systemmetric',
            index=models.Index(fields=['organization', 'category', '-timestamp'], name='CLEAR_syste_organiz_99b13e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='businessmetric',
            unique_together={('metric_type', 'period_start', 'period_end', 'organization', 'project')},
        ),
    ]