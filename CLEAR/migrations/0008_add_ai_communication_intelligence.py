import uuid

import django.contrib.postgres.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django for AI Communication Intelligence Foundation




class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('CLEAR', '0007_alter_conversation_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='InternalEmail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('from_address', models.CharField(help_text='Internal email address (e.g., <EMAIL>)', max_length=255)),
                ('to_addresses', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), help_text='Internal recipient addresses', size=None)),
                ('cc_addresses', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, size=None)),
                ('bcc_addresses', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, size=None)),
                ('subject', models.CharField(max_length=255)),
                ('body', models.TextField()),
                ('html_body', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed')], default='draft', max_length=20)),
                ('thread_id', models.UUIDField(blank=True, help_text='Group related emails in threads', null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='internal_emails', to='CLEAR.project')),
                ('related_comment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='CLEAR.comment')),
                ('related_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='CLEAR.chatmessage')),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='CLEAR.internalemail')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_internal_emails', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EntityMention',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('entity_type', models.CharField(help_text='Type of entity (project, task, utility, etc.)', max_length=50)),
                ('entity_id', models.CharField(help_text='ID of the mentioned entity', max_length=255)),
                ('entity_identifier', models.CharField(help_text='Human-readable identifier used in mention', max_length=255)),
                ('mention_text', models.CharField(help_text='The actual @mention text used', max_length=255)),
                ('mention_context', models.TextField(help_text='Surrounding text context')),
                ('object_id', models.CharField(help_text='ID of the content object', max_length=255)),
                ('activity_logged', models.BooleanField(default=False, help_text='Whether this mention has been logged as an activity')),
                ('notification_sent', models.BooleanField(default=False, help_text='Whether notifications have been sent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(help_text='Type of content containing the mention', on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('mentioned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entity_mentions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EntityRelationship',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('source_entity_type', models.CharField(max_length=50)),
                ('source_entity_id', models.CharField(max_length=255)),
                ('target_entity_type', models.CharField(max_length=50)),
                ('target_entity_id', models.CharField(max_length=255)),
                ('relationship_type', models.CharField(choices=[('mentions', 'Entity Mentions'), ('related_to', 'Related To'), ('depends_on', 'Depends On'), ('blocks', 'Blocks'), ('assigned_to', 'Assigned To'), ('parent_child', 'Parent-Child'), ('conflict_with', 'Conflicts With')], max_length=50)),
                ('mention_count', models.PositiveIntegerField(default=1, help_text='Number of times this relationship was mentioned')),
                ('confidence_score', models.FloatField(default=0.0, help_text='AI confidence in this relationship (0.0-1.0)')),
                ('discovery_method', models.CharField(choices=[('manual', 'Manual'), ('mention_analysis', 'Mention Analysis'), ('semantic_analysis', 'Semantic Analysis'), ('pattern_recognition', 'Pattern Recognition')], default='mention_analysis', max_length=50)),
                ('evidence_sources', models.JSONField(default=list, help_text='List of mention IDs or other evidence')),
                ('context_summary', models.TextField(blank=True, null=True)),
                ('first_discovered', models.DateTimeField(auto_now_add=True)),
                ('last_confirmed', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CommunicationIntelligence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('analysis_period_start', models.DateTimeField()),
                ('analysis_period_end', models.DateTimeField()),
                ('total_mentions', models.PositiveIntegerField(default=0)),
                ('unique_entities_mentioned', models.PositiveIntegerField(default=0)),
                ('cross_entity_relationships', models.PositiveIntegerField(default=0)),
                ('communication_frequency', models.FloatField(default=0.0, help_text='Messages/emails per day')),
                ('most_mentioned_entities', models.JSONField(default=list, help_text='Top mentioned entities with counts')),
                ('entity_mention_trends', models.JSONField(default=dict, help_text='Mention patterns over time')),
                ('discovered_relationships', models.PositiveIntegerField(default=0)),
                ('relationship_patterns', models.JSONField(default=dict, help_text='Patterns in entity relationships')),
                ('project_health_indicators', models.JSONField(default=dict, help_text='Communication-based project health metrics')),
                ('collaboration_patterns', models.JSONField(default=dict, help_text='Team collaboration insights')),
                ('analysis_version', models.CharField(default='1.0', max_length=20)),
                ('confidence_level', models.FloatField(default=0.0, help_text='Overall confidence in analysis')),
                ('analyzed_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='communication_intelligence', to='CLEAR.project')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='communication_intelligence', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='InternalEmailRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient_type', models.CharField(choices=[('to', 'To'), ('cc', 'CC'), ('bcc', 'BCC')], max_length=10)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('email', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='CLEAR.internalemail')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('email', 'recipient')},
            },
        ),
        migrations.AddField(
            model_name='internalemail',
            name='recipients',
            field=models.ManyToManyField(related_name='received_internal_emails', through='CLEAR.InternalEmailRecipient', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='internalemail',
            name='entity_mentions',
            field=models.ManyToManyField(blank=True, related_name='internal_emails', to='CLEAR.entitymention'),
        ),
        migrations.AddIndex(
            model_name='internalemail',
            index=models.Index(fields=['project', '-created_at'], name='CLEAR_inter_project_c96bb4_idx'),
        ),
        migrations.AddIndex(
            model_name='internalemail',
            index=models.Index(fields=['sender', '-created_at'], name='CLEAR_inter_sender__6b7fea_idx'),
        ),
        migrations.AddIndex(
            model_name='internalemail',
            index=models.Index(fields=['status', '-created_at'], name='CLEAR_inter_status__9f8a91_idx'),
        ),
        migrations.AddIndex(
            model_name='internalemail',
            index=models.Index(fields=['thread_id', '-created_at'], name='CLEAR_inter_thread__8b4d8b_idx'),
        ),
        migrations.AddIndex(
            model_name='entitymention',
            index=models.Index(fields=['entity_type', 'entity_id'], name='CLEAR_entit_entity__8a3b42_idx'),
        ),
        migrations.AddIndex(
            model_name='entitymention',
            index=models.Index(fields=['mentioned_by', '-created_at'], name='CLEAR_entit_mention_df6b73_idx'),
        ),
        migrations.AddIndex(
            model_name='entitymention',
            index=models.Index(fields=['content_type', 'object_id'], name='CLEAR_entit_content_6e8db1_idx'),
        ),
        migrations.AddIndex(
            model_name='entitymention',
            index=models.Index(fields=['activity_logged'], name='CLEAR_entit_activit_17ab8e_idx'),
        ),
        migrations.AddIndex(
            model_name='entityrelationship',
            index=models.Index(fields=['source_entity_type', 'source_entity_id'], name='CLEAR_entit_source__f7c8e3_idx'),
        ),
        migrations.AddIndex(
            model_name='entityrelationship',
            index=models.Index(fields=['target_entity_type', 'target_entity_id'], name='CLEAR_entit_target__7f8a98_idx'),
        ),
        migrations.AddIndex(
            model_name='entityrelationship',
            index=models.Index(fields=['relationship_type', '-mention_count'], name='CLEAR_entit_relatio_1a9b7c_idx'),
        ),
        migrations.AddIndex(
            model_name='entityrelationship',
            index=models.Index(fields=['confidence_score'], name='CLEAR_entit_confide_c8b4e6_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationintelligence',
            index=models.Index(fields=['project', '-analyzed_at'], name='CLEAR_commu_project_4f7b38_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationintelligence',
            index=models.Index(fields=['user', '-analyzed_at'], name='CLEAR_commu_user_id_7a2b5e_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationintelligence',
            index=models.Index(fields=['analysis_period_start', 'analysis_period_end'], name='CLEAR_commu_analysi_6d8e4a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='entityrelationship',
            unique_together={('source_entity_type', 'source_entity_id', 'target_entity_type', 'target_entity_id', 'relationship_type')},
        ),
        migrations.AlterUniqueTogether(
            name='entitymention',
            unique_together={('content_type', 'object_id', 'mention_text', 'mentioned_by')},
        ),
    ]