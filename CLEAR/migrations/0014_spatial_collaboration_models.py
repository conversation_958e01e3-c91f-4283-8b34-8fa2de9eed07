import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated migration for spatial collaboration features



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0013_knowledge_base_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='SpatialAnnotation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('annotation_text', models.TextField(blank=True, null=True)),
                ('annotation_type', models.CharField(choices=[
                    ('note', 'Note'),
                    ('warning', 'Warning'),
                    ('conflict', 'Conflict'),
                    ('measurement', 'Measurement'),
                    ('markup', 'Markup')
                ], default='note', max_length=50)),
                ('color', models.CharField(default='#ff0000', max_length=7)),
                ('is_visible', models.BooleanField(default=True)),
                ('is_persistent', models.BooleanField(default=True)),
                ('geometry', django.contrib.gis.db.models.fields.GeometryField(srid=4326)),
                ('metadata', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spatial_annotations', to='CLEAR.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spatial_annotations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'spatial_annotations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserPresence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_cursor_position', django.contrib.gis.db.models.fields.PointField(blank=True, null=True, srid=4326)),
                ('current_tool', models.CharField(blank=True, max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('session_id', models.CharField(max_length=255)),
                ('last_seen', models.DateTimeField(auto_now=True)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('left_at', models.DateTimeField(blank=True, null=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_presences', to='CLEAR.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='map_presences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_presence',
                'unique_together': {('user', 'project', 'session_id')},
            },
        ),
        migrations.CreateModel(
            name='CollaborativeDrawing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('drawing_type', models.CharField(choices=[
                    ('line', 'Line'),
                    ('polygon', 'Polygon'),
                    ('point', 'Point'),
                    ('circle', 'Circle'),
                    ('rectangle', 'Rectangle'),
                    ('freehand', 'Freehand')
                ], max_length=20)),
                ('geometry', django.contrib.gis.db.models.fields.GeometryField(srid=4326)),
                ('style', models.JSONField(default=dict)),
                ('is_temporary', models.BooleanField(default=False)),
                ('is_locked', models.BooleanField(default=False)),
                ('version', models.IntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('annotation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='drawings', to='CLEAR.spatialannotation')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collaborative_drawings', to='CLEAR.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drawings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'collaborative_drawings',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='CollaborationSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_public', models.BooleanField(default=False)),
                ('max_participants', models.IntegerField(default=10)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('settings', models.JSONField(default=dict)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_collaboration_sessions', to=settings.AUTH_USER_MODEL)),
                ('participants', models.ManyToManyField(related_name='collaboration_sessions', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collaboration_sessions', to='CLEAR.project')),
            ],
            options={
                'db_table': 'collaboration_sessions',
                'ordering': ['-started_at'],
            },
        ),
        migrations.AddIndex(
            model_name='spatialannotation',
            index=models.Index(fields=['project', 'user'], name='spatial_annot_project_user_idx'),
        ),
        migrations.AddIndex(
            model_name='spatialannotation',
            index=models.Index(fields=['created_at'], name='spatial_annot_created_idx'),
        ),
        migrations.AddIndex(
            model_name='userpresence',
            index=models.Index(fields=['project', 'is_active'], name='user_presence_project_active_idx'),
        ),
        migrations.AddIndex(
            model_name='userpresence',
            index=models.Index(fields=['last_seen'], name='user_presence_last_seen_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborativedrawing',
            index=models.Index(fields=['project', 'is_temporary'], name='collab_drawing_project_temp_idx'),
        ),
    ]