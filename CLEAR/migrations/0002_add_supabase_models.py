import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

# Generated by Django 5.2.3 on 2025-06-14 14:33



class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.Char<PERSON>ield(max_length=255)),
                ('details', models.J<PERSON><PERSON>ield(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='AnalyticsEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(max_length=100)),
                ('path', models.Char<PERSON><PERSON>(blank=True, max_length=500, null=True)),
                ('details', models.J<PERSON>NField(blank=True, default=dict, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('session_id', models.CharField(blank=True, max_length=255, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='AppVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_number', models.CharField(max_length=50)),
                ('release_date', models.DateTimeField()),
                ('release_notes', models.TextField(blank=True, null=True)),
                ('is_mandatory', models.BooleanField(default=False)),
                ('is_current', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-release_date'],
            },
        ),
        migrations.CreateModel(
            name='DatabaseBackup',
            fields=[
                ('id', models.CharField(max_length=255, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('backup_type', models.CharField(choices=[('manual', 'Manual'), ('scheduled', 'Scheduled'), ('automatic', 'Automatic')], default='manual', max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('file_path', models.CharField(blank=True, max_length=500, null=True)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='FeeTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('apply_to', models.CharField(choices=[('fees', 'Fees'), ('expenses', 'Expenses'), ('both', 'Both')], default='fees', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='FeeTemplateOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('base_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('unit_type', models.CharField(blank=True, max_length=50, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('order_index', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['order_index'],
            },
        ),
        migrations.CreateModel(
            name='FeeTemplateSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('order_index', models.IntegerField(default=0)),
                ('is_required', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['order_index'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('slug', models.SlugField(unique=True)),
                ('order_index', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Knowledge Categories',
                'ordering': ['order_index', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PendingChange',
            fields=[
                ('id', models.CharField(max_length=255, primary_key=True, serialize=False)),
                ('change_type', models.CharField(max_length=100)),
                ('entity_type', models.CharField(max_length=100)),
                ('entity_id', models.CharField(max_length=255)),
                ('changes_data', models.JSONField(default=dict)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('applied', 'Applied')], default='pending', max_length=20)),
                ('review_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('applied_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ProjectLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=255)),
                ('details', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TimesheetEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField()),
                ('hours', models.DecimalField(decimal_places=2, max_digits=5)),
                ('notes', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TimesheetPeriod',
            fields=[
                ('id', models.CharField(max_length=255, primary_key=True, serialize=False)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_current', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(max_length=100)),
                ('description', models.CharField(max_length=500)),
                ('metadata', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('note_type', models.CharField(default='general', max_length=50)),
                ('is_pinned', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=255, unique=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserSkill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skill_name', models.CharField(max_length=255)),
                ('proficiency_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], max_length=20)),
                ('years_experience', models.IntegerField(blank=True, null=True)),
                ('is_certified', models.BooleanField(default=False)),
                ('certification_details', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserVersionAcknowledgment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('acknowledged_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='UtilityPhaseStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phase_name', models.CharField(max_length=255)),
                ('status', models.CharField(max_length=50)),
                ('status_date', models.DateField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='WorkType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('complexity_multiplier', models.DecimalField(decimal_places=2, max_digits=5)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RenameIndex(
            model_name='knowledgearticle',
            new_name='CLEAR_knowl_categor_58d3db_idx',
            old_name='CLEAR_knowl_categor_a203f9_idx',
        ),
        migrations.AddField(
            model_name='organization',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='country',
            field=models.CharField(default='USA', max_length=50),
        ),
        migrations.AddField(
            model_name='organization',
            name='currency',
            field=models.CharField(default='USD', max_length=10),
        ),
        migrations.AddField(
            model_name='organization',
            name='date_format',
            field=models.CharField(default='MM/DD/YYYY', max_length=20),
        ),
        migrations.AddField(
            model_name='organization',
            name='email_integration_config',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='email_integration_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='organization',
            name='email_provider',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='email_redirect_uri',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='email_sync_settings',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='fiscal_year_start',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='organization',
            name='setup_completed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='organization',
            name='setup_completed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='setup_completed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_organization_setups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='organization',
            name='signin_footer_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='signin_image_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='state',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='theme_config',
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name='organization',
            name='timezone',
            field=models.CharField(default='America/New_York', max_length=50),
        ),
        migrations.AddField(
            model_name='organization',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='zip_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='project',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='CLEAR.organization'),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='monday_id',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='projecttemplate',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='project_templates', to='CLEAR.organization'),
        ),
        migrations.AddField(
            model_name='user',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invited_users', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='user',
            name='department',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='display_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='email_domain',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='email_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='hourly_rate',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='invite_expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='invite_token',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='job_title',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='last_login_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='last_seen_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='users', to='CLEAR.organization'),
        ),
        migrations.AddField(
            model_name='user',
            name='pay_rate_effective_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='pay_rate_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='permissions',
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name='user',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='salary',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('active', 'Active'), ('inactive', 'Inactive')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='knowledgearticle',
            name='id',
            field=models.CharField(max_length=255, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='organization',
            name='id',
            field=models.CharField(max_length=255, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='organization',
            name='primary_color',
            field=models.CharField(default='#3B82F6', max_length=7),
        ),
        migrations.AlterField(
            model_name='organization',
            name='secondary_color',
            field=models.CharField(default='#10B981', max_length=7),
        ),
        migrations.AlterField(
            model_name='organization',
            name='subdomain',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='projecttemplate',
            name='id',
            field=models.CharField(max_length=255, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='user',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active'),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('admin', 'Admin'), ('user', 'User'), ('manager', 'Manager'), ('coordinator', 'Coordinator')], default='user', max_length=50),
        ),
        migrations.AddField(
            model_name='adminlog',
            name='performed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_actions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='analyticsevent',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='analytics_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='databasebackup',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_backups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='feetemplate',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fee_templates', to='CLEAR.organization'),
        ),
        migrations.AddField(
            model_name='feetemplatesection',
            name='fee_template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='CLEAR.feetemplate'),
        ),
        migrations.AddField(
            model_name='feetemplateoption',
            name='section',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='CLEAR.feetemplatesection'),
        ),
        migrations.AddField(
            model_name='knowledgecategory',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='CLEAR.knowledgecategory'),
        ),
        migrations.AlterField(
            model_name='knowledgearticle',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to='CLEAR.knowledgecategory'),
        ),
        migrations.AddField(
            model_name='pendingchange',
            name='requested_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_changes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='pendingchange',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_changes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectlog',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='CLEAR.project'),
        ),
        migrations.AddField(
            model_name='projectlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_logs', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timesheetentry',
            name='project',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='timesheet_entries', to='CLEAR.project'),
        ),
        migrations.AddField(
            model_name='timesheetentry',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timesheet_entries', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='timesheetentry',
            name='timesheet_period',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entries', to='CLEAR.timesheetperiod'),
        ),
        migrations.AddField(
            model_name='useractivity',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_activities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='usernote',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_notes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='usersession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userskill',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userversionacknowledgment',
            name='app_version',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_acknowledgments', to='CLEAR.appversion'),
        ),
        migrations.AddField(
            model_name='userversionacknowledgment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='version_acknowledgments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='utilityphasestatus',
            name='updated_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='utility_status_updates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='utilityphasestatus',
            name='utility',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='phase_statuses', to='CLEAR.utility'),
        ),
        migrations.AddIndex(
            model_name='adminlog',
            index=models.Index(fields=['performed_by', '-created_at'], name='CLEAR_admin_perform_7f6268_idx'),
        ),
        migrations.AddIndex(
            model_name='adminlog',
            index=models.Index(fields=['action', '-created_at'], name='CLEAR_admin_action_0fa0bb_idx'),
        ),
        migrations.AddIndex(
            model_name='analyticsevent',
            index=models.Index(fields=['event_type', '-timestamp'], name='CLEAR_analy_event_t_67807a_idx'),
        ),
        migrations.AddIndex(
            model_name='analyticsevent',
            index=models.Index(fields=['user', '-timestamp'], name='CLEAR_analy_user_id_434c45_idx'),
        ),
        migrations.AddIndex(
            model_name='analyticsevent',
            index=models.Index(fields=['session_id', '-timestamp'], name='CLEAR_analy_session_92986e_idx'),
        ),
        migrations.AddIndex(
            model_name='pendingchange',
            index=models.Index(fields=['status', '-created_at'], name='CLEAR_pendi_status_40eab8_idx'),
        ),
        migrations.AddIndex(
            model_name='pendingchange',
            index=models.Index(fields=['entity_type', 'entity_id'], name='CLEAR_pendi_entity__22cc75_idx'),
        ),
        migrations.AddIndex(
            model_name='projectlog',
            index=models.Index(fields=['project', '-timestamp'], name='CLEAR_proje_project_648d4b_idx'),
        ),
        migrations.AddIndex(
            model_name='projectlog',
            index=models.Index(fields=['user', '-timestamp'], name='CLEAR_proje_user_id_c06db2_idx'),
        ),
        migrations.AddIndex(
            model_name='timesheetentry',
            index=models.Index(fields=['user', 'timesheet_period'], name='CLEAR_times_user_id_d8262a_idx'),
        ),
        migrations.AddIndex(
            model_name='timesheetentry',
            index=models.Index(fields=['project', 'timesheet_period'], name='CLEAR_times_project_38669e_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['user', '-timestamp'], name='CLEAR_usera_user_id_a9ada8_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['activity_type', '-timestamp'], name='CLEAR_usera_activit_06af07_idx'),
        ),
        migrations.AddIndex(
            model_name='usernote',
            index=models.Index(fields=['user', '-updated_at'], name='CLEAR_usern_user_id_128438_idx'),
        ),
        migrations.AddIndex(
            model_name='usernote',
            index=models.Index(fields=['is_pinned', '-updated_at'], name='CLEAR_usern_is_pinn_21c58c_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', '-started_at'], name='CLEAR_users_user_id_49860f_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_key'], name='CLEAR_users_session_db66aa_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userskill',
            unique_together={('user', 'skill_name')},
        ),
        migrations.AlterUniqueTogether(
            name='userversionacknowledgment',
            unique_together={('user', 'app_version')},
        ),
        migrations.AddIndex(
            model_name='utilityphasestatus',
            index=models.Index(fields=['utility', 'phase_name'], name='CLEAR_utili_utility_7e66e6_idx'),
        ),
        migrations.AddIndex(
            model_name='utilityphasestatus',
            index=models.Index(fields=['status_date'], name='CLEAR_utili_status__de4348_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='utilityphasestatus',
            unique_together={('utility', 'phase_name')},
        ),
    ]
