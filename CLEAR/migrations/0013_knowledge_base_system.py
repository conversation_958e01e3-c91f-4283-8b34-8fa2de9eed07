import uuid

import django.db.models.deletion
from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import migrations, models

# Generated by create_kb_migration.py




class Migration(migrations.Migration):

    dependencies = [
        ('CLEAR', '0012_add_document_phase2_advanced_features'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArticleCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('icon', models.CharField(blank=True, help_text='Font Awesome icon class', max_length=50, null=True)),
                ('color', models.CharField(default='#6366f1', help_text='Hex color code for category', max_length=7)),
                ('sort_order', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='CLEAR.articlecategory')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Article Category',
                'verbose_name_plural': 'Article Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('summary', models.TextField(help_text='Brief summary for search results and cards', max_length=500)),
                ('content', models.TextField(help_text='Full article content in HTML or Markdown')),
                ('content_type', models.CharField(choices=[('html', 'HTML'), ('markdown', 'Markdown')], default='html', max_length=10)),
                ('tags', ArrayField(models.CharField(max_length=50), blank=True, default=list)),
                ('article_type', models.CharField(choices=[('guide', 'How-to Guide'), ('troubleshooting', 'Troubleshooting'), ('faq', 'FAQ'), ('procedure', 'Standard Procedure'), ('reference', 'Reference Document'), ('tutorial', 'Tutorial'), ('policy', 'Policy Document')], default='guide', max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('review', 'Under Review'), ('published', 'Published'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured articles section')),
                ('is_pinned', models.BooleanField(default=False, help_text='Pin to top of category')),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('last_reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('review_due_date', models.DateField(blank=True, help_text='When this article should be reviewed for accuracy', null=True)),
                ('meta_description', models.CharField(blank=True, help_text='SEO meta description', max_length=160, null=True)),
                ('meta_keywords', ArrayField(models.CharField(max_length=50), blank=True, default=list)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('helpful_votes', models.PositiveIntegerField(default=0)),
                ('not_helpful_votes', models.PositiveIntegerField(default=0)),
                ('version', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='authored_articles', to=settings.AUTH_USER_MODEL)),
                ('categories', models.ManyToManyField(blank=True, related_name='articles', to='CLEAR.articlecategory')),
                ('contributors', models.ManyToManyField(blank=True, related_name='contributed_articles', to=settings.AUTH_USER_MODEL)),
                ('previous_version', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='next_versions', to='CLEAR.article')),
            ],
            options={
                'ordering': ['-is_pinned', '-is_featured', '-published_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ArticleView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('referrer', models.URLField(blank=True, null=True)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='CLEAR.article')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ArticleVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('vote_type', models.CharField(choices=[('helpful', 'Helpful'), ('not_helpful', 'Not Helpful')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='CLEAR.article')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('article', 'user'), ('article', 'ip_address')},
            },
        ),
        migrations.CreateModel(
            name='ArticleRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('summary', models.TextField(max_length=500)),
                ('change_notes', models.TextField(blank=True, help_text='What changed in this revision', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions', to='CLEAR.article')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ArticleAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='knowledge_base/attachments/')),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField()),
                ('content_type', models.CharField(max_length=100)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='CLEAR.article')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['original_filename'],
            },
        ),
        # Add indexes
        migrations.AddIndex(
            model_name='articlecategory',
            index=models.Index(fields=['parent', 'sort_order'], name='CLEAR_artic_parent__b0a6e8_idx'),
        ),
        migrations.AddIndex(
            model_name='articlecategory',
            index=models.Index(fields=['slug'], name='CLEAR_artic_slug_f60c3c_idx'),
        ),
        migrations.AddIndex(
            model_name='articlecategory',
            index=models.Index(fields=['is_active', 'sort_order'], name='CLEAR_artic_is_acti_bf5ea0_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['status', '-published_at'], name='CLEAR_artic_status_d9a0d5_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['article_type', 'status'], name='CLEAR_artic_article_0c8fa0_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['-is_featured', '-published_at'], name='CLEAR_artic_is_feat_7cbbca_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['slug'], name='CLEAR_artic_slug_8bf956_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['author', '-created_at'], name='CLEAR_artic_author__e0e6b9_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['-view_count'], name='CLEAR_artic_view_co_99b773_idx'),
        ),
        migrations.AddIndex(
            model_name='articleview',
            index=models.Index(fields=['article', '-viewed_at'], name='CLEAR_artic_article_7b2b3b_idx'),
        ),
        migrations.AddIndex(
            model_name='articleview',
            index=models.Index(fields=['user', '-viewed_at'], name='CLEAR_artic_user_id_10fb86_idx'),
        ),
        migrations.AddIndex(
            model_name='articleview',
            index=models.Index(fields=['ip_address', '-viewed_at'], name='CLEAR_artic_ip_addr_46b38a_idx'),
        ),
        migrations.AddIndex(
            model_name='articlevote',
            index=models.Index(fields=['article', 'vote_type'], name='CLEAR_artic_article_ce6e21_idx'),
        ),
        migrations.AddIndex(
            model_name='articlevote',
            index=models.Index(fields=['-created_at'], name='CLEAR_artic_created_e3b36c_idx'),
        ),
        migrations.AddIndex(
            model_name='articlerevision',
            index=models.Index(fields=['article', '-created_at'], name='CLEAR_artic_article_25906f_idx'),
        ),
    ]
