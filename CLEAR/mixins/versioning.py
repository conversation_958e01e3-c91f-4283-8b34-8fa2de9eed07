"""
Versioning Mixin for Django Models

Provides git-like versioning capabilities for CLEAR models using django-reversion.
Tracks all changes with user attribution and timestamps.
"""

import reversion
from django.conf import settings
from django.db import models
from reversion.models import Version


class VersionedModelMixin(models.Model):
    """
    Mixin to add git-like versioning to Django models.
    
    Features:
    - Automatic version tracking on save
    - User attribution for changes
    - Rollback capabilities
    - Diff generation between versions
    - Audit trail with metadata
    """
    
    # Version tracking fields
    version_created_at = models.DateTimeField(auto_now_add=True, help_text="When this record was first created")
    version_modified_at = models.DateTimeField(auto_now=True, help_text="When this record was last modified")
    version_created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        help_text="User who created this record"
    )
    version_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_modified',
        help_text="User who last modified this record"
    )
    
    class Meta:
        abstract = True
    
    def get_version_history(self, limit=None):
        """
        Get the version history for this object.
        
        Args:
            limit (int): Maximum number of versions to return
            
        Returns:
            QuerySet: Version objects ordered by revision date
        """
        versions = Version.objects.get_for_object(self)
        if limit:
            versions = versions[:limit]
        return versions
    
    def get_version_count(self):
        """
        Get the total number of versions for this object.
        
        Returns:
            int: Number of versions
        """
        return Version.objects.get_for_object(self).count()
    
    def get_version_at_date(self, date):
        """
        Get the version of this object at a specific date.
        
        Args:
            date (datetime): The date to get the version for
            
        Returns:
            Version or None: The version at the specified date
        """
        versions = Version.objects.get_for_object(self).filter(
            revision__date_created__lte=date
        ).first()
        return versions
    
    def rollback_to_version(self, version_id):
        """
        Rollback this object to a specific version.
        
        Args:
            version_id (int): ID of the version to rollback to
            
        Returns:
            bool: Success status
        """
        try:
            version = Version.objects.get_for_object(self).get(pk=version_id)
            version.revert()
            return True
        except Version.DoesNotExist:
            return False
    
    def get_version_diff(self, version1_id, version2_id):
        """
        Get the differences between two versions.
        
        Args:
            version1_id (int): ID of the first version
            version2_id (int): ID of the second version
            
        Returns:
            dict: Field-by-field differences
        """
        try:
            v1 = Version.objects.get_for_object(self).get(pk=version1_id)
            v2 = Version.objects.get_for_object(self).get(pk=version2_id)
            
            obj1 = v1.object_version.object
            obj2 = v2.object_version.object
            
            diff = {}
            for field in self._meta.fields:
                if field.name not in ['id', 'version_created_at', 'version_modified_at',
                                     'version_created_by', 'version_modified_by']:
                    val1 = getattr(obj1, field.name)
                    val2 = getattr(obj2, field.name)
                    if val1 != val2:
                        diff[field.name] = {
                            'old': val1,
                            'new': val2
                        }
            
            return diff
        except Version.DoesNotExist:
            return {}
    
    def get_version_metadata(self, version_id=None):
        """
        Get metadata for a specific version or the latest version.
        
        Args:
            version_id (int): Optional version ID
            
        Returns:
            dict: Version metadata
        """
        try:
            if version_id:
                version = Version.objects.get_for_object(self).get(pk=version_id)
            else:
                version = Version.objects.get_for_object(self).first()
            
            if version:
                return {
                    'version_id': version.pk,
                    'date': version.revision.date_created,
                    'user': version.revision.user,
                    'comment': version.revision.comment,
                    'version_number': Version.objects.get_for_object(self).filter(
                        revision__date_created__gte=version.revision.date_created
                    ).count()
                }
        except Version.DoesNotExist:
            pass
        
        return {}
    
    def create_version_comment(self, comment, user=None):
        """
        Create a version with a specific comment.
        
        Args:
            comment (str): Comment for this version
            user: User making the change
        """
        with reversion.create_revision():
            self.save()
            reversion.set_user(user)
            reversion.set_comment(comment)


# Admin integration helper
class VersionedModelAdmin(reversion.admin.VersionAdmin):
    """
    Enhanced admin for versioned models.
    
    Features:
    - Version history in admin interface
    - Rollback functionality
    - Diff viewer
    - User attribution display
    """
    
    history_latest_first = True
    
    def get_readonly_fields(self, request, obj=None):
        """Add version tracking fields as readonly."""
        readonly = list(super().get_readonly_fields(request, obj))
        readonly.extend(['version_created_at', 'version_modified_at',
                        'version_created_by', 'version_modified_by'])
        return readonly
    
    def save_model(self, request, obj, form, change):
        """Automatically set version tracking fields."""
        if not change:  # Creating new object
            obj.version_created_by = request.user
        obj.version_modified_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_list_display(self, request):
        """Add version info to list display."""
        list_display = list(super().get_list_display(request))
        if 'version_modified_at' not in list_display:
            list_display.append('version_modified_at')
        if 'version_modified_by' not in list_display:
            list_display.append('version_modified_by')
        return list_display