"""
HTMX Response Mixin for Django Views

This mixin provides standardized methods for handling HTMX responses,
ensuring consistent behavior across all views in the CLEAR application.
Follows HDA (Hypermedia-Driven Application) principles.
"""

import json
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.middleware.csrf import get_token


class HTMXResponseMixin:
    """
    Mixin to provide HTMX-specific response methods for Django views.
    
    This mixin ensures all HTMX responses follow HDA principles:
    - HTML-only responses (no JSON for UI updates)
    - Server-side state management
    - Proper HTMX headers and triggers
    """
    
    def htmx_render(self, template_name, context=None, target=None, 
                    swap='innerHTML', push_url=None, trigger_events=None):
        """
        Render an HTML fragment for HTMX requests.
        
        Args:
            template_name (str): Path to the template to render
            context (dict): Context data for the template
            target (str): CSS selector for the target element (sets HX-Retarget header)
            swap (str): How to swap content (innerHTML, outerHTML, beforebegin, etc.)
            push_url (str): URL to push to browser history (sets HX-Push-Url header)
            trigger_events (dict): Events to trigger on the client (sets HX-Trigger header)
            
        Returns:
            HttpResponse: HTML fragment with appropriate HTMX headers
        """
        context = context or {}
        
        # Add CSRF token to context if not present
        if hasattr(self, 'request') and 'csrf_token' not in context:
            context['csrf_token'] = get_token(self.request)
        
        # Render the template
        html = render_to_string(template_name, context, request=getattr(self, 'request', None))
        
        # Create response
        response = HttpResponse(html, content_type='text/html')
        
        # Set HTMX headers
        if target:
            response['HX-Retarget'] = target
            
        if swap != 'innerHTML':
            response['HX-Reswap'] = swap
            
        if push_url is not None:
            response['HX-Push-Url'] = push_url if push_url else 'false'
            
        if trigger_events:
            response['HX-Trigger'] = json.dumps(trigger_events) if isinstance(trigger_events, dict) else trigger_events
            
        return response
    
    def htmx_redirect(self, url, push_url=True):
        """
        Perform an HTMX redirect to a new URL.
        
        Args:
            url (str): URL to redirect to
            push_url (bool): Whether to update browser history
            
        Returns:
            HttpResponse: Response with HX-Redirect header
        """
        response = HttpResponse(status=200)
        response['HX-Redirect'] = url
        
        if not push_url:
            response['HX-Push-Url'] = 'false'
            
        return response
    
    def htmx_refresh(self, target=None):
        """
        Trigger a refresh of the current page or specific target.
        
        Args:
            target (str): Optional CSS selector to refresh specific element
            
        Returns:
            HttpResponse: Response with HX-Refresh or HX-Trigger header
        """
        response = HttpResponse(status=200)
        
        if target:
            # Refresh specific target
            response['HX-Trigger'] = json.dumps({
                'refresh': {'target': target}
            })
        else:
            # Full page refresh
            response['HX-Refresh'] = 'true'
            
        return response
    
    def htmx_trigger(self, event_name, event_data=None, after_settle=False, after_swap=False):
        """
        Trigger a custom event on the client.
        
        Args:
            event_name (str): Name of the event to trigger
            event_data (dict): Optional data to pass with the event
            after_settle (bool): Trigger after settle phase
            after_swap (bool): Trigger after swap phase
            
        Returns:
            HttpResponse: Response with appropriate trigger header
        """
        response = HttpResponse(status=200)
        
        # Build event object
        if event_data:
            event = {event_name: event_data}
        else:
            event = event_name
            
        # Set appropriate header
        if after_settle:
            header_name = 'HX-Trigger-After-Settle'
        elif after_swap:
            header_name = 'HX-Trigger-After-Swap'
        else:
            header_name = 'HX-Trigger'
            
        response[header_name] = json.dumps(event) if isinstance(event, dict) else event
        
        return response
    
    def htmx_error(self, template_name='components/htmx_error.html', 
                   error_message=None, status_code=400):
        """
        Return an error response for HTMX requests.
        
        Args:
            template_name (str): Error template to render
            error_message (str): Error message to display
            status_code (int): HTTP status code
            
        Returns:
            HttpResponse: Error response with appropriate status
        """
        context = {
            'error_message': error_message or 'An error occurred',
            'status_code': status_code
        }
        
        html = render_to_string(template_name, context, request=getattr(self, 'request', None))
        return HttpResponse(html, status=status_code, content_type='text/html')
    
    def htmx_form_errors(self, form, template_name='components/form_errors.html'):
        """
        Return form validation errors as HTML fragment.
        
        Args:
            form: Django form with errors
            template_name (str): Template for rendering errors
            
        Returns:
            HttpResponse: HTML fragment with form errors
        """
        context = {
            'form': form,
            'errors': form.errors
        }
        
        return self.htmx_render(template_name, context)
    
    def is_htmx_request(self):
        """
        Check if the current request is an HTMX request.
        
        Returns:
            bool: True if request has HX-Request header
        """
        if hasattr(self, 'request'):
            return self.request.headers.get('HX-Request') == 'true'
        return False
    
    def get_htmx_trigger(self):
        """
        Get the element that triggered the HTMX request.
        
        Returns:
            str: ID of the triggering element or None
        """
        if hasattr(self, 'request'):
            return self.request.headers.get('HX-Trigger')
        return None
    
    def get_htmx_target(self):
        """
        Get the target element for the HTMX request.
        
        Returns:
            str: ID of the target element or None
        """
        if hasattr(self, 'request'):
            return self.request.headers.get('HX-Target')
        return None