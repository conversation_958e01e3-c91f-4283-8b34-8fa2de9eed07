"""
Feature Flags for CLEAR Application

Provides a simple feature flag system for gradual rollout of new features.
Allows toggling between old and new implementations during migration.
"""

import os
from functools import wraps
from django.conf import settings
from django.core.cache import cache
from django.shortcuts import redirect
from django.http import Http404


class FeatureFlags:
    """
    Feature flag management for the CLEAR application.
    
    Flags can be set via:
    1. Django settings (FEATURE_FLAGS dictionary)
    2. Environment variables (FEATURE_FLAG_<NAME>)
    3. Cache (for runtime toggling)
    """
    
    # Default feature flags
    DEFAULTS = {
        'USE_HTMX_ENDPOINTS': False,  # Use HTMX views instead of JSON APIs
        'USE_OPENLAYERS': False,       # Use OpenLayers instead of Leaflet
        'USE_BOOTSTRAP_ONLY': False,   # Disable Tailwind CSS
        'USE_VERSIONING': False,       # Enable django-reversion
        'USE_ENHANCED_TESTING': True,  # Use new HTMXTestCase framework
    }
    
    @classmethod
    def is_enabled(cls, flag_name):
        """
        Check if a feature flag is enabled.
        
        Priority order:
        1. Cache (runtime override)
        2. Environment variable
        3. Django settings
        4. Default value
        
        Args:
            flag_name (str): Name of the feature flag
            
        Returns:
            bool: Whether the feature is enabled
        """
        # Check cache first (for runtime toggling)
        cache_key = f'feature_flag:{flag_name}'
        cached_value = cache.get(cache_key)
        if cached_value is not None:
            return cached_value
        
        # Check environment variable
        env_key = f'FEATURE_FLAG_{flag_name.upper()}'
        env_value = os.environ.get(env_key)
        if env_value is not None:
            return env_value.lower() in ('true', '1', 'yes', 'on')
        
        # Check Django settings
        if hasattr(settings, 'FEATURE_FLAGS'):
            settings_value = settings.FEATURE_FLAGS.get(flag_name)
            if settings_value is not None:
                return settings_value
        
        # Return default
        return cls.DEFAULTS.get(flag_name, False)
    
    @classmethod
    def set_flag(cls, flag_name, enabled, timeout=3600):
        """
        Set a feature flag value in cache.
        
        Args:
            flag_name (str): Name of the feature flag
            enabled (bool): Whether to enable the feature
            timeout (int): Cache timeout in seconds (default: 1 hour)
        """
        cache_key = f'feature_flag:{flag_name}'
        cache.set(cache_key, enabled, timeout)
    
    @classmethod
    def clear_flag(cls, flag_name):
        """
        Clear a feature flag from cache.
        
        Args:
            flag_name (str): Name of the feature flag
        """
        cache_key = f'feature_flag:{flag_name}'
        cache.delete(cache_key)
    
    @classmethod
    def get_all_flags(cls):
        """
        Get the current state of all feature flags.
        
        Returns:
            dict: Flag names and their current values
        """
        all_flags = {}
        for flag_name in cls.DEFAULTS:
            all_flags[flag_name] = cls.is_enabled(flag_name)
        return all_flags


def feature_flag_required(flag_name, redirect_url=None):
    """
    Decorator to require a feature flag to be enabled.
    
    Args:
        flag_name (str): Name of the required feature flag
        redirect_url (str): Optional URL to redirect if flag is disabled
        
    Returns:
        Decorated function
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not FeatureFlags.is_enabled(flag_name):
                if redirect_url:
                    return redirect(redirect_url)
                else:
                    raise Http404("Feature not enabled")
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def use_htmx_endpoint(htmx_view, json_view):
    """
    Decorator to switch between HTMX and JSON views based on feature flag.
    
    Args:
        htmx_view: The HTMX view function
        json_view: The JSON API view function
        
    Returns:
        The appropriate view based on feature flag
    """
    def view_wrapper(request, *args, **kwargs):
        if FeatureFlags.is_enabled('USE_HTMX_ENDPOINTS'):
            return htmx_view(request, *args, **kwargs)
        else:
            return json_view(request, *args, **kwargs)
    
    # Copy attributes from the HTMX view
    view_wrapper.__name__ = htmx_view.__name__
    view_wrapper.__doc__ = htmx_view.__doc__
    
    return view_wrapper


# Template context processor to make feature flags available in templates
def feature_flags_context(request):
    """
    Add feature flags to template context.
    
    Usage in templates:
        {% if feature_flags.USE_HTMX_ENDPOINTS %}
            <!-- HTMX version -->
        {% else %}
            <!-- Legacy version -->
        {% endif %}
    """
    return {
        'feature_flags': FeatureFlags.get_all_flags()
    }