"""
URL patterns for the CLEAR application.

Main user-facing pages and HTMX endpoints.
"""


"""


app_name = 'CLEAR'

# Migration Status:
# ✅ = Fully migrated with 100% visual parity
# 🔄 = Partially migrated, needs visual refinement
# ❌ = Not yet migrated
#
# Dashboard: ✅ - Main dashboard with all components
# Authentication: ✅ - Login page with full styling
# Projects: 🔄 - Project listing and detail pages need visual refinement
# Tasks: 🔄 - Task board needs visual refinement
# Stakeholders: 🔄 - Stakeholder listing needs visual refinement
# Knowledge Base: 🔄 - Knowledge base home page needs visual refinement
# Feature Requests: 🔄 - Feature request listing needs visual refinement
# Admin: 🔄 - Admin dashboard needs visual refinement
# Profile: 🔄 - Profile page needs visual refinement
# Settings: 🔄 - Settings page needs visual refinement
# Timesheet: 🔄 - Timesheet page needs visual refinement
# Notebook: 🔄 - Notebook page needs visual refinement
# Utility Coordination: 🔄 - Utility coordination page needs visual refinement
# GIS Professional: ❌ - Not yet migrated
# Reports: ❌ - Not yet migrated
# Documents: 🔄 - In active development (Phase 1 complete)
# Messages: ❌ - Not yet migrated

# Authentication patterns
auth_patterns = [
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('signup/', views.SignupView.as_view(), name='signup'),
]

# Dashboard patterns  
dashboard_patterns = [
    path('', views.dashboard, name='dashboard'),
    path('stats/', views.dashboard_stats, name='dashboard_stats'),
    path('activity/', views.recent_activity, name='recent_activity'),
]

# Messaging patterns - Enhanced Real-time Messaging System with Performance & Scalability
messaging_patterns = [
    path('messages/', views.messaging_interface, name='messages'),
    path('htmx/conversations/list/', views.conversation_list_htmx, name='conversation_list_htmx'),
    path('htmx/conversations/create/', views.conversation_create_htmx, name='conversation_create_htmx'),
    path('htmx/conversations/<uuid:conversation_id>/thread/', views.message_thread_htmx, name='message_thread_htmx'),
    path('htmx/conversations/<uuid:conversation_id>/members/', views.conversation_members_htmx, name='conversation_members_htmx'),
    path('htmx/conversations/<uuid:conversation_id>/leave/', views.conversation_leave_htmx, name='conversation_leave_htmx'),
    path('htmx/conversations/<uuid:conversation_id>/mark-read/', views.conversation_mark_read_htmx, name='conversation_mark_read_htmx'),
    path('htmx/messages/create/', views.message_create_htmx, name='message_create_htmx'),
    path('htmx/messages/search/', views.message_search_htmx, name='message_search_htmx'),
    path('htmx/messages/file-upload/', views.message_file_upload_htmx, name='message_file_upload_htmx'),
    path('htmx/messages/<int:message_id>/read/', views.message_mark_read_htmx, name='message_mark_read_htmx'),
    path('htmx/messages/<int:message_id>/delete/', views.message_delete_htmx, name='message_delete_htmx'),
    
    # Performance & Scalability Features - Paginated Message Loading
    path('htmx/messages/paginated/conversation/<uuid:conversation_id>/', message_pagination_views.conversation_messages_paginated, name='conversation_messages_paginated'),
    path('htmx/messages/paginated/channel/<str:channel>/', message_pagination_views.channel_messages_paginated, name='channel_messages_paginated'),
    path('htmx/messages/paginated/thread/<int:parent_message_id>/', message_pagination_views.thread_messages_paginated, name='thread_messages_paginated'),
    path('htmx/messages/paginated/search/', message_pagination_views.message_search_paginated, name='message_search_paginated'),
    path('htmx/messages/load-more/', message_pagination_views.load_more_messages, name='load_more_messages'),
    
    # Message Performance & Analytics
    path('htmx/messages/stats/summary/', message_pagination_views.message_count_summary, name='message_count_summary'),
    path('htmx/conversations/<uuid:conversation_id>/typing/', message_pagination_views.conversation_typing_status, name='conversation_typing_status'),
    path('htmx/conversations/<uuid:conversation_id>/typing/set/', message_pagination_views.set_typing_status, name='set_typing_status'),
    path('htmx/messages/<int:message_id>/thread-preview/', message_pagination_views.message_thread_preview, name='message_thread_preview'),
    
    # Bulk Operations for Scalability
    path('htmx/conversations/<uuid:conversation_id>/mark-all-read/', message_pagination_views.mark_conversation_read, name='mark_conversation_read'),
    
    # Advanced Collaboration Features - Week 4 Task 4.2
    # Message Threading
    path('htmx/messages/thread/create/', views.message_thread_create_htmx, name='message_thread_create_htmx'),
    path('htmx/messages/thread/reply/', views.message_thread_reply_htmx, name='message_thread_reply_htmx'),
    path('htmx/messages/thread/toggle/', views.message_thread_toggle_htmx, name='message_thread_toggle_htmx'),
    path('htmx/messages/thread/<int:thread_id>/view/', views.message_thread_view_htmx, name='message_thread_view_htmx'),
    
    # @Mentions
    path('htmx/users/mention-search/', views.user_mention_search_htmx, name='user_mention_search_htmx'),
    path('htmx/messages/mentions/list/', views.message_mentions_list_htmx, name='message_mentions_list_htmx'),
    
    # Message Reactions
    path('htmx/messages/reaction/add/', views.message_reaction_add_htmx, name='message_reaction_add_htmx'),
    path('htmx/messages/reaction/picker/', views.message_reaction_picker_htmx, name='message_reaction_picker_htmx'),
    
    # Collaboration Settings
    path('htmx/collaboration/settings/', views.collaboration_settings_htmx, name='collaboration_settings_htmx'),
    
]

# Project patterns
project_patterns = [
    path('', views.ProjectListView.as_view(), name='list'),
    path('create/', views.ProjectCreateView.as_view(), name='create'),
    path('<str:pk>/', views.ProjectDetailView.as_view(), name='detail'),
    path('<str:pk>/edit/', views.ProjectEditView.as_view(), name='edit'),
]

# HTMX helper patterns
htmx_patterns = [
    path('whispers/count/', views.whispers_count, name='whispers_count'),
    path('notifications/dropdown/', views.notifications_dropdown, name='notifications_dropdown'),
    path('notifications/<uuid:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    # Enhanced notification management HTMX endpoints
    path('notifications/dropdown-enhanced/', views.notifications_dropdown_enhanced, name='notifications_dropdown_enhanced'),
    path('notifications/mark-all-read/', views.mark_all_notifications_read_htmx, name='mark_all_notifications_read'),
    path('notifications/test/', views.test_notification_htmx, name='test_notification_htmx'),
    path('notifications/<uuid:notification_id>/toggle/', views.notification_mark_read_toggle_htmx, name='notification_mark_read_toggle_htmx'),
    path('notifications/settings/', views.notification_settings_view, name='notification_settings'),
    path('notifications/settings/save/', views.notification_settings_save_htmx, name='notification_settings_save'),
    path('notifications/message-notify/', views.create_message_notification, name='create_message_notification'),
    path('notifications/missed-messages/', views.send_missed_message_notifications, name='send_missed_message_notifications'),
    # Dashboard component endpoints
    path('dashboard-stats-cards/', views.dashboard_stats_cards, name='dashboard_stats_cards'),
    path('team-chat/', views.team_chat_partial, name='team_chat_partial'),
    path('team-chat/send/', views.send_team_message, name='send_team_message'),
    path('timesheet-summary/', views.timesheet_summary_partial, name='timesheet_summary_partial'),
    path('tasks-list/', views.tasks_list_partial, name='tasks_list_partial'),
    path('meetings-list/', views.meetings_list_partial, name='meetings_list_partial'),
    path('my-projects-list/', views.my_projects_list_partial, name='my_projects_list_partial'),
    # Timer management endpoints
    path('timer/start/', views.start_timer_htmx, name='start_timer_htmx'),
    path('timer/stop/', views.stop_timer_htmx, name='stop_timer_htmx'),
    path('timer/status/', views.timer_status_htmx, name='timer_status_htmx'),
    path('timer/quick-entry/', views.quick_time_entry_htmx, name='quick_time_entry_htmx'),
    # Timesheet export endpoints
    path('timesheet/export/csv/', views.export_timesheet_summary_csv, name='export_timesheet_summary_csv'),
    path('timesheet/export/json/', views.export_timesheet_summary_json, name='export_timesheet_summary_json'),
    # Projects HTMX endpoints
    path('projects/search/', views.projects_search, name='projects_search'),
    path('projects/filter/', views.projects_filter, name='projects_filter'),
    # Enhanced Project DataGrid endpoints
    path('projects/sort/', views.projects_sort, name='projects_sort'),
    path('projects/<str:project_id>/update-field/', views.project_update_field, name='project_update_field'),
    path('projects/enhanced/', views.project_list_enhanced, name='project_list_enhanced'),
    path('projects/<str:project_id>/real-time-stats/', views.project_real_time_stats, name='project_real_time_stats'),
    # Document management HTMX endpoints
    path('documents/upload/', views.document_upload_htmx, name='document_upload_htmx'),
    path('documents/<uuid:document_id>/preview/', views.document_preview_htmx, name='document_preview_htmx'),
    path('documents/<uuid:document_id>/delete/', views.document_delete_htmx, name='document_delete_htmx'),
    path('documents/<uuid:document_id>/share/', views.document_share_htmx, name='document_share_htmx'),
    path('documents/create-folder/', views.document_create_folder_htmx, name='document_create_folder_htmx'),
    path('documents/search/', views.document_search_htmx, name='document_search_htmx'),
    # Document collaboration HTMX endpoints
    path('documents/<uuid:document_id>/discussions/', views.document_discussion_list_htmx, name='document_discussion_list_htmx'),
    path('documents/<uuid:document_id>/discussions/create/', views.document_discussion_create_htmx, name='document_discussion_create_htmx'),
    path('documents/discussions/<uuid:discussion_id>/messages/', views.document_discussion_messages_htmx, name='document_discussion_messages_htmx'),
    path('documents/discussions/<uuid:discussion_id>/messages/create/', views.document_discussion_message_create_htmx, name='document_discussion_message_create_htmx'),
    path('documents/discussions/<uuid:discussion_id>/resolve/', views.document_discussion_resolve_htmx, name='document_discussion_resolve_htmx'),
    path('documents/<uuid:document_id>/activity/', views.document_activity_feed_htmx, name='document_activity_feed_htmx'),
    path('documents/<uuid:document_id>/collaboration/', views.document_collaboration_interface_htmx, name='document_collaboration_interface_htmx'),
    path('documents/<uuid:document_id>/version-upload/', views.document_version_upload_htmx, name='document_version_upload_htmx'),
    # Document Management System Phase 2: Advanced Features
    path('documents/<uuid:document_id>/version-history/', views.document_version_history_htmx, name='document_version_history_htmx'),
    path('documents/<uuid:document_id>/create-branch/', views.document_create_branch_htmx, name='document_create_branch_htmx'),
    path('documents/version-diff/<uuid:from_version_id>/<uuid:to_version_id>/', views.document_version_diff_htmx, name='document_version_diff_htmx'),
    path('documents/<uuid:document_id>/rollback/<uuid:version_id>/', views.document_rollback_version_htmx, name='document_rollback_version_htmx'),
    path('documents/advanced-search/', views.document_advanced_search_htmx, name='document_advanced_search_htmx'),
    # Project financial dashboard HTMX endpoints
    path('projects/<str:project_id>/financial-dashboard/', views.project_financial_dashboard_htmx, name='project_financial_dashboard_htmx'),
    path('projects/<str:project_id>/team-activity/', views.project_team_activity_htmx, name='project_team_activity_htmx'),
    path('projects/<str:project_id>/financial-export/', views.project_financial_export_htmx, name='project_financial_export_htmx'),
    path('projects/<str:project_id>/budget-chart-data/', views.project_budget_chart_data_htmx, name='project_budget_chart_data_htmx'),
    # Team collaboration HTMX endpoints
    path('projects/<str:project_id>/team-member-action/', views.project_team_member_action_htmx, name='project_team_member_action_htmx'),
    path('projects/<str:project_id>/team-invite/', views.project_team_invite_htmx, name='project_team_invite_htmx'),
    # Mapping and utility management HTMX endpoints
    path('projects/<str:project_id>/utilities-geojson/', views.mapping_utility_data_geojson, name='mapping_utility_data_geojson'),
    # New Project Mapping System HTMX endpoints
    path('projects/<str:project_id>/map/layers/', views.map_layers_htmx, name='map_layers_htmx'),
    path('projects/<str:project_id>/map/conflicts/', views.utility_conflicts_htmx, name='utility_conflicts_htmx'),
    path('projects/<str:project_id>/map/search/', views.spatial_search_htmx, name='spatial_search_htmx'),
    path('projects/<str:project_id>/map/geojson/', views.map_geojson_data, name='map_geojson_data'),
    # Comment system HTMX endpoints
    path('comments/create/', views.comment_create_htmx, name='comment_create_htmx'),
    path('comments/list/<str:commentable_type>/<str:commentable_id>/', views.comment_list_htmx, name='comment_list_htmx'),
    path('comments/<uuid:comment_id>/update/', views.comment_update_htmx, name='comment_update_htmx'),
    path('comments/<uuid:comment_id>/delete/', views.comment_delete_htmx, name='comment_delete_htmx'),
    path('comments/<uuid:comment_id>/replies/', views.comment_replies_htmx, name='comment_replies_htmx'),
    path('comments/count/<str:commentable_type>/<str:commentable_id>/', views.comment_count_htmx, name='comment_count_htmx'),
    # Project tab system HTMX endpoints
    path('projects/<str:project_id>/tabs/<str:tab_name>/', views.project_tab_htmx, name='project_tab_htmx'),
    # Project messaging HTMX endpoints
    path('projects/<str:project_id>/conversations/create/', views.project_conversation_create_htmx, name='project_conversation_create_htmx'),
    path('projects/<str:project_id>/conversations/create-post/', views.project_conversation_create_post_htmx, name='project_conversation_create_post_htmx'),
    path('projects/<str:project_id>/conversations/<uuid:conversation_id>/select/', views.project_conversation_select_htmx, name='project_conversation_select_htmx'),
    path('projects/<str:project_id>/conversations/search/', views.project_conversations_search_htmx, name='project_conversations_search_htmx'),
    # Profile & Settings HTMX endpoints
    path('profile/stats/', views.profile_stats_htmx, name='profile_stats_htmx'),
    path('profile/activity/', views.profile_activity_htmx, name='profile_activity_htmx'),
    path('settings/save/', views.settings_save_htmx, name='settings_save_htmx'),
    path('settings/tab/', views.settings_tab_htmx, name='settings_tab_htmx'),
    path('profile/avatar/upload/', views.avatar_upload_htmx, name='avatar_upload_htmx'),
    
    # Project mapping HTMX endpoints
    path('projects/<str:project_id>/map/layers/', views.map_layers_htmx, name='map_layers_htmx'),
    path('projects/<str:project_id>/map/conflict-detection/', views.map_conflict_detection_htmx, name='map_conflict_detection_htmx'),
    path('projects/<str:project_id>/map/utility/<int:utility_line_id>/', views.map_utility_info_htmx, name='map_utility_info_htmx'),
    
    # Timeline HTMX endpoints
    path('timeline-data/<str:project_id>/', views.timeline_data_htmx, name='timeline_data_htmx'),
    path('timeline/task-update/', views.task_timeline_update_htmx, name='task_timeline_update_htmx'),
    path('timeline/task-move/', views.timeline_task_move_htmx, name='timeline_task_move_htmx'),
    path('timeline/dependency-management/', views.dependency_management_htmx, name='dependency_management_htmx'),
    path('timeline/metrics/<str:project_id>/', views.timeline_metrics_htmx, name='timeline_metrics_htmx'),
    path('timeline/critical-path/<str:project_id>/', views.calculate_critical_path_htmx, name='calculate_critical_path_htmx'),
    path('timeline/export/<str:project_id>/', views.timeline_export_htmx, name='timeline_export_htmx'),
]

urlpatterns = [
    # Root redirect to dashboard
    path('', RedirectView.as_view(url='/dashboard/', permanent=False), name='root_redirect'),

    # Authentication pages
    path('auth/', include(auth_patterns)),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # Main dashboard
    path('dashboard/', include(dashboard_patterns)),

    # Project management
    path('projects/', include(project_patterns)),

    # Messaging system
    path('', include(messaging_patterns)),

    # HTMX endpoints
    path('htmx/', include(htmx_patterns)),

    # Utility management
    path('projects/<str:project_id>/utilities/', views.UtilityListView.as_view(), name='utility_list'),
    path('projects/<str:project_id>/utilities/<int:pk>/', views.UtilityDetailView.as_view(), name='utility_detail'),
    path('projects/<str:project_id>/utilities/create/', views.UtilityCreateView.as_view(), name='utility_create'),

    # Conflict detection and management
    path('projects/<str:project_id>/conflicts/', views.ConflictListView.as_view(), name='conflict_list'),
    path('projects/<str:project_id>/conflicts/<int:pk>/', views.ConflictDetailView.as_view(), name='conflict_detail'),
    path('projects/<str:project_id>/conflicts/detect/', views.ConflictDetectionView.as_view(), name='conflict_detection'),

    # Mapping and spatial features
    path('projects/<str:project_id>/map/', views.project_map_view, name='project_map'),
    path('projects/<str:project_id>/map/3d/', views.Project3DView.as_view(), name='project_3d'),

    # Task management
    path('projects/<str:project_id>/tasks/', views.TaskListView.as_view(), name='task_list'),
    path('projects/<str:project_id>/tasks/<str:pk>/', views.TaskDetailView.as_view(), name='task_detail'),
    path('projects/<str:project_id>/tasks/create/', views.TaskCreateView.as_view(), name='task_create'),
    
    # Project Timeline & Gantt Chart
    path('projects/<str:project_id>/timeline/', views.ProjectTimelineView.as_view(), name='project_timeline'),

    # Communication and collaboration
    path('projects/<str:project_id>/chat/', views.ProjectChatView.as_view(), name='project_chat'),
    path('projects/<str:project_id>/comments/', views.ProjectCommentsView.as_view(), name='project_comments'),
    path('notifications/', views.notification_list_view, name='notifications'),

    # Admin features
    path('admin-panel/', views.AdminPanelView.as_view(), name='admin_panel'),
    path('admin-panel/users/', views.UserManagementView.as_view(), name='user_management'),
    path('admin-panel/organizations/', views.OrganizationSettingsView.as_view(), name='organization_settings'),

    # User profile and settings
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('settings/', views.UserSettingsView.as_view(), name='user_settings'),

    # HTMX partial views (for dynamic content)
    path('htmx/project-stats/', views.ProjectStatsPartial.as_view(), name='project_stats_partial'),
    path('htmx/utility-form/', views.UtilityFormPartial.as_view(), name='utility_form_partial'),
    path('htmx/conflict-summary/', views.ConflictSummaryPartial.as_view(), name='conflict_summary_partial'),
    path('htmx/task-form/', views.TaskFormPartial.as_view(), name='task_form_partial'),
    path('htmx/notification-count/', views.NotificationCountPartial.as_view(), name='notification_count_partial'),

    # ========== ADMIN SECTION - Enhanced Implementation ==========
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/analytics/', views.admin_analytics_dashboard, name='admin_analytics'),
    path('admin/user-management/', views.admin_user_management, name='admin_user_management'),
    
    # Admin HTMX endpoints for real-time functionality
    path('htmx/admin/stats/', views.admin_stats_partial, name='admin_stats_partial'),
    path('htmx/admin/analytics/data/', views.admin_analytics_data_htmx, name='admin_analytics_data_htmx'),
    path('htmx/admin/refresh-stats/', views.admin_stats_partial, name='admin_refresh_stats'),
    
    # User management HTMX endpoints
    path('htmx/admin/users/search/', views.admin_user_search, name='admin_user_search'),
    path('htmx/admin/users/filter/', views.admin_user_filter, name='admin_user_filter'),
    path('htmx/admin/users/create/', views.admin_user_create, name='admin_user_create'),
    path('htmx/admin/users/stats/', views.admin_user_stats_htmx, name='admin_user_stats_htmx'),
    path('htmx/admin/users/<int:user_id>/status-toggle/', views.admin_user_status_toggle, name='admin_user_status_toggle'),
    path('htmx/admin/users/<int:user_id>/edit/', views.admin_user_edit_htmx, name='admin_user_edit'),
    path('htmx/admin/users/<int:user_id>/update/', views.admin_user_update_htmx, name='admin_user_update'),
    path('htmx/admin/users/<int:user_id>/detail/', views.admin_user_detail_htmx, name='admin_user_detail'),
    path('htmx/admin/users/<int:user_id>/delete-confirmation/', views.admin_user_delete_confirmation_htmx, name='admin_user_delete_confirmation'),
    path('htmx/admin/users/<int:user_id>/delete/', views.admin_user_delete_htmx, name='admin_user_delete'),
    path('htmx/admin/users/<int:user_id>/resend-invite/', views.admin_user_resend_invite_htmx, name='admin_user_resend_invite'),
    
    # Real-time system monitoring endpoints
    path('htmx/admin/system-health/', views.admin_system_health_partial, name='admin_system_health_partial'),
    path('api/admin/system-overview/', views.admin_system_overview_api, name='admin_system_overview_api'),
    
    # System Monitoring Dashboard - Phase 2 Implementation
    path('admin/system-monitoring/', views.admin_system_monitoring, name='admin_system_monitoring'),
    path('htmx/admin/realtime/cpu/', views.admin_realtime_cpu_metric, name='admin_realtime_cpu_metric'),
    path('htmx/admin/realtime/memory/', views.admin_realtime_memory_metric, name='admin_realtime_memory_metric'),
    path('htmx/admin/realtime/users/', views.admin_realtime_users_metric, name='admin_realtime_users_metric'),
    path('htmx/admin/realtime/database/', views.admin_realtime_database_metric, name='admin_realtime_database_metric'),
    path('htmx/admin/performance/metrics/', views.admin_performance_metrics_partial, name='admin_performance_metrics_partial'),
    path('htmx/admin/system/alerts/', views.admin_system_alerts_partial, name='admin_system_alerts_partial'),
    path('htmx/admin/database/metrics/', views.admin_database_metrics_partial, name='admin_database_metrics_partial'),
    path('htmx/admin/refresh-stats/', views.admin_refresh_stats, name='admin_refresh_stats'),
    
    # Legacy admin routes for compatibility
    path('admin/clients/', views.AdminClientManagementView.as_view(), name='admin_clients'),
    path('admin/contract-administration/', views.AdminContractView.as_view(), name='contract_administration'),
    path('admin/data-import/', views.AdminDataImportView.as_view(), name='admin_data_import'),
    path('admin/database-management/', views.admin_dashboard, name='admin_database_management'),
    path('admin/man-hour-calculator/', views.admin_dashboard, name='admin_calculator'),
    path('admin/metrics/', views.AdminMetricsView.as_view(), name='admin_metrics'),
    path('admin/organization-settings/', views.OrganizationSettingsView.as_view(), name='admin_organization_settings'),
    path('admin/organization-setup/', views.OrganizationSettingsView.as_view(), name='admin_org_setup'),
    path('admin/performance/', views.AdminMetricsView.as_view(), name='performance'),
    path('admin/security/', views.AdminSecurityView.as_view(), name='admin_security'),
    path('admin/template-management/', views.AdminTemplateManagementView.as_view(), name='admin_template_management'),
    path('admin/templates/', views.AdminTemplateManagementView.as_view(), name='admin_templates'),
    path('admin/templates/<uuid:pk>/', views.TemplateDetailView.as_view(), name='admin_template_detail'),
    path('admin/test-results/', views.admin_dashboard, name='admin_test_results'),
    path('admin/user-management/', views.admin_user_management, name='user_management'),
    path('admin/version-management/', views.admin_dashboard, name='version_management'),
    path('admin/work-types/', views.admin_dashboard, name='admin_work_types'),
    path('admin/audit-log/', views.admin_dashboard, name='admin_audit_log'),

    # ========== FEATURE REQUESTS ==========
    path('feature-requests/', views.FeatureRequestListView.as_view(), name='feature_requests'),
    path('feature-requests/create/', views.FeatureRequestCreateView.as_view(), name='feature_request_create'),
    path('feature-requests/<uuid:pk>/', views.FeatureRequestDetailView.as_view(), name='feature_request_detail'),

    # ========== GIS PROFESSIONAL ==========
    path('gis-professional/', views.GISProfessionalView.as_view(), name='gis_professional'),
    path('gis-test/', views.GISTestView.as_view(), name='gis_test'),

    # ========== KNOWLEDGE BASE ==========
    path('knowledge-base/', views.knowledge_base_list, name='knowledge_base_list'),
    path('knowledge-base/articles/<slug:slug>/', views.knowledge_base_article_detail, name='knowledge_base_article_detail'),
    path('knowledge-base/categories/<slug:slug>/', views.knowledge_base_category_list, name='knowledge_base_category_list'),
    
    # Knowledge Base HTMX endpoints
    path('htmx/kb/articles/search/', views.kb_articles_search_htmx, name='kb_articles_search_htmx'),
    path('htmx/kb/articles/vote/', views.kb_article_vote_htmx, name='kb_article_vote_htmx'),
    path('htmx/kb/articles/create/', views.kb_article_create_htmx, name='kb_article_create_htmx'),
    path('htmx/kb/articles/<slug:slug>/update/', views.kb_article_update_htmx, name='kb_article_update_htmx'),
    path('htmx/kb/categories/list/', views.kb_categories_list_htmx, name='kb_categories_list_htmx'),
    path('htmx/kb/recent-articles/', views.kb_recent_articles_htmx, name='kb_recent_articles_htmx'),

    # ========== PROJECT MANAGEMENT ==========
    path('my-projects/', views.MyProjectsView.as_view(), name='my_projects'),
    path('project-portfolio/', views.ProjectPortfolioView.as_view(), name='project_portfolio'),
    path('projects/new/', views.ProjectCreateView.as_view(), name='project_new'),
    path('projects/<str:pk>/edit/', views.ProjectEditView.as_view(), name='project_edit'),
    path('projects/<str:pk>/utility-coordination/', views.UtilityCoordinationView.as_view(), name='project_utility_coordination'),

    # ========== NOTES AND NOTEBOOK ==========
    path('notebook/', views.NotebookView.as_view(), name='notebook'),
    path('notes/', views.NotesListView.as_view(), name='notes'),
    path('notes/create/', views.NoteCreateView.as_view(), name='note_create'),
    path('notes/<uuid:pk>/', views.NoteDetailView.as_view(), name='note_detail'),

    # ========== PROFILE AND SETTINGS ==========
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('settings/', views.UserSettingsView.as_view(), name='settings'),

    # ========== STAKEHOLDER MANAGEMENT ==========
    path('stakeholders/', views.StakeholderListView.as_view(), name='stakeholders'),
    path('stakeholders/<int:pk>/', views.StakeholderDetailView.as_view(), name='stakeholder_detail'),

    # ========== INVOICING ==========
    path('sub-invoices/', views.InvoiceListView.as_view(), name='sub_invoices'),
    path('invoices/', views.InvoiceListView.as_view(), name='invoices'),
    path('invoices/<uuid:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),

    # ========== TASK MANAGEMENT ==========
    path('tasks/', views.TaskDataGridView.as_view(), name='tasks'),
    path('tasks/board/', views.TaskBoardView.as_view(), name='task_board'),
    
    # HTMX endpoints for Task Data Grid
    path('tasks/update/<str:task_id>/', views.task_update, name='task_update'),
    path('tasks/filter/', views.tasks_filter, name='tasks_filter'),
    path('tasks/sort/', views.tasks_sort, name='tasks_sort'),

    # ========== TEMPLATES ==========
    path('templates/', views.TemplatesListView.as_view(), name='templates'),
    path('templates/<uuid:pk>/', views.TemplateDetailView.as_view(), name='template_detail'),
    path('templates/js/versioning.js', views.template_versioning_js, name='template_versioning_js'),

    # ========== TIMESHEET ==========
    path('timesheet/', views.timesheet_view, name='timesheet'),
    path('timesheet/save/', views.save_timesheet, name='save_timesheet'),
    path('timesheet/submit/', views.submit_timesheet, name='submit_timesheet'),
    path('timesheet/export/', views.export_timesheet, name='export_timesheet'),
    path('timesheet/update-entry/', views.update_time_entry, name='update_time_entry'),
    # path('meetings/', views.MeetingsView.as_view(), name='meetings'),  # TODO: Implement MeetingsView

    # ========== UTILITY COORDINATION ==========
    path('utility-coordination/', views.UtilityCoordinationView.as_view(), name='utility_coordination'),

    # ========== REPORTS & ANALYTICS ==========
    path('reports/', views.ReportsView.as_view(), name='reports'),
    path('reports/dashboard/', views.BusinessAnalyticsDashboardView.as_view(), name='business_analytics_dashboard'),
    path('reports/analytics/', views.BusinessAnalyticsDashboardView.as_view(), name='analytics_dashboard'),
    
    # Executive Analytics Dashboard
    path('reports/executive/', views.executive_analytics_dashboard, name='executive_analytics_dashboard'),
    
    # HTMX Analytics Endpoints
    path('htmx/analytics/business-metrics/', views.business_metrics_partial, name='business_metrics_partial'),
    path('htmx/analytics/chart-data/', views.analytics_chart_data, name='analytics_chart_data'),
    path('htmx/analytics/export/', views.analytics_export, name='analytics_export'),
    
    # Executive Analytics HTMX Endpoints
    path('htmx/analytics/executive/refresh/', views.executive_analytics_refresh, name='executive_analytics_refresh'),
    path('htmx/analytics/executive/period/', views.executive_analytics_period, name='executive_analytics_period'),
    path('htmx/analytics/executive/export/', views.executive_analytics_export, name='executive_analytics_export'),

    # ========== DOCUMENTS ==========
    path('documents/', views.DocumentListView.as_view(), name='documents'),
    path('documents/<uuid:document_id>/workspace/', views.DocumentWorkspaceView.as_view(), name='document_workspace'),

    # ========== COMMUNICATION ==========
    path('messages/', views.MessagesView.as_view(), name='messages'),

    # ========== HTMX API ENDPOINTS ==========
    path('htmx/tasks/create/', views.create_task_htmx, name='htmx_create_task'),
    path('htmx/tasks/<str:task_id>/update-status/', views.update_task_status_htmx, name='htmx_update_task_status'),
    path('htmx/projects/<str:project_id>/conflict-detection/', views.run_conflict_detection_htmx, name='htmx_conflict_detection'),

    # Enhanced HTMX endpoints for dynamic content
    path('htmx/notebook/search/', views.notebook_search, name='htmx_notebook_search'),
    path('htmx/stakeholders/search/', views.stakeholder_search, name='htmx_stakeholder_search'),
    # Enhanced Stakeholder Management HTMX endpoints
    path('htmx/stakeholders/search-grid/', views.stakeholder_search_htmx, name='stakeholder_search_htmx'),
    path('htmx/stakeholders/filter/', views.stakeholder_filter_htmx, name='stakeholder_filter_htmx'),
    path('htmx/stakeholders/sort/', views.stakeholder_sort_htmx, name='stakeholder_sort_htmx'),
    path('htmx/stakeholders/<int:stakeholder_id>/update/', views.stakeholder_update_htmx, name='stakeholder_update_htmx'),
    path('htmx/stakeholders/<int:stakeholder_id>/projects/', views.stakeholder_projects_htmx, name='stakeholder_projects_htmx'),
    path('htmx/stakeholders/analytics/', views.stakeholder_analytics_htmx, name='stakeholder_analytics_htmx'),
    path('htmx/stakeholders/analytics/export/', views.stakeholder_analytics_export_csv, name='stakeholder_analytics_export_csv'),
    # Export and Reporting endpoints
    path('stakeholders/export/csv/', views.stakeholder_export_csv, name='stakeholder_export_csv'),
    path('stakeholders/export/communications/', views.stakeholder_communications_report_csv, name='stakeholder_communications_report_csv'),
    path('stakeholders/export/project-assignments/', views.stakeholder_project_assignments_report_csv, name='stakeholder_project_assignments_report_csv'),
    # Mobile-optimized HTMX endpoints
    path('htmx/mobile/stakeholders/<int:stakeholder_id>/projects/', views.mobile_stakeholder_projects_htmx, name='mobile_stakeholder_projects_htmx'),
    path('htmx/mobile/communication/history/<str:entity_type>/<str:entity_id>/', views.mobile_communication_history_htmx, name='mobile_communication_history_htmx'),
    # Communication tracking HTMX endpoints
    path('htmx/communication/history/<str:entity_type>/<str:entity_id>/', views.communication_history_htmx, name='communication_history_htmx'),
    path('htmx/communication/log/<str:entity_type>/<str:entity_id>/', views.log_communication_htmx, name='log_communication_htmx'),
    # Project assignment HTMX endpoints
    path('htmx/stakeholders/<int:stakeholder_id>/assign-project/', views.stakeholder_assign_project_htmx, name='stakeholder_assign_project_htmx'),
    path('htmx/stakeholders/<int:stakeholder_id>/remove-project/', views.stakeholder_remove_project_htmx, name='stakeholder_remove_project_htmx'),
    path('htmx/projects/search/', views.project_search_htmx, name='project_search_htmx'),
    path('htmx/templates/search/', views.template_search, name='htmx_template_search'),
    path('htmx/projects/<str:project_id>/stats/', views.project_quick_stats, name='htmx_project_stats'),
    path('htmx/activity-feed/', views.user_activity_feed, name='htmx_activity_feed'),
    path('htmx/notifications/<uuid:notification_id>/toggle/', views.toggle_notification_read, name='htmx_toggle_notification'),
    path('htmx/projects/<str:project_id>/utilities/', views.project_utilities_list, name='htmx_project_utilities'),
    path('htmx/projects/<str:project_id>/conflicts-summary/', views.project_conflicts_summary, name='htmx_conflicts_summary'),
    path('htmx/timesheet/summary/', views.timesheet_week_summary, name='htmx_timesheet_summary'),
    path('htmx/tasks/quick-create/', views.quick_task_create, name='htmx_quick_task_create'),
    path('htmx/invoices/summary/', views.invoice_summary, name='htmx_invoice_summary'),
    path('htmx/gis/layers/', views.gis_layers_list, name='htmx_gis_layers'),
    path('htmx/gis/layers/<uuid:layer_id>/toggle/', views.toggle_gis_layer, name='htmx_toggle_gis_layer'),
    # Reports and Analytics HTMX endpoints
    path('htmx/reports/metrics/', views.business_metrics_partial, name='business_metrics_partial'),
    path('htmx/reports/chart-data/<str:chart_type>/', views.analytics_chart_data, name='analytics_chart_data'),
    path('htmx/reports/export/', views.analytics_export, name='analytics_export'),
    
    # ========== AI COMMUNICATION INTELLIGENCE ==========
    path('ai-communication/', views.ai_communication_dashboard, name='ai_communication_dashboard'),
    
    # AI Communication HTMX endpoints
    path('htmx/ai/email-compose/', views.internal_email_compose_htmx, name='internal_email_compose_htmx'),
    path('htmx/ai/entity-autocomplete/', views.entity_mention_autocomplete_htmx, name='entity_mention_autocomplete_htmx'),
    path('htmx/ai/chain-builder/', views.entity_chain_builder_htmx, name='entity_chain_builder_htmx'),
    path('htmx/ai/command-process/', views.ai_command_process_htmx, name='ai_command_process_htmx'),
    path('htmx/ai/knowledge-graph/', views.knowledge_graph_visualization_htmx, name='knowledge_graph_visualization_htmx'),
    path('htmx/ai/insights/', views.communication_insights_htmx, name='communication_insights_htmx'),
    
    # ========== ENHANCED SPATIAL ANALYSIS ==========
    # Spatial analysis endpoints for advanced mapping functionality
    path('htmx/projects/<uuid:project_id>/spatial-analysis/buffer-zones/', views.spatial_analysis_buffer_zones, name='spatial_analysis_buffer_zones'),
    path('htmx/projects/<uuid:project_id>/spatial-analysis/clearance-violations/', views.spatial_analysis_clearance_violations, name='spatial_analysis_clearance_violations'),
    path('htmx/projects/<uuid:project_id>/spatial-analysis/spatial-query/', views.spatial_analysis_spatial_query, name='spatial_analysis_spatial_query'),
    
    # GeoJSON data endpoints for mapping
    path('htmx/projects/<uuid:project_id>/utilities-geojson/', views.project_utilities_geojson, name='project_utilities_geojson'),
    path('htmx/projects/<uuid:project_id>/boundary-geojson/', views.project_boundary_geojson, name='project_boundary_geojson'),
    path('htmx/projects/<uuid:project_id>/coordinate-systems/', views.project_coordinate_systems, name='project_coordinate_systems'),
    
    # Enhanced conflict detection
    path('htmx/projects/<uuid:project_id>/conflict-detection/', views.htmx_project_conflict_detection, name='htmx_project_conflict_detection'),
    
    # ========== COLLABORATIVE MAPPING (Enhanced Mapping Interface Phase 2) ==========
    # Real-time collaborative mapping endpoints
    path('api/spatial/collaboration/<uuid:project_id>/init/', views.spatial_collaboration_init, name='spatial_collaboration_init'),
    path('htmx/spatial/<uuid:project_id>/annotations/create/', views.spatial_annotation_create_htmx, name='spatial_annotation_create_htmx'),
    path('htmx/spatial/<uuid:project_id>/annotations/', views.spatial_annotations_list_htmx, name='spatial_annotations_list_htmx'),
    path('htmx/spatial/<uuid:project_id>/annotations/<int:annotation_id>/update/', views.spatial_annotation_update_htmx, name='spatial_annotation_update_htmx'),
    path('htmx/spatial/<uuid:project_id>/annotations/<int:annotation_id>/delete/', views.spatial_annotation_delete_htmx, name='spatial_annotation_delete_htmx'),
    path('htmx/spatial/<uuid:project_id>/collaboration/status/', views.spatial_collaboration_status_htmx, name='spatial_collaboration_status_htmx'),
    path('htmx/spatial/<uuid:project_id>/drawings/create/', views.spatial_drawing_create_htmx, name='spatial_drawing_create_htmx'),
    path('htmx/spatial/<uuid:project_id>/analysis/panel/', views.spatial_analysis_panel_htmx, name='spatial_analysis_panel_htmx'),
]

"""