"""
Celery tasks for CLEAR application.
Handles background processing including scheduled reports.
"""

import logging
from datetime import timed<PERSON><PERSON>
from celery import shared_task
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from .models.analytics import AnalyticsReport, ReportExecution
from .services.stakeholder_analytics import StakeholderAnalyticsEngine
from .models.projects import Project
from .models.analytics import SystemMetric

User = get_user_model()
logger = logging.getLogger(__name__)


@shared_task(bind=True)
def run_scheduled_reports(self):
    """
    Main task to check for and execute scheduled reports.
    Runs every 5 minutes to check for reports that need execution.
    """
    try:
        now = timezone.now()
        # Find reports that are scheduled and due for execution
        due_reports = AnalyticsReport.objects.filter(
            is_scheduled=True,
            next_execution_at__lte=now
        ).select_related('organization', 'created_by')
        
        executed_count = 0
        for report in due_reports:
            try:
                # Execute the report asynchronously
                execute_scheduled_report.delay(str(report.id))
                executed_count += 1
                logger.info(f"Queued execution for report: {report.name}")
            except Exception as e:
                logger.error(f"Failed to queue report {report.name}: {str(e)}")
        
        logger.info(f"Scheduled report check completed. Queued {executed_count} reports.")
        return f"Queued {executed_count} reports for execution"
        
    except Exception as e:
        logger.error(f"Error in run_scheduled_reports: {str(e)}")
        raise


@shared_task(bind=True)
def execute_scheduled_report(self, report_id):
    """
    Execute a specific scheduled report and handle email delivery.
    
    Args:
        report_id (str): UUID of the AnalyticsReport to execute
    """
    try:
        report = AnalyticsReport.objects.get(id=report_id)
        
        # Create execution record
        execution = ReportExecution.objects.create(
            report=report,
            status='running'
        )
        
        start_time = timezone.now()
        
        try:
            # Generate report data based on report type
            if report.report_type == 'scheduled':
                data = generate_stakeholder_analytics_report(report)
            else:
                data = generate_generic_report(report)
            
            # Calculate execution time
            execution_time = (timezone.now() - start_time).total_seconds() * 1000
            
            # Update execution record with success
            execution.status = 'completed'
            execution.result_data = data
            execution.execution_time_ms = int(execution_time)
            execution.data_points_processed = data.get('data_points_count', 0)
            execution.completed_at = timezone.now()
            execution.save()
            
            # Send email if configured
            if should_send_email(report):
                send_report_email.delay(str(execution.id))
            
            # Update next execution time
            update_next_execution_time(report)
            
            logger.info(f"Successfully executed report: {report.name}")
            return f"Report executed successfully: {report.name}"
            
        except Exception as e:
            # Update execution record with failure
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.completed_at = timezone.now()
            execution.save()
            
            logger.error(f"Report execution failed for {report.name}: {str(e)}")
            raise
            
    except AnalyticsReport.DoesNotExist:
        logger.error(f"Report not found: {report_id}")
        raise
    except Exception as e:
        logger.error(f"Error executing report {report_id}: {str(e)}")
        raise


@shared_task(bind=True)
def send_report_email(self, execution_id):
    """
    Send email notification for completed report.
    
    Args:
        execution_id (str): UUID of the ReportExecution
    """
    try:
        execution = ReportExecution.objects.select_related(
            'report', 'report__organization', 'report__created_by'
        ).get(id=execution_id)
        
        report = execution.report
        
        # Get email recipients
        recipients = get_report_recipients(report)
        
        if not recipients:
            logger.warning(f"No recipients found for report: {report.name}")
            return "No recipients found"
        
        # Render email content
        subject = f"Scheduled Report: {report.name}"
        
        context = {
            'report': report,
            'execution': execution,
            'data': execution.result_data,
            'organization': report.organization,
        }
        
        html_message = render_to_string('emails/scheduled_report.html', context)
        plain_message = render_to_string('emails/scheduled_report.txt', context)
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email='<EMAIL>',
            recipient_list=recipients,
            fail_silently=False,
        )
        
        logger.info(f"Sent report email for {report.name} to {len(recipients)} recipients")
        return f"Email sent to {len(recipients)} recipients"
        
    except ReportExecution.DoesNotExist:
        logger.error(f"Report execution not found: {execution_id}")
        raise
    except Exception as e:
        logger.error(f"Error sending report email {execution_id}: {str(e)}")
        raise


def generate_stakeholder_analytics_report(report):
    """Generate stakeholder analytics report data."""
    try:
        # Get projects for the organization
        projects = Project.objects.filter(organization=report.organization)
        
        if not projects.exists():
            return {
                'error': 'No projects found for organization',
                'data_points_count': 0
            }
        
        # Use the stakeholder analytics engine
        analytics_engine = StakeholderAnalyticsEngine()
        
        # Aggregate data across all projects
        all_data = []
        total_stakeholders = 0
        
        for project in projects:
            try:
                project_data = analytics_engine.get_comprehensive_analytics(project)
                all_data.append({
                    'project_id': project.id,
                    'project_name': project.name,
                    'data': project_data
                })
                total_stakeholders += len(project_data.get('stakeholder_details', []))
            except Exception as e:
                logger.warning(f"Failed to get analytics for project {project.name}: {str(e)}")
        
        # Generate summary
        summary = {
            'total_projects': len(all_data),
            'total_stakeholders': total_stakeholders,
            'generated_at': timezone.now().isoformat(),
            'report_period': get_report_period(report),
        }
        
        return {
            'summary': summary,
            'project_data': all_data,
            'data_points_count': total_stakeholders,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error generating stakeholder analytics: {str(e)}")
        return {
            'error': str(e),
            'data_points_count': 0,
            'success': False
        }


def generate_generic_report(report):
    """Generate generic report data based on configuration."""
    try:
        # Basic report structure
        data = {
            'report_name': report.name,
            'report_type': report.report_type,
            'generated_at': timezone.now().isoformat(),
            'organization': report.organization.name,
            'configuration': report.configuration,
            'data_points_count': 1,
            'success': True
        }
        
        # Add basic metrics if available
        recent_metrics = SystemMetric.objects.filter(
            organization=report.organization,
            timestamp__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        data['recent_metrics_count'] = recent_metrics
        data['data_points_count'] = recent_metrics
        
        return data
        
    except Exception as e:
        logger.error(f"Error generating generic report: {str(e)}")
        return {
            'error': str(e),
            'data_points_count': 0,
            'success': False
        }


def should_send_email(report):
    """Check if email should be sent for this report."""
    schedule_config = report.schedule_config or {}
    return schedule_config.get('send_email', True)


def get_report_recipients(report):
    """Get email recipients for the report."""
    schedule_config = report.schedule_config or {}
    
    # Get recipients from configuration
    recipient_emails = schedule_config.get('recipients', [])
    
    # Default to report creator if no recipients specified
    if not recipient_emails:
        if report.created_by and report.created_by.email:
            recipient_emails.append(report.created_by.email)
    
    # Add allowed users if specified
    if schedule_config.get('include_allowed_users', False):
        for user in report.allowed_users.all():
            if user.email and user.email not in recipient_emails:
                recipient_emails.append(user.email)
    
    return recipient_emails


def update_next_execution_time(report):
    """Update the next execution time based on schedule configuration."""
    try:
        schedule_config = report.schedule_config or {}
        recurrence = schedule_config.get('recurrence', 'daily')
        
        now = timezone.now()
        
        if recurrence == 'hourly':
            next_execution = now + timedelta(hours=1)
        elif recurrence == 'daily':
            next_execution = now + timedelta(days=1)
        elif recurrence == 'weekly':
            next_execution = now + timedelta(weeks=1)
        elif recurrence == 'monthly':
            # Approximate monthly (30 days)
            next_execution = now + timedelta(days=30)
        else:
            # Default to daily
            next_execution = now + timedelta(days=1)
        
        report.last_executed_at = now
        report.next_execution_at = next_execution
        report.save(update_fields=['last_executed_at', 'next_execution_at'])
        
        logger.info(f"Updated next execution time for {report.name}: {next_execution}")
        
    except Exception as e:
        logger.error(f"Error updating next execution time for {report.name}: {str(e)}")


def get_report_period(report):
    """Get the report period based on configuration."""
    schedule_config = report.schedule_config or {}
    recurrence = schedule_config.get('recurrence', 'daily')
    
    now = timezone.now()
    
    if recurrence == 'hourly':
        start = now - timedelta(hours=1)
    elif recurrence == 'daily':
        start = now - timedelta(days=1)
    elif recurrence == 'weekly':
        start = now - timedelta(weeks=1)
    elif recurrence == 'monthly':
        start = now - timedelta(days=30)
    else:
        start = now - timedelta(days=1)
    
    return {
        'start': start.isoformat(),
        'end': now.isoformat(),
        'recurrence': recurrence
    }