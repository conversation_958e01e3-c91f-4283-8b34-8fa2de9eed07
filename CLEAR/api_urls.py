"""
API URL patterns for the CLEAR application.

REST API endpoints for external integrations and AJAX calls.
"""


"""


# Create a router and register viewsets
router = DefaultRouter()
router.register(r'projects', api_views.ProjectViewSet)
router.register(r'utilities', api_views.UtilityViewSet)
router.register(r'conflicts', api_views.ConflictViewSet)
router.register(r'tasks', api_views.TaskViewSet)
router.register(r'comments', api_views.CommentViewSet)
router.register(r'notifications', api_views.NotificationViewSet)
router.register(r'coordinate-systems', api_views.CoordinateSystemViewSet)
router.register(r'documents', api_views.DocumentViewSet)
router.register(r'reports', api_views.ReportViewSet)
router.register(r'time-entries', api_views.TimeEntryViewSet)
router.register(r'feature-requests', api_views.FeatureRequestViewSet)
router.register(r'knowledge-articles', api_views.KnowledgeArticleViewSet)
router.register(r'notes', api_views.NoteViewSet)
router.register(r'workflows', api_views.WorkflowViewSet)
router.register(r'stakeholders', api_views.StakeholderViewSet)
router.register(r'invoices', api_views.InvoiceViewSet)
router.register(r'gis-layers', api_views.GISLayerViewSet)
router.register(r'organizations', api_views.OrganizationViewSet)

app_name = 'api'

urlpatterns = [
    # DRF router URLs
    path('', include(router.urls)),
    
    # Authentication endpoints
    path('auth/login/', api_views.LoginAPIView.as_view(), name='login'),
    path('auth/logout/', api_views.LogoutAPIView.as_view(), name='logout'),
    path('auth/user/', api_views.CurrentUserAPIView.as_view(), name='current_user'),
    
    # Spatial analysis endpoints
    path('spatial/conflict-detection/', api_views.ConflictDetectionAPIView.as_view(), name='conflict_detection'),
    path('spatial/coordinate-transform/', api_views.CoordinateTransformAPIView.as_view(), name='coordinate_transform'),
    path('spatial/buffer-analysis/', api_views.BufferAnalysisAPIView.as_view(), name='buffer_analysis'),
    
    # File upload endpoints
    path('uploads/cad-files/', api_views.CADFileUploadAPIView.as_view(), name='cad_upload'),
    path('uploads/documents/', api_views.DocumentUploadAPIView.as_view(), name='document_upload'),
    
    # Dashboard data endpoints
    path('dashboard/stats/', api_views.DashboardStatsAPIView.as_view(), name='dashboard_stats'),
    path('dashboard/recent-activity/', api_views.RecentActivityAPIView.as_view(), name='recent_activity'),
    
    # Real-time updates
    path('realtime/project-updates/<str:project_id>/', api_views.ProjectUpdatesAPIView.as_view(), name='project_updates'),
    path('realtime/chat-messages/', api_views.ChatMessagesAPIView.as_view(), name='chat_messages'),
    
    # Export endpoints
    path('export/project/<str:project_id>/pdf/', api_views.ProjectPDFExportAPIView.as_view(), name='project_pdf_export'),
    path('export/project/<str:project_id>/excel/', api_views.ProjectExcelExportAPIView.as_view(), name='project_excel_export'),
    path('export/conflicts/<str:project_id>/csv/', api_views.ConflictCSVExportAPIView.as_view(), name='conflict_csv_export'),
    
    # ========== EXTENDED API ENDPOINTS ==========
    
    # Communication endpoints
    path('chat/messages/', api_views.ChatMessageAPIView.as_view(), name='chat_messages'),
    path('whispers/', api_views.WhisperMessageAPIView.as_view(), name='whisper_messages'),
    
    # Analytics endpoints
    path('analytics/project/<str:project_id>/', api_views.ProjectAnalyticsAPIView.as_view(), name='project_analytics'),
    path('analytics/system/', api_views.SystemMetricsAPIView.as_view(), name='system_metrics'),
    
    # Import/Export endpoints
    path('import/data/', api_views.DataImportAPIView.as_view(), name='data_import'),
    path('export/data/', api_views.DataExportAPIView.as_view(), name='data_export'),
    
    # Advanced search endpoints
    path('search/global/', api_views.DataExportAPIView.as_view(), name='global_search'),
    path('search/projects/', api_views.ProjectViewSet.as_view({'get': 'list'}), name='search_projects'),
]
"""