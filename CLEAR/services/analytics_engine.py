"""
Analytics Engine service for CLEAR.
"""

import logging
from datetime import timedelta
from typing import Any, Dict
from django.db.models import Sum
from django.utils import timezone
from ..models import Project, Task
from ..models.financial import TimeEntry


logger = logging.getLogger(__name__)

class AnalyticsEngine:
    """Analytics engine for generating reports and insights."""
    
    def __init__(self, user):
        self.user = user
    
    def get_project_metrics(self) -> Dict[str, Any]:
        """Get project-related metrics."""
        
        return {
            'total_projects': Project.objects.filter(
                assigned_to=self.user
            ).count(),
            'active_projects': Project.objects.filter(
                assigned_to=self.user,
                status='active'
            ).count(),
            'completed_projects': Project.objects.filter(
                assigned_to=self.user,
                status='completed'
            ).count(),
        }
    
    def get_time_metrics(self) -> Dict[str, Any]:
        """Get time tracking metrics."""
        
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        
        week_time = TimeEntry.objects.filter(
            user=self.user,
            date__gte=week_start
        ).aggregate(total=Sum('duration_minutes'))['total'] or 0
        
        return {
            'week_hours': week_time / 60.0,
            'today_hours': TimeEntry.objects.filter(
                user=self.user,
                date=today
            ).aggregate(total=Sum('duration_minutes'))['total'] or 0
        }
    
    def get_task_metrics(self) -> Dict[str, Any]:
        """Get task-related metrics."""
        
        return {
            'pending_tasks': Task.objects.filter(
                assigned_to=self.user,
                status='pending'
            ).count(),
            'in_progress_tasks': Task.objects.filter(
                assigned_to=self.user,
                status='in_progress'
            ).count(),
            'completed_tasks': Task.objects.filter(
                assigned_to=self.user,
                status='completed'
            ).count(),
        }