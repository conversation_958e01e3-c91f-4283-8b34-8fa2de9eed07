"""
Predictive Relationship Engine

Provides ML-based relationship prediction capabilities including:
- Collaborative filtering for entity co-occurrence patterns
- Graph embedding techniques (Node2Vec-style) for similarity scoring
- Pattern recognition for likely entity relationships
- Anomaly detection for unusual relationship patterns
- Relationship strength learning and prediction

This service extends the Knowledge Graph Analytics with predictive capabilities
for proactive relationship suggestions and intelligence insights.
"""


import logging
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, List
import networkx as nx
import numpy as np
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from django.utils import timezone
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from ..models import (
    ChainedEntityMention,
    EntityMention,
    EntityRelationship,
    KnowledgeGraphNode,
)
from .knowledge_graph_analytics import KnowledgeGraphAnalytics

logger = logging.getLogger(__name__)


class PredictiveRelationshipEngine:
    """ML-powered engine for predicting entity relationships and patterns"""
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'PREDICTIVE_ENGINE_CACHE_TIMEOUT', 7200)  # 2 hours
        self.min_cooccurrence_threshold = 3
        self.similarity_threshold = 0.3
        self.max_predictions_per_entity = 10
        
    # ========================================
    # COLLABORATIVE FILTERING
    # ========================================
    
    def generate_collaborative_predictions(self, entity_type: str = None, 
                                        entity_id: str = None, 
                                        top_k: int = 5) -> List[Dict[str, Any]]:
        """Generate relationship predictions using collaborative filtering"""
        try:
            cache_key = f"collab_predictions_{entity_type}_{entity_id}_{top_k}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            logger.info("Generating collaborative filtering predictions...")
            
            # Build entity co-occurrence matrix
            cooccurrence_matrix = self._build_cooccurrence_matrix()
            
            if not cooccurrence_matrix:
                return []
            
            # Generate predictions based on collaborative filtering
            if entity_type and entity_id:
                predictions = self._predict_for_entity(
                    entity_type, entity_id, cooccurrence_matrix, top_k
                )
            else:
                predictions = self._predict_for_all_entities(
                    cooccurrence_matrix, top_k
                )
            
            # Cache results
            cache.set(cache_key, predictions, self.cache_timeout)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating collaborative predictions: {str(e)}")
            return []
    
    def _build_cooccurrence_matrix(self) -> Dict[str, Dict[str, float]]:
        """Build entity co-occurrence matrix from entity mentions"""
        try:
            # Get all entity mentions grouped by content object
            mentions_by_content = defaultdict(list)
            
            mentions = EntityMention.objects.select_related().values(
                'content_type_id', 'object_id', 'mentioned_entity_type', 'mentioned_entity_id'
            )
            
            for mention in mentions:
                content_key = f"{mention['content_type_id']}:{mention['object_id']}"
                entity_key = f"{mention['mentioned_entity_type']}:{mention['mentioned_entity_id']}"
                mentions_by_content[content_key].append(entity_key)
            
            # Build co-occurrence counts
            cooccurrence_counts = defaultdict(lambda: defaultdict(int))
            entity_totals = defaultdict(int)
            
            for content_key, entities in mentions_by_content.items():
                # Count total mentions for each entity
                for entity in entities:
                    entity_totals[entity] += 1
                
                # Count co-occurrences
                for i, entity1 in enumerate(entities):
                    for entity2 in entities[i+1:]:
                        cooccurrence_counts[entity1][entity2] += 1
                        cooccurrence_counts[entity2][entity1] += 1
            
            # Convert to normalized scores
            cooccurrence_matrix = {}
            for entity1, cooccurrences in cooccurrence_counts.items():
                if entity_totals[entity1] < self.min_cooccurrence_threshold:
                    continue
                    
                cooccurrence_matrix[entity1] = {}
                for entity2, count in cooccurrences.items():
                    if count >= self.min_cooccurrence_threshold:
                        # Calculate association strength using PMI (Pointwise Mutual Information)
                        total_documents = len(mentions_by_content)
                        p_entity1 = entity_totals[entity1] / total_documents
                        p_entity2 = entity_totals[entity2] / total_documents
                        p_together = count / total_documents
                        
                        if p_entity1 * p_entity2 > 0:
                            pmi = np.log(p_together / (p_entity1 * p_entity2))
                            # Normalize PMI to 0-1 range
                            normalized_score = max(0, min(1, (pmi + 10) / 20))
                            cooccurrence_matrix[entity1][entity2] = normalized_score
            
            logger.info(f"Built co-occurrence matrix with {len(cooccurrence_matrix)} entities")
            return cooccurrence_matrix
            
        except Exception as e:
            logger.error(f"Error building co-occurrence matrix: {str(e)}")
            return {}
    
    def _predict_for_entity(self, entity_type: str, entity_id: str, 
                          cooccurrence_matrix: Dict, top_k: int) -> List[Dict[str, Any]]:
        """Generate predictions for a specific entity"""
        try:
            entity_key = f"{entity_type}:{entity_id}"
            
            if entity_key not in cooccurrence_matrix:
                return []
            
            # Get entities this entity co-occurs with
            related_entities = cooccurrence_matrix[entity_key]
            
            # Find similar entities based on co-occurrence patterns
            candidates = defaultdict(float)
            
            for related_entity, score in related_entities.items():
                if related_entity in cooccurrence_matrix:
                    # Find entities that co-occur with this related entity
                    for candidate, candidate_score in cooccurrence_matrix[related_entity].items():
                        if candidate != entity_key and candidate not in related_entities:
                            # Weight by the strength of the intermediate relationship
                            candidates[candidate] += score * candidate_score
            
            # Sort by prediction score
            predictions = []
            for candidate, score in sorted(candidates.items(), key=lambda x: x[1], reverse=True)[:top_k]:
                candidate_type, candidate_id = candidate.split(':', 1)
                
                # Get entity name
                try:
                    node = KnowledgeGraphNode.objects.get(
                        entity_type=candidate_type,
                        entity_id=candidate_id
                    )
                    entity_name = node.entity_name
                except KnowledgeGraphNode.DoesNotExist:
                    entity_name = f"{candidate_type}-{candidate_id}"
                
                predictions.append({
                    'entity_type': candidate_type,
                    'entity_id': candidate_id,
                    'entity_name': entity_name,
                    'prediction_score': round(score, 4),
                    'prediction_type': 'collaborative_filtering',
                    'reasoning': f"Often mentioned together with entities similar to {entity_type}-{entity_id}"
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting for entity {entity_type}:{entity_id}: {str(e)}")
            return []
    
    def _predict_for_all_entities(self, cooccurrence_matrix: Dict, top_k: int) -> List[Dict[str, Any]]:
        """Generate global relationship predictions"""
        try:
            all_predictions = []
            
            # Generate predictions for each entity
            for entity_key in list(cooccurrence_matrix.keys())[:20]:  # Limit for performance
                entity_type, entity_id = entity_key.split(':', 1)
                entity_predictions = self._predict_for_entity(
                    entity_type, entity_id, cooccurrence_matrix, top_k
                )
                all_predictions.extend(entity_predictions)
            
            # Sort by prediction score and return top results
            all_predictions.sort(key=lambda x: x['prediction_score'], reverse=True)
            return all_predictions[:top_k * 5]  # Return more for global view
            
        except Exception as e:
            logger.error(f"Error predicting for all entities: {str(e)}")
            return []
    
    # ========================================
    # GRAPH EMBEDDING SIMILARITY
    # ========================================
    
    def calculate_entity_similarity_scores(self, target_entity_type: str = None,
                                         target_entity_id: str = None) -> List[Dict[str, Any]]:
        """Calculate entity similarities using graph embedding techniques"""
        try:
            cache_key = f"similarity_scores_{target_entity_type}_{target_entity_id}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            logger.info("Calculating entity similarity scores...")
            
            # Build feature vectors for entities
            feature_vectors = self._build_entity_feature_vectors()
            
            if not feature_vectors:
                return []
            
            # Calculate similarities
            similarities = self._calculate_cosine_similarities(
                feature_vectors, target_entity_type, target_entity_id
            )
            
            # Cache results
            cache.set(cache_key, similarities, self.cache_timeout)
            
            return similarities
            
        except Exception as e:
            logger.error(f"Error calculating entity similarities: {str(e)}")
            return []
    
    def _build_entity_feature_vectors(self) -> Dict[str, np.ndarray]:
        """Build feature vectors for entities based on their characteristics"""
        try:
            feature_vectors = {}
            
            # Get all knowledge graph nodes
            nodes = KnowledgeGraphNode.objects.all()
            
            # Build feature matrix
            features = []
            entity_keys = []
            
            for node in nodes:
                entity_key = f"{node.entity_type}:{node.entity_id}"
                entity_keys.append(entity_key)
                
                # Create feature vector
                feature_vector = [
                    node.importance_score,
                    node.centrality_score,
                    node.activity_level,
                    node.total_mentions,
                    len(node.keywords) if node.keywords else 0,
                ]
                
                # Add entity type one-hot encoding
                entity_types = ['project', 'task', 'user', 'stakeholder', 'utility', 'conflict', 'document', 'note']
                type_encoding = [1 if node.entity_type == et else 0 for et in entity_types]
                feature_vector.extend(type_encoding)
                
                # Add relationship count features
                incoming_rels = EntityRelationship.objects.filter(
                    target_entity_type=node.entity_type,
                    target_entity_id=node.entity_id
                ).count()
                
                outgoing_rels = EntityRelationship.objects.filter(
                    source_entity_type=node.entity_type,
                    source_entity_id=node.entity_id
                ).count()
                
                feature_vector.extend([incoming_rels, outgoing_rels])
                
                features.append(feature_vector)
            
            # Convert to numpy array and normalize
            if features:
                feature_matrix = np.array(features)
                
                # Normalize features
                scaler = StandardScaler()
                normalized_features = scaler.fit_transform(feature_matrix)
                
                # Create feature vectors dictionary
                for i, entity_key in enumerate(entity_keys):
                    feature_vectors[entity_key] = normalized_features[i]
            
            logger.info(f"Built feature vectors for {len(feature_vectors)} entities")
            return feature_vectors
            
        except Exception as e:
            logger.error(f"Error building feature vectors: {str(e)}\"")
            return {}
    
    def _calculate_cosine_similarities(self, feature_vectors: Dict[str, np.ndarray],
                                     target_entity_type: str = None,
                                     target_entity_id: str = None) -> List[Dict[str, Any]]:
        """Calculate cosine similarities between entities"""
        try:
            similarities = []
            
            if target_entity_type and target_entity_id:
                # Calculate similarities for specific entity
                target_key = f"{target_entity_type}:{target_entity_id}"
                if target_key not in feature_vectors:
                    return []
                
                target_vector = feature_vectors[target_key]
                
                for entity_key, vector in feature_vectors.items():
                    if entity_key != target_key:
                        similarity = cosine_similarity([target_vector], [vector])[0][0]
                        
                        if similarity > self.similarity_threshold:
                            entity_type, entity_id = entity_key.split(':', 1)
                            
                            # Get entity name
                            try:
                                node = KnowledgeGraphNode.objects.get(
                                    entity_type=entity_type,
                                    entity_id=entity_id
                                )
                                entity_name = node.entity_name
                            except KnowledgeGraphNode.DoesNotExist:
                                entity_name = f"{entity_type}-{entity_id}"
                            
                            similarities.append({
                                'entity_type': entity_type,
                                'entity_id': entity_id,
                                'entity_name': entity_name,
                                'similarity_score': round(float(similarity), 4),
                                'prediction_type': 'feature_similarity',
                                'reasoning': f"Similar characteristics and patterns to {target_entity_type}-{target_entity_id}"
                            })
            else:
                # Calculate global similarities - find most similar pairs
                entity_keys = list(feature_vectors.keys())
                vectors = np.array(list(feature_vectors.values()))
                
                # Calculate similarity matrix
                similarity_matrix = cosine_similarity(vectors)
                
                # Find top similarities
                for i in range(len(entity_keys)):
                    for j in range(i + 1, len(entity_keys)):
                        similarity = similarity_matrix[i][j]
                        
                        if similarity > self.similarity_threshold:
                            entity1_type, entity1_id = entity_keys[i].split(':', 1)
                            entity2_type, entity2_id = entity_keys[j].split(':', 1)
                            
                            similarities.append({
                                'entity1_type': entity1_type,
                                'entity1_id': entity1_id,
                                'entity2_type': entity2_type,
                                'entity2_id': entity2_id,
                                'similarity_score': round(float(similarity), 4),
                                'prediction_type': 'mutual_similarity'
                            })
            
            # Sort by similarity score
            similarities.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            return similarities[:self.max_predictions_per_entity]
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarities: {str(e)}")
            return []
    
    # ========================================
    # PATTERN RECOGNITION
    # ========================================
    
    def detect_relationship_patterns(self) -> Dict[str, Any]:
        """Detect patterns in existing relationships for prediction"""
        try:
            cache_key = "relationship_patterns"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            logger.info("Detecting relationship patterns...")
            
            patterns = {
                'temporal_patterns': self._detect_temporal_patterns(),
                'hierarchy_patterns': self._detect_hierarchy_patterns(),
                'cluster_patterns': self._detect_cluster_patterns(),
                'communication_patterns': self._detect_communication_patterns()
            }
            
            # Cache results
            cache.set(cache_key, patterns, self.cache_timeout)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting relationship patterns: {str(e)}")
            return {}
    
    def _detect_temporal_patterns(self) -> List[Dict[str, Any]]:
        """Detect temporal patterns in relationship formation"""
        try:
            # Analyze when relationships typically form
            relationships = EntityRelationship.objects.filter(
                discovered_at__gte=timezone.now() - timedelta(days=90)
            ).order_by('discovered_at')
            
            patterns = []
            
            # Group by time periods
            daily_counts = defaultdict(int)
            weekly_counts = defaultdict(int)
            
            for rel in relationships:
                day_key = rel.discovered_at.strftime('%A')  # Day of week
                week_key = rel.discovered_at.isocalendar()[1]  # Week number
                
                daily_counts[day_key] += 1
                weekly_counts[week_key] += 1
            
            # Find peak days
            if daily_counts:
                peak_day = max(daily_counts, key=daily_counts.get)
                patterns.append({
                    'type': 'peak_discovery_day',
                    'pattern': f"Most relationships discovered on {peak_day}",
                    'confidence': daily_counts[peak_day] / sum(daily_counts.values()),
                    'data': dict(daily_counts)
                })
            
            # Find growth trends
            if len(weekly_counts) >= 4:
                weeks = sorted(weekly_counts.keys())
                recent_avg = np.mean([weekly_counts[w] for w in weeks[-4:]])
                earlier_avg = np.mean([weekly_counts[w] for w in weeks[:4]])
                
                if recent_avg > earlier_avg * 1.2:
                    patterns.append({
                        'type': 'relationship_growth',
                        'pattern': 'Increasing relationship discovery rate',
                        'confidence': min(1.0, (recent_avg - earlier_avg) / earlier_avg),
                        'trend': 'increasing'
                    })
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting temporal patterns: {str(e)}")
            return []
    
    def _detect_hierarchy_patterns(self) -> List[Dict[str, Any]]:
        """Detect hierarchical patterns in entity relationships"""
        try:
            patterns = []
            
            # Analyze entity chains from ChainedEntityMention
            chains = ChainedEntityMention.objects.select_related('base_mention').all()
            
            # Group by chain patterns
            chain_patterns = defaultdict(int)
            
            for chain in chains:
                if chain.chain_path:
                    # Extract pattern from chain path
                    chain_types = []
                    for segment in chain.chain_path:
                        if 'entity_type' in segment:
                            chain_types.append(segment['entity_type'])
                    
                    if len(chain_types) >= 2:
                        pattern_key = ' -> '.join(chain_types)
                        chain_patterns[pattern_key] += 1
            
            # Find common chain patterns
            for pattern, count in sorted(chain_patterns.items(), key=lambda x: x[1], reverse=True)[:5]:
                patterns.append({
                    'type': 'common_chain_pattern',
                    'pattern': pattern,
                    'occurrences': count,
                    'confidence': count / len(chains) if chains else 0
                })
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting hierarchy patterns: {str(e)}")
            return []
    
    def _detect_cluster_patterns(self) -> List[Dict[str, Any]]:
        """Detect clustering patterns in entity relationships"""
        try:
            # Get entity relationship graph
            analytics = KnowledgeGraphAnalytics()
            G = analytics.build_networkx_graph()
            
            if G.number_of_nodes() < 5:
                return []
            
            patterns = []
            
            # Detect communities
            communities = analytics.detect_communities(G)
            
            if communities.get('communities'):
                for i, community in enumerate(communities['communities'][:3]):  # Top 3 communities
                    # Analyze entity types in community
                    entity_types = defaultdict(int)
                    for node_id in community['nodes']:
                        entity_type = node_id.split(':', 1)[0]
                        entity_types[entity_type] += 1
                    
                    dominant_type = max(entity_types, key=entity_types.get)
                    patterns.append({
                        'type': 'entity_cluster',
                        'pattern': f"Cluster {i+1} dominated by {dominant_type} entities",
                        'size': community['size'],
                        'density': community['density'],
                        'entity_distribution': dict(entity_types)
                    })
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting cluster patterns: {str(e)}")
            return []
    
    def _detect_communication_patterns(self) -> List[Dict[str, Any]]:
        """Detect communication patterns that lead to relationships"""
        try:
            patterns = []
            
            # Analyze entity mentions that led to relationship discovery
            recent_relationships = EntityRelationship.objects.filter(
                discovered_at__gte=timezone.now() - timedelta(days=30),
                discovered_through='entity_mentions'
            )
            
            # Count mentions by content type
            content_type_counts = defaultdict(int)
            for rel in recent_relationships:
                for mention in rel.supporting_communications.all():
                    if hasattr(mention, 'content_type'):
                        content_type_counts[mention.content_type.model] += 1
            
            if content_type_counts:
                most_productive = max(content_type_counts, key=content_type_counts.get)
                patterns.append({
                    'type': 'productive_communication_channel',
                    'pattern': f"Most relationship discoveries happen in {most_productive}",
                    'count': content_type_counts[most_productive],
                    'percentage': content_type_counts[most_productive] / sum(content_type_counts.values()) * 100
                })
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting communication patterns: {str(e)}")
            return []
    
    # ========================================
    # ANOMALY DETECTION
    # ========================================
    
    def detect_relationship_anomalies(self) -> List[Dict[str, Any]]:
        """Detect unusual patterns or anomalies in relationships"""
        try:
            cache_key = "relationship_anomalies"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            logger.info("Detecting relationship anomalies...")
            
            anomalies = []
            
            # Detect relationships with unusual strength patterns
            anomalies.extend(self._detect_strength_anomalies())
            
            # Detect isolated entities that should have relationships
            anomalies.extend(self._detect_isolation_anomalies())
            
            # Detect unusually dense relationship clusters
            anomalies.extend(self._detect_density_anomalies())
            
            # Cache results
            cache.set(cache_key, anomalies, self.cache_timeout)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error detecting relationship anomalies: {str(e)}")
            return []
    
    def _detect_strength_anomalies(self) -> List[Dict[str, Any]]:
        """Detect relationships with unusual strength scores"""
        try:
            anomalies = []
            
            # Get relationship strength statistics
            relationships = EntityRelationship.objects.all()
            strengths = [rel.strength for rel in relationships]
            
            if len(strengths) < 10:
                return anomalies
            
            mean_strength = np.mean(strengths)
            std_strength = np.std(strengths)
            
            # Find outliers (more than 2 standard deviations)
            threshold_high = mean_strength + (2 * std_strength)
            threshold_low = mean_strength - (2 * std_strength)
            
            for rel in relationships:
                if rel.strength > threshold_high:
                    anomalies.append({
                        'type': 'unusually_strong_relationship',
                        'severity': 'medium',
                        'relationship_id': rel.id,
                        'source': f"{rel.source_entity_type}:{rel.source_entity_id}",
                        'target': f"{rel.target_entity_type}:{rel.target_entity_id}",
                        'strength': rel.strength,
                        'expected_range': f"{mean_strength:.2f} ± {std_strength:.2f}",
                        'description': f"Relationship strength ({rel.strength:.2f}) significantly higher than average"
                    })
                elif rel.strength < threshold_low and rel.strength > 0:
                    anomalies.append({
                        'type': 'unusually_weak_relationship',
                        'severity': 'low',
                        'relationship_id': rel.id,
                        'source': f"{rel.source_entity_type}:{rel.source_entity_id}",
                        'target': f"{rel.target_entity_type}:{rel.target_entity_id}",
                        'strength': rel.strength,
                        'expected_range': f"{mean_strength:.2f} ± {std_strength:.2f}",
                        'description': f"Relationship strength ({rel.strength:.2f}) significantly lower than average"
                    })
            
            return anomalies[:5]  # Return top 5 anomalies
            
        except Exception as e:
            logger.error(f"Error detecting strength anomalies: {str(e)}")
            return []
    
    def _detect_isolation_anomalies(self) -> List[Dict[str, Any]]:
        """Detect entities that are unusually isolated"""
        try:
            anomalies = []
            
            # Find entities with high activity but few relationships
            nodes = KnowledgeGraphNode.objects.filter(
                total_mentions__gte=5,  # Mentioned at least 5 times
                importance_score__gte=0.3  # Above average importance
            )
            
            for node in nodes:
                # Count relationships
                rel_count = EntityRelationship.objects.filter(
                    Q(source_entity_type=node.entity_type, source_entity_id=node.entity_id) |
                    Q(target_entity_type=node.entity_type, target_entity_id=node.entity_id)
                ).count()
                
                # Expected relationships based on mentions and importance
                expected_rels = min(10, node.total_mentions * node.importance_score)
                
                if rel_count < expected_rels * 0.3:  # Less than 30% of expected
                    anomalies.append({
                        'type': 'isolated_important_entity',
                        'severity': 'high',
                        'entity_type': node.entity_type,
                        'entity_id': node.entity_id,
                        'entity_name': node.entity_name,
                        'actual_relationships': rel_count,
                        'expected_relationships': int(expected_rels),
                        'mentions': node.total_mentions,
                        'importance': node.importance_score,
                        'description': f"Entity has {node.total_mentions} mentions and high importance but only {rel_count} relationships"
                    })
            
            return anomalies[:3]  # Return top 3 isolation anomalies
            
        except Exception as e:
            logger.error(f"Error detecting isolation anomalies: {str(e)}")
            return []
    
    def _detect_density_anomalies(self) -> List[Dict[str, Any]]:
        """Detect unusually dense relationship clusters"""
        try:
            anomalies = []
            
            # Get entity relationship graph
            analytics = KnowledgeGraphAnalytics()
            G = analytics.build_networkx_graph()
            
            if G.number_of_nodes() < 10:
                return anomalies
            
            # Calculate overall graph density
            overall_density = nx.density(G)
            
            # Find dense subgraphs
            communities = analytics.detect_communities(G)
            
            if communities.get('communities'):
                for community in communities['communities']:
                    if community['density'] > overall_density * 3:  # 3x denser than average
                        anomalies.append({
                            'type': 'unusually_dense_cluster',
                            'severity': 'medium',
                            'cluster_size': community['size'],
                            'cluster_density': community['density'],
                            'overall_density': overall_density,
                            'density_ratio': community['density'] / overall_density,
                            'description': f"Cluster of {community['size']} entities with {community['density']:.2f} density (vs {overall_density:.2f} average)"
                        })
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error detecting density anomalies: {str(e)}")
            return []
    
    # ========================================
    # COMPREHENSIVE PREDICTION ANALYSIS
    # ========================================
    
    def generate_comprehensive_predictions(self, entity_type: str = None,
                                         entity_id: str = None) -> Dict[str, Any]:
        """Generate comprehensive relationship predictions using all methods"""
        try:
            logger.info("Generating comprehensive relationship predictions...")
            
            predictions = {
                'collaborative_filtering': self.generate_collaborative_predictions(
                    entity_type, entity_id, top_k=5
                ),
                'similarity_analysis': self.calculate_entity_similarity_scores(
                    entity_type, entity_id
                ),
                'pattern_analysis': self.detect_relationship_patterns(),
                'anomaly_detection': self.detect_relationship_anomalies(),
                'summary': {
                    'timestamp': timezone.now().isoformat(),
                    'target_entity': f"{entity_type}:{entity_id}" if entity_type and entity_id else "global",
                    'prediction_methods': ['collaborative_filtering', 'similarity_analysis', 'pattern_recognition', 'anomaly_detection']
                }
            }
            
            # Generate consolidated recommendations
            predictions['recommendations'] = self._generate_consolidated_recommendations(predictions)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating comprehensive predictions: {str(e)}")
            return {}
    
    def _generate_consolidated_recommendations(self, predictions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate consolidated recommendations from all prediction methods"""
        try:
            recommendations = []
            
            # High-confidence collaborative filtering suggestions
            collab_predictions = predictions.get('collaborative_filtering', [])
            for pred in collab_predictions[:3]:
                if pred.get('prediction_score', 0) > 0.5:
                    recommendations.append({
                        'type': 'relationship_suggestion',
                        'priority': 'high',
                        'confidence': pred['prediction_score'],
                        'title': f"Consider connecting with {pred['entity_name']}",
                        'description': pred['reasoning'],
                        'action': f"Review potential relationship with {pred['entity_type']}-{pred['entity_id']}",
                        'method': 'collaborative_filtering'
                    })
            
            # High-similarity entities
            similarity_predictions = predictions.get('similarity_analysis', [])
            for pred in similarity_predictions[:2]:
                if pred.get('similarity_score', 0) > 0.7:
                    recommendations.append({
                        'type': 'similar_entity',
                        'priority': 'medium',
                        'confidence': pred['similarity_score'],
                        'title': f"Similar entity: {pred['entity_name']}",
                        'description': pred['reasoning'],
                        'action': f"Explore collaboration opportunities with {pred['entity_type']}-{pred['entity_id']}",
                        'method': 'similarity_analysis'
                    })
            
            # Pattern-based insights
            patterns = predictions.get('pattern_analysis', {})
            for pattern_type, pattern_list in patterns.items():
                if pattern_list and isinstance(pattern_list, list):
                    for pattern in pattern_list[:1]:  # Top pattern per type
                        if pattern.get('confidence', 0) > 0.6:
                            recommendations.append({
                                'type': 'pattern_insight',
                                'priority': 'low',
                                'confidence': pattern['confidence'],
                                'title': f"Pattern: {pattern['pattern']}",
                                'description': f"Detected {pattern_type} pattern",
                                'action': "Consider this pattern for future relationship strategies",
                                'method': 'pattern_recognition'
                            })
            
            # Critical anomalies
            anomalies = predictions.get('anomaly_detection', [])
            for anomaly in anomalies:
                if anomaly.get('severity') == 'high':
                    recommendations.append({
                        'type': 'anomaly_alert',
                        'priority': 'high',
                        'confidence': 0.8,
                        'title': f"Anomaly: {anomaly['type']}",
                        'description': anomaly['description'],
                        'action': "Investigate and address this unusual pattern",
                        'method': 'anomaly_detection'
                    })
            
            # Sort by priority and confidence
            priority_order = {'high': 3, 'medium': 2, 'low': 1}
            recommendations.sort(
                key=lambda x: (priority_order.get(x['priority'], 0), x['confidence']),
                reverse=True
            )
            
            return recommendations[:10]  # Return top 10 recommendations
            
        except Exception as e:
            logger.error(f"Error generating consolidated recommendations: {str(e)}")
            return []