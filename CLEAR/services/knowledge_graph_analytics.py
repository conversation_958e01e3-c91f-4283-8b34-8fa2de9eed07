"""
Advanced Knowledge Graph Analytics Service

Provides sophisticated graph analysis capabilities including:
- Centrality scoring (PageRank, Betweenness, Closeness, Eigenvector)
- Community detection (Louvain algorithm, Modularity optimization)
- Predictive relationship analysis with ML-based recommendations
- Network analysis metrics and anomaly detection
- Communication flow analysis and bottleneck identification

This service transforms the basic knowledge graph into an intelligent
entity relationship analytics platform with predictive capabilities.
"""
"""



import logging
from collections import defaultdict
from datetime import timedelta
from typing import Any, Dict, List, Optional
import networkx as nx
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from ..models import EntityRelationship, KnowledgeGraphNode
import community.community_louvain as community_louvain

"""




logger = logging.getLogger(__name__)


class KnowledgeGraphAnalytics:
    """Advanced analytics engine for knowledge graph analysis"""
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'KNOWLEDGE_GRAPH_CACHE_TIMEOUT', 3600)  # 1 hour
        self.min_nodes_for_analytics = 5  # Minimum nodes required for meaningful analytics
        self.max_nodes_for_realtime = 1000  # Maximum nodes for real-time processing
        
    # ========================================
    # GRAPH CONSTRUCTION
    # ========================================
    
    def build_networkx_graph(self, include_weak_relationships: bool = False, 
                           entity_types: List[str] = None, 
                           time_window_days: int = None) -> nx.Graph:
        """Build NetworkX graph from knowledge graph data"""
        try:
            # Get nodes
            nodes_query = KnowledgeGraphNode.objects.filter(importance_score__gt=0.01)
            
            if entity_types:
                nodes_query = nodes_query.filter(entity_type__in=entity_types)
            
            if time_window_days:
                cutoff_date = timezone.now() - timedelta(days=time_window_days)
                nodes_query = nodes_query.filter(last_activity__gte=cutoff_date)
            
            nodes = list(nodes_query.values(
                'entity_type', 'entity_id', 'entity_name', 'importance_score',
                'centrality_score', 'activity_level', 'total_mentions'
            ))
            
            # Get relationships
            relationships_query = EntityRelationship.objects.filter(strength__gt=0.1)
            
            if not include_weak_relationships:
                relationships_query = relationships_query.filter(strength__gte=0.3)
            
            if time_window_days:
                relationships_query = relationships_query.filter(last_confirmed_at__gte=cutoff_date)
            
            relationships = list(relationships_query.values(
                'source_entity_type', 'source_entity_id',
                'target_entity_type', 'target_entity_id',
                'relationship_type', 'strength', 'confidence', 'evidence_count'
            ))
            
            # Build NetworkX graph
            G = nx.Graph()
            
            # Add nodes
            for node in nodes:
                node_id = f"{node['entity_type']}:{node['entity_id']}"
                G.add_node(node_id, **node)
            
            # Add edges
            for rel in relationships:
                source_id = f"{rel['source_entity_type']}:{rel['source_entity_id']}"
                target_id = f"{rel['target_entity_type']}:{rel['target_entity_id']}"
                
                if source_id in G.nodes and target_id in G.nodes:
                    G.add_edge(source_id, target_id, **rel)
            
            logger.info(f"Built NetworkX graph with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges")
            return G
            
        except Exception as e:
            logger.error(f"Error building NetworkX graph: {str(e)}")
            return nx.Graph()  # Return empty graph on error
    
    # ========================================
    # CENTRALITY ANALYSIS
    # ========================================
    
    def calculate_all_centrality_scores(self, G: nx.Graph = None, 
                                      update_database: bool = True) -> Dict[str, Dict[str, float]]:
        """Calculate all centrality measures for the graph"""
        if G is None:
            G = self.build_networkx_graph()
        
        if G.number_of_nodes() < self.min_nodes_for_analytics:
            logger.warning("Not enough nodes for meaningful centrality analysis")
            return {}
        
        try:
            centrality_scores = {}
            
            # PageRank (overall importance)
            logger.info("Calculating PageRank centrality...")
            pagerank = nx.pagerank(G, max_iter=1000, tol=1e-06)
            centrality_scores['pagerank'] = pagerank
            
            # Betweenness centrality (communication bridges)
            logger.info("Calculating Betweenness centrality...")
            if G.number_of_nodes() <= self.max_nodes_for_realtime:
                betweenness = nx.betweenness_centrality(G, normalized=True)
            else:
                # Use approximation for large graphs
                sample_size = min(self.max_nodes_for_realtime, G.number_of_nodes())
                betweenness = nx.betweenness_centrality(G, k=sample_size, normalized=True)
            centrality_scores['betweenness'] = betweenness
            
            # Closeness centrality (accessibility)
            logger.info("Calculating Closeness centrality...")
            try:
                if nx.is_connected(G):
                    closeness = nx.closeness_centrality(G, distance='weight')
                else:
                    # Handle disconnected graphs
                    closeness = {}
                    for component in nx.connected_components(G):
                        subgraph = G.subgraph(component)
                        component_closeness = nx.closeness_centrality(subgraph, distance='weight')
                        closeness.update(component_closeness)
            except Exception:
                closeness = nx.closeness_centrality(G)
            centrality_scores['closeness'] = closeness
            
            # Eigenvector centrality (influence)
            logger.info("Calculating Eigenvector centrality...")
            try:
                eigenvector = nx.eigenvector_centrality(G, max_iter=1000, tol=1e-06)
                centrality_scores['eigenvector'] = eigenvector
            except (nx.PowerIterationFailedConvergence, nx.NetworkXError):
                # Fallback to degree centrality if eigenvector fails
                logger.warning("Eigenvector centrality failed, using degree centrality")
                eigenvector = nx.degree_centrality(G)
                centrality_scores['eigenvector'] = eigenvector
            
            # Degree centrality (connections)
            degree = nx.degree_centrality(G)
            centrality_scores['degree'] = degree
            
            # Update database if requested
            if update_database:
                self._update_centrality_scores_in_database(centrality_scores)
            
            return centrality_scores
            
        except Exception as e:
            logger.error(f"Error calculating centrality scores: {str(e)}")
            return {}
    
    def _update_centrality_scores_in_database(self, centrality_scores: Dict[str, Dict[str, float]]):
        """Update centrality scores in the database"""
        try:
            pagerank_scores = centrality_scores.get('pagerank', {})
            betweenness_scores = centrality_scores.get('betweenness', {})
            
            for node_id, pagerank_score in pagerank_scores.items():
                try:
                    entity_type, entity_id = node_id.split(':', 1)
                    node = KnowledgeGraphNode.objects.get(
                        entity_type=entity_type,
                        entity_id=entity_id
                    )
                    
                    # Update scores
                    node.importance_score = min(1.0, pagerank_score * 10)  # Scale PageRank
                    node.centrality_score = betweenness_scores.get(node_id, 0.0)
                    node.save(update_fields=['importance_score', 'centrality_score'])
                    
                except (KnowledgeGraphNode.DoesNotExist, ValueError):
                    continue
                    
            logger.info(f"Updated centrality scores for {len(pagerank_scores)} nodes")
            
        except Exception as e:
            logger.error(f"Error updating centrality scores in database: {str(e)}")
    
    # ========================================
    # COMMUNITY DETECTION
    # ========================================
    
    def detect_communities(self, G: nx.Graph = None, algorithm: str = 'louvain') -> Dict[str, Any]:
        """Detect communities in the graph using various algorithms"""
        if G is None:
            G = self.build_networkx_graph()
        
        if G.number_of_nodes() < self.min_nodes_for_analytics:
            return {'communities': [], 'modularity': 0.0, 'algorithm': algorithm}
        
        try:
            if algorithm == 'louvain':
                return self._louvain_communities(G)
            elif algorithm == 'greedy_modularity':
                return self._greedy_modularity_communities(G)
            elif algorithm == 'connected_components':
                return self._connected_components(G)
            else:
                logger.warning(f"Unknown algorithm {algorithm}, using Louvain")
                return self._louvain_communities(G)
                
        except Exception as e:
            logger.error(f"Error detecting communities: {str(e)}")
            return {'communities': [], 'modularity': 0.0, 'algorithm': algorithm}
    
    def _louvain_communities(self, G: nx.Graph) -> Dict[str, Any]:
        """Detect communities using Louvain algorithm"""
        try:
            # Use python-louvain if available, otherwise use NetworkX
            try:
                partition = community_louvain.best_partition(G, weight='strength')
                modularity = community_louvain.modularity(partition, G, weight='strength')
            except ImportError:
                # Fallback to greedy modularity if python-louvain not available
                logger.warning("python-louvain not available, using greedy modularity")
                return self._greedy_modularity_communities(G)
            
            # Convert partition to communities list
            communities = defaultdict(list)
            for node, community_id in partition.items():
                communities[community_id].append(node)
            
            community_list = [
                {
                    'id': community_id,
                    'nodes': nodes,
                    'size': len(nodes),
                    'density': self._calculate_community_density(G, nodes)
                }
                for community_id, nodes in communities.items()
                if len(nodes) >= 2  # Only include communities with 2+ nodes
            ]
            
            # Sort by size (largest first)
            community_list.sort(key=lambda x: x['size'], reverse=True)
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'algorithm': 'louvain',
                'num_communities': len(community_list)
            }
            
        except Exception as e:
            logger.error(f"Error in Louvain community detection: {str(e)}")
            return self._greedy_modularity_communities(G)
    
    def _greedy_modularity_communities(self, G: nx.Graph) -> Dict[str, Any]:
        """Detect communities using greedy modularity optimization"""
        try:
            communities_generator = nx.community.greedy_modularity_communities(G, weight='strength')
            communities = list(communities_generator)
            
            community_list = [
                {
                    'id': i,
                    'nodes': list(community),
                    'size': len(community),
                    'density': self._calculate_community_density(G, list(community))
                }
                for i, community in enumerate(communities)
                if len(community) >= 2
            ]
            
            # Calculate modularity
            if communities:
                modularity = nx.community.modularity(G, communities, weight='strength')
            else:
                modularity = 0.0
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'algorithm': 'greedy_modularity',
                'num_communities': len(community_list)
            }
            
        except Exception as e:
            logger.error(f"Error in greedy modularity community detection: {str(e)}")
            return {'communities': [], 'modularity': 0.0, 'algorithm': 'greedy_modularity'}
    
    def _connected_components(self, G: nx.Graph) -> Dict[str, Any]:
        """Find connected components as communities"""
        try:
            components = list(nx.connected_components(G))
            
            community_list = [
                {
                    'id': i,
                    'nodes': list(component),
                    'size': len(component),
                    'density': self._calculate_community_density(G, list(component))
                }
                for i, component in enumerate(components)
                if len(component) >= 2
            ]
            
            return {
                'communities': community_list,
                'modularity': 0.0,  # Not applicable for connected components
                'algorithm': 'connected_components',
                'num_communities': len(community_list)
            }
            
        except Exception as e:
            logger.error(f"Error finding connected components: {str(e)}")
            return {'communities': [], 'modularity': 0.0, 'algorithm': 'connected_components'}
    
    def _calculate_community_density(self, G: nx.Graph, nodes: List[str]) -> float:
        """Calculate the density of a community subgraph"""
        try:
            if len(nodes) < 2:
                return 0.0
            
            subgraph = G.subgraph(nodes)
            return nx.density(subgraph)
            
        except Exception:
            return 0.0
    
    # ========================================
    # NETWORK ANALYSIS METRICS
    # ========================================
    
    def calculate_network_metrics(self, G: nx.Graph = None) -> Dict[str, Any]:
        """Calculate comprehensive network analysis metrics"""
        if G is None:
            G = self.build_networkx_graph()
        
        try:
            metrics = {
                'basic_metrics': self._calculate_basic_metrics(G),
                'connectivity_metrics': self._calculate_connectivity_metrics(G),
                'path_metrics': self._calculate_path_metrics(G),
                'clustering_metrics': self._calculate_clustering_metrics(G)
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating network metrics: {str(e)}")
            return {}
    
    def _calculate_basic_metrics(self, G: nx.Graph) -> Dict[str, Any]:
        """Calculate basic graph metrics"""
        try:
            num_nodes = G.number_of_nodes()
            num_edges = G.number_of_edges()
            
            return {
                'num_nodes': num_nodes,
                'num_edges': num_edges,
                'density': nx.density(G),
                'avg_degree': sum(dict(G.degree()).values()) / num_nodes if num_nodes > 0 else 0,
                'max_degree': max(dict(G.degree()).values()) if num_nodes > 0 else 0,
                'min_degree': min(dict(G.degree()).values()) if num_nodes > 0 else 0,
            }
        except Exception as e:
            logger.error(f"Error calculating basic metrics: {str(e)}")
            return {}
    
    def _calculate_connectivity_metrics(self, G: nx.Graph) -> Dict[str, Any]:
        """Calculate connectivity metrics"""
        try:
            is_connected = nx.is_connected(G)
            num_components = nx.number_connected_components(G)
            
            metrics = {
                'is_connected': is_connected,
                'num_connected_components': num_components,
            }
            
            if is_connected and G.number_of_nodes() > 1:
                metrics['node_connectivity'] = nx.node_connectivity(G)
                metrics['edge_connectivity'] = nx.edge_connectivity(G)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating connectivity metrics: {str(e)}")
            return {}
    
    def _calculate_path_metrics(self, G: nx.Graph) -> Dict[str, Any]:
        """Calculate path and distance metrics"""
        try:
            metrics = {}
            
            if nx.is_connected(G) and G.number_of_nodes() > 1:
                try:
                    avg_shortest_path = nx.average_shortest_path_length(G, weight='strength')
                    diameter = nx.diameter(G)
                    radius = nx.radius(G)
                    
                    metrics.update({
                        'avg_shortest_path_length': avg_shortest_path,
                        'diameter': diameter,
                        'radius': radius,
                    })
                except nx.NetworkXError:
                    # Handle cases where calculation fails
                    pass
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating path metrics: {str(e)}")
            return {}
    
    def _calculate_clustering_metrics(self, G: nx.Graph) -> Dict[str, Any]:
        """Calculate clustering metrics"""
        try:
            if G.number_of_nodes() == 0:
                return {}
            
            avg_clustering = nx.average_clustering(G, weight='strength')
            transitivity = nx.transitivity(G)
            
            return {
                'avg_clustering_coefficient': avg_clustering,
                'transitivity': transitivity,
            }
            
        except Exception as e:
            logger.error(f"Error calculating clustering metrics: {str(e)}")
            return {}
    
    # ========================================
    # ANALYSIS CACHING
    # ========================================
    
    def get_cached_analysis(self, analysis_type: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Get cached analysis results"""
        try:
            cache_key = f"kg_analytics_{analysis_type}_{hash(str(sorted(kwargs.items())))}"
            return cache.get(cache_key)
        except Exception:
            return None
    
    def cache_analysis(self, analysis_type: str, data: Dict[str, Any], **kwargs):
        """Cache analysis results"""
        try:
            cache_key = f"kg_analytics_{analysis_type}_{hash(str(sorted(kwargs.items())))}"
            cache.set(cache_key, data, self.cache_timeout)
        except Exception as e:
            logger.error(f"Error caching analysis: {str(e)}")
    
    # ========================================
    # HIGH-LEVEL ANALYSIS METHODS
    # ========================================
    
    def generate_comprehensive_analysis(self, entity_types: List[str] = None,
                                      time_window_days: int = 30,
                                      include_predictions: bool = True) -> Dict[str, Any]:
        """Generate a comprehensive analysis of the knowledge graph"""
        try:
            # Check cache first
            cache_params = {'entity_types': entity_types, 'time_window_days': time_window_days}
            cached_result = self.get_cached_analysis('comprehensive', **cache_params)
            if cached_result:
                return cached_result
            
            logger.info("Generating comprehensive knowledge graph analysis...")
            
            # Build graph
            G = self.build_networkx_graph(
                include_weak_relationships=False,
                entity_types=entity_types,
                time_window_days=time_window_days
            )
            
            if G.number_of_nodes() < self.min_nodes_for_analytics:
                return {
                    'error': 'Insufficient data for analysis',
                    'min_nodes_required': self.min_nodes_for_analytics,
                    'actual_nodes': G.number_of_nodes()
                }
            
            # Perform analyses
            analysis = {
                'graph_info': {
                    'nodes': G.number_of_nodes(),
                    'edges': G.number_of_edges(),
                    'analysis_timestamp': timezone.now().isoformat()
                },
                'centrality_analysis': self.calculate_all_centrality_scores(G, update_database=True),
                'community_analysis': self.detect_communities(G, algorithm='louvain'),
                'network_metrics': self.calculate_network_metrics(G)
            }
            
            # Add top entities by centrality
            analysis['top_entities'] = self._extract_top_entities(analysis['centrality_analysis'])
            
            # Add recommendations if requested
            if include_predictions:
                analysis['recommendations'] = self._generate_basic_recommendations(G)
            
            # Cache the results
            self.cache_analysis('comprehensive', analysis, **cache_params)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error generating comprehensive analysis: {str(e)}")
            return {'error': str(e)}
    
    def _extract_top_entities(self, centrality_analysis: Dict[str, Dict[str, float]]) -> Dict[str, List[Dict[str, Any]]]:
        """Extract top entities by different centrality measures"""
        try:
            top_entities = {}
            
            for centrality_type, scores in centrality_analysis.items():
                if not scores:
                    continue
                
                # Sort by score and take top 10
                sorted_entities = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:10]
                
                entity_list = []
                for entity_id, score in sorted_entities:
                    try:
                        entity_type, entity_id_part = entity_id.split(':', 1)
                        
                        # Get entity name from database
                        try:
                            node = KnowledgeGraphNode.objects.get(
                                entity_type=entity_type,
                                entity_id=entity_id_part
                            )
                            entity_name = node.entity_name
                        except KnowledgeGraphNode.DoesNotExist:
                            entity_name = f"{entity_type}-{entity_id_part}"
                        
                        entity_list.append({
                            'entity_id': entity_id,
                            'entity_type': entity_type,
                            'entity_name': entity_name,
                            'score': round(score, 4)
                        })
                    except ValueError:
                        continue
                
                top_entities[centrality_type] = entity_list
            
            return top_entities
            
        except Exception as e:
            logger.error(f"Error extracting top entities: {str(e)}")
            return {}
    
    def _generate_basic_recommendations(self, G: nx.Graph) -> List[Dict[str, Any]]:
        """Generate basic recommendations based on graph analysis"""
        try:
            recommendations = []
            
            # Find isolated nodes
            isolated_nodes = list(nx.isolates(G))
            if isolated_nodes:
                recommendations.append({
                    'type': 'isolated_entities',
                    'priority': 'medium',
                    'title': 'Isolated Entities Detected',
                    'description': f"Found {len(isolated_nodes)} entities with no connections",
                    'action': 'Review and link isolated entities to improve collaboration',
                    'entities': isolated_nodes[:5]  # Show first 5
                })
            
            # Find low-degree nodes that might need more connections
            degrees = dict(G.degree())
            low_degree_threshold = 2
            low_degree_nodes = [node for node, degree in degrees.items() if degree < low_degree_threshold]
            
            if len(low_degree_nodes) > len(isolated_nodes):
                recommendations.append({
                    'type': 'low_connectivity',
                    'priority': 'low',
                    'title': 'Low Connectivity Entities',
                    'description': f"Found {len(low_degree_nodes)} entities with few connections",
                    'action': 'Consider strengthening relationships for better collaboration',
                    'entities': low_degree_nodes[:5]
                })
            
            # Find potential bottlenecks (high betweenness centrality)
            try:
                betweenness = nx.betweenness_centrality(G)
                high_betweenness_threshold = 0.1
                bottleneck_nodes = [
                    node for node, score in betweenness.items() 
                    if score > high_betweenness_threshold
                ]
                
                if bottleneck_nodes:
                    recommendations.append({
                        'type': 'communication_bottlenecks',
                        'priority': 'high',
                        'title': 'Communication Bottlenecks',
                        'description': f"Found {len(bottleneck_nodes)} potential communication bottlenecks",
                        'action': 'Monitor these entities for overload and consider delegation',
                        'entities': bottleneck_nodes[:5]
                    })
            except Exception:
                pass  # Skip if betweenness calculation fails
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []