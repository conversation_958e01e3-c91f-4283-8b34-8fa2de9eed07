"""
AI Communication Intelligence Service

Provides foundational AI-powered communication features including:
- Internal email system management
- Entity mention detection and parsing
- Knowledge graph building and analysis
- Dynamic entity creation commands

This service forms the backbone of CLEAR's AI Communication Intelligence
enhancement, transforming the platform into an intelligent project coordination system.
"""


import logging
import re
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.db.models import Avg, Count, Q
from django.utils import timezone
from ..models import (
    AICommand,
    Conflict,
    Document,
    EntityMention,
    EntityRelationship,
    InternalEmail,
    InternalEmailMessage,
    KnowledgeGraphNode,
    Note,
    Project,
    Stakeholder,
    Task,
    User,
    Utility,
)
from .entity_chaining import EntityChainingService
from django.db.models import Count
from django.db.models.functions import TruncDate

logger = logging.getLogger(__name__)


class AICommunicationService:
    """Main service for AI Communication Intelligence features"""
    
    def __init__(self):
        self.entity_patterns = {
            'project': r'@project-(\w+)',
            'task': r'@task-(\w+)',
            'utility': r'@utility-(\w+)',
            'conflict': r'@conflict(?:id)?-?(\w+)',
            'stakeholder': r'@stakeholder-(\w+)',
            'user': r'@user-(\w+)',
            'document': r'@doc(?:ument)?-(\w+)',
            'note': r'@note-(\w+)',
        }
        
        self.command_patterns = {
            'add_conflict': r'@addconflict\b',
            'add_task': r'@addtask\b',
            'assign_to': r'@assignto\b',
            'set_deadline': r'@setdeadline\b',
            'create_meeting': r'@createmeeting\b',
            'update_status': r'@updatestatus\b',
            'add_note': r'@addnote\b',
            'notify_team': r'@notifyteam\b',
        }

    # ========================================
    # INTERNAL EMAIL SYSTEM
    # ========================================
    
    def create_internal_email_address(self, entity_type: str, entity_id: str, user: User) -> InternalEmail:
        """Create an internal email address for an entity"""
        try:
            # Check if address already exists
            existing = InternalEmail.objects.filter(
                entity_type=entity_type, 
                entity_id=entity_id
            ).first()
            
            if existing:
                return existing
            
            # Create new internal email address
            internal_email = InternalEmail.objects.create(
                entity_type=entity_type,
                entity_id=entity_id,
                created_by=user,
                is_active=True
            )
            
            # Set up default forwarding for project emails
            if entity_type == 'project':
                self._setup_project_email_forwarding(internal_email, entity_id)
            
            logger.info(f"Created internal email address: {internal_email.address}")
            return internal_email
            
        except Exception as e:
            logger.error(f"Error creating internal email address: {str(e)}")
            raise ValidationError(f"Could not create internal email address: {str(e)}")
    
    def _setup_project_email_forwarding(self, internal_email: InternalEmail, project_id: str):
        """Set up automatic forwarding for project emails to team members"""
        try:
            project = Project.objects.get(id=project_id)
            # Forward to project managers and coordinators
            project_members = project.members.filter(
                Q(role='manager') | Q(role='coordinator') | Q(role='admin')
            )
            internal_email.forward_to_users.set(project_members)
            
        except Project.DoesNotExist:
            logger.warning(f"Project {project_id} not found for email forwarding setup")
    
    def send_internal_email(self, from_address: str, to_addresses: List[str], 
                          subject: str, body: str, user: User, 
                          cc_addresses: List[str] = None, html_body: str = None) -> InternalEmailMessage:
        """Send an internal email message"""
        try:
            # Get or create email addresses
            from_email = InternalEmail.objects.get(address=from_address)
            to_emails = InternalEmail.objects.filter(address__in=to_addresses)
            cc_emails = InternalEmail.objects.filter(address__in=cc_addresses) if cc_addresses else []
            
            # Create email message
            message = InternalEmailMessage.objects.create(
                from_address=from_email,
                subject=subject,
                body=body,
                html_body=html_body,
                created_by=user,
                sent_at=timezone.now(),
                status='sent'
            )
            
            # Set recipients
            message.to_addresses.set(to_emails)
            if cc_emails:
                message.cc_addresses.set(cc_emails)
            
            # Process entity mentions in the email
            self.detect_and_create_entity_mentions(body, message, user)
            
            # Update analytics
            from_email.total_sent += 1
            from_email.last_activity = timezone.now()
            from_email.save()
            
            for to_email in to_emails:
                to_email.total_received += 1
                to_email.last_activity = timezone.now()
                to_email.save()
            
            logger.info(f"Sent internal email: {subject} from {from_address}")
            return message
            
        except Exception as e:
            logger.error(f"Error sending internal email: {str(e)}")
            raise ValidationError(f"Could not send internal email: {str(e)}")
    
    # ========================================
    # ENTITY MENTION DETECTION
    # ========================================
    
    def detect_and_create_entity_mentions(self, text: str, content_object: Any, user: User) -> List[EntityMention]:
        """Detect entity mentions in text and create EntityMention records"""
        mentions = []
        chained_mentions = []
        
        try:
            # First, detect entity chains using the EntityChainingService
            chaining_service = EntityChainingService()
            
            chained_mentions = chaining_service.detect_entity_chains(text, content_object, user)
            logger.info(f"Detected {len(chained_mentions)} entity chains")
            
            # Then detect simple (non-chained) entity mentions
            for entity_type, pattern in self.entity_patterns.items():
                matches = re.finditer(pattern, text, re.IGNORECASE)
                
                for match in matches:
                    entity_id = match.group(1)
                    mention_text = match.group(0)
                    start_pos = match.start()
                    end_pos = match.end()
                    
                    # Skip if this mention is already part of a chain
                    is_part_of_chain = any(
                        start_pos >= chain.base_mention.position_start and 
                        end_pos <= chain.base_mention.position_end 
                        for chain in chained_mentions
                    )
                    if is_part_of_chain:
                        continue
                    
                    # Extract context
                    context_start = max(0, start_pos - 50)
                    context_end = min(len(text), end_pos + 50)
                    context_before = text[context_start:start_pos].strip()
                    context_after = text[end_pos:context_end].strip()
                    
                    # Validate entity exists
                    if self._validate_entity_exists(entity_type, entity_id):
                        # Create or get existing mention
                        mention, created = EntityMention.objects.get_or_create(
                            content_type=ContentType.objects.get_for_model(content_object),
                            object_id=str(content_object.id),
                            mention_text=mention_text,
                            position_start=start_pos,
                            defaults={
                                'mentioned_entity_type': entity_type,
                                'mentioned_entity_id': entity_id,
                                'position_end': end_pos,
                                'context_before': context_before,
                                'context_after': context_after,
                                'detected_by': user,
                                'confidence': 0.95,  # High confidence for pattern-based detection
                            }
                        )
                        
                        if created:
                            mentions.append(mention)
                            logger.debug(f"Created entity mention: {mention_text}")
            
            # Combine regular mentions with chained mentions for relationship processing
            all_base_mentions = mentions + [chain.base_mention for chain in chained_mentions]
            
            # Process multi-entity relationships if multiple mentions found
            if len(all_base_mentions) > 1:
                self._process_entity_relationships(all_base_mentions, user)
            
            # Update knowledge graph
            self._update_knowledge_graph(all_base_mentions, user)
            
            return mentions
            
        except Exception as e:
            logger.error(f"Error detecting entity mentions: {str(e)}")
            return []
    
    def _validate_entity_exists(self, entity_type: str, entity_id: str) -> bool:
        """Validate that the mentioned entity actually exists"""
        model_mapping = {
            'project': Project,
            'task': Task,
            'utility': Utility,
            'conflict': Conflict,
            'stakeholder': Stakeholder,
            'user': User,
            'document': Document,
            'note': Note,
        }
        
        model = model_mapping.get(entity_type)
        if not model:
            return False
        
        try:
            model.objects.get(id=entity_id)
            return True
        except (model.DoesNotExist, ValueError):
            return False
    
    def _process_entity_relationships(self, mentions: List[EntityMention], user: User):
        """Process relationships between entities mentioned together"""
        try:
            for i, mention1 in enumerate(mentions):
                for mention2 in mentions[i+1:]:
                    # Create or update relationship
                    relationship, created = EntityRelationship.objects.get_or_create(
                        source_entity_type=mention1.mentioned_entity_type,
                        source_entity_id=mention1.mentioned_entity_id,
                        target_entity_type=mention2.mentioned_entity_type,
                        target_entity_id=mention2.mentioned_entity_id,
                        relationship_type='mentioned_together',
                        defaults={
                            'strength': 0.5,
                            'confidence': 0.8,
                            'evidence_count': 1,
                            'discovered_through': 'entity_mentions',
                            'discovered_by': user,
                        }
                    )
                    
                    if not created:
                        # Strengthen existing relationship
                        relationship.evidence_count += 1
                        relationship.strength = min(1.0, relationship.strength + 0.1)
                        relationship.last_confirmed_at = timezone.now()
                        relationship.save()
                    
                    # Add mentions as supporting evidence
                    relationship.supporting_communications.add(mention1, mention2)
                    
        except Exception as e:
            logger.error(f"Error processing entity relationships: {str(e)}")
    
    # ========================================
    # KNOWLEDGE GRAPH MANAGEMENT
    # ========================================
    
    def _update_knowledge_graph(self, mentions: List[EntityMention], user: User):
        """Update knowledge graph with entity mentions"""
        try:
            for mention in mentions:
                # Get or create knowledge graph node
                node, created = KnowledgeGraphNode.objects.get_or_create(
                    entity_type=mention.mentioned_entity_type,
                    entity_id=mention.mentioned_entity_id,
                    defaults={
                        'entity_name': self._get_entity_name(mention.mentioned_entity_type, mention.mentioned_entity_id),
                        'first_mentioned': mention.detected_at,
                        'importance_score': 0.1,
                        'centrality_score': 0.0,
                        'activity_level': 1.0,
                    }
                )
                
                # Update node statistics
                node.total_mentions += 1
                node.last_activity = mention.detected_at
                node.activity_level = min(1.0, node.activity_level + 0.05)
                node.importance_score = min(1.0, node.importance_score + (0.01 * node.total_mentions))
                
                # Extract keywords from context
                keywords = self._extract_keywords(mention.context_before, mention.context_after)
                for keyword in keywords:
                    if keyword not in node.keywords:
                        node.keywords.append(keyword)
                
                node.save()
                
        except Exception as e:
            logger.error(f"Error updating knowledge graph: {str(e)}")
    
    def _get_entity_name(self, entity_type: str, entity_id: str) -> str:
        """Get the display name for an entity"""
        model_mapping = {
            'project': Project,
            'task': Task,
            'utility': Utility,
            'conflict': Conflict,
            'stakeholder': Stakeholder,
            'user': User,
            'document': Document,
            'note': Note,
        }
        
        model = model_mapping.get(entity_type)
        if not model:
            return f"{entity_type}-{entity_id}"
        
        try:
            obj = model.objects.get(id=entity_id)
            if hasattr(obj, 'name'):
                return obj.name
            elif hasattr(obj, 'title'):
                return obj.title
            elif hasattr(obj, 'username'):
                return obj.username
            else:
                return str(obj)
        except model.DoesNotExist:
            return f"{entity_type}-{entity_id}"
    
    def _extract_keywords(self, context_before: str, context_after: str) -> List[str]:
        """Extract keywords from mention context"""
        # Simple keyword extraction - can be enhanced with NLP
        text = f"{context_before} {context_after}".lower()
        
        # Remove common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        keywords = [word for word in words if word not in stop_words]
        
        return list(set(keywords))[:10]  # Return up to 10 unique keywords
    
    # ========================================
    # AI COMMAND PROCESSING
    # ========================================
    
    def detect_and_process_ai_commands(self, text: str, content_object: Any, user: User) -> List[AICommand]:
        """Detect AI commands in text and create processing records"""
        commands = []
        
        try:
            for command_type, pattern in self.command_patterns.items():
                matches = re.finditer(pattern, text, re.IGNORECASE)
                
                for match in matches:
                    command_text = self._extract_command_context(text, match.start(), match.end())
                    
                    # Parse command parameters
                    parameters = self._parse_command_parameters(command_type, command_text)
                    
                    # Create AI command record
                    command = AICommand.objects.create(
                        command_text=command_text,
                        command_type=command_type,
                        parsed_parameters=parameters,
                        source_content_type=ContentType.objects.get_for_model(content_object),
                        source_object_id=str(content_object.id),
                        issued_by=user,
                        status='requires_confirmation' if parameters.get('requires_confirmation', True) else 'pending'
                    )
                    
                    commands.append(command)
                    logger.info(f"Detected AI command: {command_type} by {user.username}")
            
            return commands
            
        except Exception as e:
            logger.error(f"Error detecting AI commands: {str(e)}")
            return []
    
    def _extract_command_context(self, text: str, start: int, end: int) -> str:
        """Extract the full command context from text"""
        # Find the sentence or paragraph containing the command
        context_start = max(0, start - 100)
        context_end = min(len(text), end + 200)
        
        # Look for sentence boundaries
        before_text = text[context_start:start]
        after_text = text[end:context_end]
        
        # Find sentence start
        sentence_start = context_start
        for i, char in enumerate(reversed(before_text)):
            if char in '.!?':
                sentence_start = start - i
                break
        
        # Find sentence end
        sentence_end = context_end
        for i, char in enumerate(after_text):
            if char in '.!?':
                sentence_end = end + i + 1
                break
        
        return text[sentence_start:sentence_end].strip()
    
    def _parse_command_parameters(self, command_type: str, command_text: str) -> Dict[str, Any]:
        """Parse parameters from AI command text"""
        parameters = {}
        
        # Extract entity mentions from command
        entity_mentions = []
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, command_text, re.IGNORECASE)
            for match in matches:
                entity_mentions.append({
                    'type': entity_type,
                    'id': match,
                    'text': f"@{entity_type}-{match}"
                })
        
        parameters['entity_mentions'] = entity_mentions
        
        # Command-specific parameter extraction
        if command_type == 'add_task':
            # Extract title and description
            title_match = re.search(r'(?:title|task)[:\s]+"([^"]+)"', command_text, re.IGNORECASE)
            parameters['title'] = title_match.group(1) if title_match else "New Task"
            
            desc_match = re.search(r'(?:description|desc)[:\s]+"([^"]+)"', command_text, re.IGNORECASE)
            parameters['description'] = desc_match.group(1) if desc_match else ""
            
        elif command_type == 'add_conflict':
            # Extract conflict details
            type_match = re.search(r'(?:type|kind)[:\s]+"([^"]+)"', command_text, re.IGNORECASE)
            parameters['conflict_type'] = type_match.group(1) if type_match else "unknown"
            
        elif command_type == 'set_deadline':
            # Extract date information
            date_match = re.search(r'(?:by|date|deadline)[:\s]+(\d{4}-\d{2}-\d{2}|\d{1,2}/\d{1,2}/\d{4})', command_text, re.IGNORECASE)
            parameters['deadline'] = date_match.group(1) if date_match else None
            
        elif command_type == 'assign_to':
            # Extract assignee information
            user_match = re.search(r'@user-(\w+)', command_text, re.IGNORECASE)
            parameters['assignee'] = user_match.group(1) if user_match else None
        
        return parameters
    
    # ========================================
    # ANALYTICS AND INSIGHTS
    # ========================================
    
    def generate_communication_insights(self, project_id: str = None, user_id: str = None, 
                                      days: int = 30) -> Dict[str, Any]:
        """Generate AI-powered communication insights"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Base queryset for mentions
            mentions_qs = EntityMention.objects.filter(detected_at__range=[start_date, end_date])
            
            if project_id:
                # Filter to project-related mentions
                mentions_qs = mentions_qs.filter(
                    Q(mentioned_entity_type='project', mentioned_entity_id=project_id) |
                    Q(content_type__model='project', object_id=project_id)
                )
            
            if user_id:
                mentions_qs = mentions_qs.filter(detected_by_id=user_id)
            
            # Calculate insights
            insights = {
                'period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat(),
                    'days': days
                },
                'total_mentions': mentions_qs.count(),
                'unique_entities': mentions_qs.values('mentioned_entity_type', 'mentioned_entity_id').distinct().count(),
                'most_mentioned_entities': self._get_top_mentioned_entities(mentions_qs),
                'entity_types_distribution': self._get_entity_types_distribution(mentions_qs),
                'communication_trends': self._get_communication_trends(mentions_qs, days),
                'discovered_relationships': self._get_relationship_insights(start_date, end_date),
                'ai_commands_summary': self._get_ai_commands_summary(start_date, end_date),
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating communication insights: {str(e)}")
            return {}
    
    def _get_top_mentioned_entities(self, mentions_qs) -> List[Dict[str, Any]]:
        """Get top mentioned entities with counts"""
        top_entities = mentions_qs.values(
            'mentioned_entity_type', 'mentioned_entity_id'
        ).annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        return [
            {
                'entity_type': entity['mentioned_entity_type'],
                'entity_id': entity['mentioned_entity_id'],
                'count': entity['count'],
                'entity_name': self._get_entity_name(entity['mentioned_entity_type'], entity['mentioned_entity_id'])
            }
            for entity in top_entities
        ]
    
    def _get_entity_types_distribution(self, mentions_qs) -> Dict[str, int]:
        """Get distribution of entity types in mentions"""
        distribution = mentions_qs.values('mentioned_entity_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        return {item['mentioned_entity_type']: item['count'] for item in distribution}
    
    def _get_communication_trends(self, mentions_qs, days: int) -> List[Dict[str, Any]]:
        """Get daily communication trends"""
        
        trends = mentions_qs.annotate(
            date=TruncDate('detected_at')
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')
        
        return [
            {
                'date': item['date'].isoformat(),
                'mentions_count': item['count']
            }
            for item in trends
        ]
    
    def _get_relationship_insights(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get relationship discovery insights"""
        relationships = EntityRelationship.objects.filter(
            discovered_at__range=[start_date, end_date]
        )
        
        return {
            'total_discovered': relationships.count(),
            'by_type': dict(relationships.values('relationship_type').annotate(count=Count('id')).values_list('relationship_type', 'count')),
            'average_strength': relationships.aggregate(avg_strength=Avg('strength'))['avg_strength'] or 0.0,
        }
    
    def _get_ai_commands_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get AI commands summary"""
        commands = AICommand.objects.filter(issued_at__range=[start_date, end_date])
        
        return {
            'total_commands': commands.count(),
            'by_type': dict(commands.values('command_type').annotate(count=Count('id')).values_list('command_type', 'count')),
            'by_status': dict(commands.values('status').annotate(count=Count('id')).values_list('status', 'count')),
            'completion_rate': commands.filter(status='completed').count() / max(1, commands.count()),
        }