"""
Entity Chaining Service for Phase 2 AI Communication Intelligence

Provides advanced entity chaining capabilities including:
- Hierarchical entity chain detection (@project-123/phase-2/task-456)
- Chain validation and relationship verification
- Context inheritance management
- Cascading entity suggestion logic

This service extends the basic entity mention system to support complex
entity relationships and multi-level entity references.
"""


import logging
import re
from typing import Any, Dict, List, Optional
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from ..models import (
    ChainedEntityContext,
    ChainedEntityMention,
    Conflict,
    Document,
    EntityHierarchy,
    EntityMention,
    EntityRelationship,
    KnowledgeGraphNode,
    Note,
    Project,
    Stakeholder,
    Task,
    User,
    Utility,
)

logger = logging.getLogger(__name__)


class EntityChainingService:
    """Service for managing hierarchical entity chains and relationships"""
    
    def __init__(self):
        # Enhanced patterns for chained entity detection
        self.chained_entity_patterns = {
            'full_chain': r'@([a-zA-Z0-9_-]+(?:/[a-zA-Z0-9_-]+)*)',
            'single_entity': r'@([a-zA-Z0-9_-]+)',
        }
        
        # Valid entity type hierarchy mappings
        self.hierarchy_rules = {
            'project': ['task', 'conflict', 'stakeholder', 'document', 'note'],
            'task': ['conflict', 'document', 'note'],
            'stakeholder': ['project', 'task', 'document'],
            'utility': ['conflict', 'document'],
            'conflict': ['document', 'note'],
            'user': ['task', 'project', 'document', 'note'],
            'document': ['note'],
            'note': [],
        }
        
        # Chain depth limits to prevent infinite chains
        self.max_chain_depth = 5
    
    # ========================================
    # ENTITY CHAIN DETECTION
    # ========================================
    
    def detect_entity_chains(self, text: str, content_object: Any, user: User) -> List[ChainedEntityMention]:
        """Detect and parse entity chains in text"""
        chains = []
        
        try:
            # Find all potential chain mentions
            chain_matches = re.finditer(self.chained_entity_patterns['full_chain'], text, re.IGNORECASE)
            
            for match in chain_matches:
                chain_text = match.group(1)
                mention_text = match.group(0)
                start_pos = match.start()
                end_pos = match.end()
                
                # Skip simple single entities (handled by basic entity detection)
                if '/' not in chain_text:
                    continue
                
                # Parse the chain components
                components = chain_text.split('/')
                if len(components) > self.max_chain_depth:
                    logger.warning(f"Chain too deep: {chain_text} (max depth: {self.max_chain_depth})")
                    continue
                
                # Validate and create chain
                chain = self._create_entity_chain(
                    full_chain_text=mention_text,
                    components=components,
                    content_object=content_object,
                    user=user,
                    start_pos=start_pos,
                    end_pos=end_pos,
                    original_text=text
                )
                
                if chain:
                    chains.append(chain)
            
            # Update knowledge graph with chain relationships
            if chains:
                self._update_knowledge_graph_with_chains(chains, user)
            
            return chains
            
        except Exception as e:
            logger.error(f"Error detecting entity chains: {str(e)}")
            return []
    
    def _create_entity_chain(self, full_chain_text: str, components: List[str], 
                           content_object: Any, user: User, start_pos: int, 
                           end_pos: int, original_text: str) -> Optional[ChainedEntityMention]:
        """Create a validated entity chain from components"""
        try:
            # Parse each component to extract entity type and ID
            parsed_components = []
            for component in components:
                entity_info = self._parse_entity_component(component)
                if not entity_info:
                    logger.warning(f"Invalid entity component: {component}")
                    return None
                parsed_components.append(entity_info)
            
            # Validate the chain hierarchy
            validation_result = self._validate_chain_hierarchy(parsed_components)
            if not validation_result['is_valid']:
                logger.warning(f"Invalid chain hierarchy: {full_chain_text} - {validation_result['errors']}")
                # Still create the chain but mark as invalid
            
            # Create base EntityMention for the leaf entity
            leaf_component = parsed_components[-1]
            base_mention = self._create_or_get_base_mention(
                leaf_component, content_object, user, start_pos, end_pos, original_text
            )
            
            # Create the chained entity mention
            chained_mention = ChainedEntityMention.objects.create(
                base_mention=base_mention,
                full_chain_text=full_chain_text,
                chain_position=len(parsed_components) - 1,  # Leaf position
                chain_depth=len(parsed_components),
                is_chain_root=len(parsed_components) == 1,
                is_chain_leaf=True,
                is_valid_chain=validation_result['is_valid'],
                validation_errors=validation_result['errors']
            )
            
            # Create or update entity contexts for each component
            self._create_chain_contexts(parsed_components, full_chain_text, user)
            
            # Create hierarchy relationships if they don't exist
            if validation_result['is_valid']:
                self._ensure_hierarchy_relationships(parsed_components, user)
            
            logger.info(f"Created entity chain: {full_chain_text}")
            return chained_mention
            
        except Exception as e:
            logger.error(f"Error creating entity chain {full_chain_text}: {str(e)}")
            return None
    
    def _parse_entity_component(self, component: str) -> Optional[Dict[str, str]]:
        """Parse a single entity component (e.g., 'project-123' or 'task-456')"""
        # Handle different formats: entity-id, entitytype-id, etc.
        if '-' not in component:
            return None
        
        # Split on the last dash to handle entity types with dashes
        parts = component.rsplit('-', 1)
        if len(parts) != 2:
            return None
        
        entity_type, entity_id = parts
        
        # Validate entity type
        valid_types = ['project', 'task', 'utility', 'conflict', 'stakeholder', 'user', 'document', 'note']
        if entity_type not in valid_types:
            return None
        
        # Validate entity ID format (alphanumeric, allows UUIDs)
        if not re.match(r'^[a-zA-Z0-9_-]+$', entity_id):
            return None
        
        return {
            'type': entity_type,
            'id': entity_id,
            'component': component
        }
    
    def _validate_chain_hierarchy(self, components: List[Dict[str, str]]) -> Dict[str, Any]:
        """Validate that the entity chain follows valid hierarchy rules"""
        errors = []
        is_valid = True
        
        # Check each parent-child relationship in the chain
        for i in range(len(components) - 1):
            parent = components[i]
            child = components[i + 1]
            
            # Check if this parent-child relationship is allowed
            allowed_children = self.hierarchy_rules.get(parent['type'], [])
            if child['type'] not in allowed_children:
                errors.append(f"Invalid hierarchy: {parent['type']} cannot contain {child['type']}")
                is_valid = False
            
            # Verify entities actually exist
            if not self._entity_exists(parent['type'], parent['id']):
                errors.append(f"Parent entity does not exist: {parent['type']}-{parent['id']}")
                is_valid = False
            
            if not self._entity_exists(child['type'], child['id']):
                errors.append(f"Child entity does not exist: {child['type']}-{child['id']}")
                is_valid = False
        
        return {
            'is_valid': is_valid,
            'errors': errors
        }
    
    def _entity_exists(self, entity_type: str, entity_id: str) -> bool:
        """Check if an entity actually exists in the database"""
        model_mapping = {
            'project': Project,
            'task': Task,
            'utility': Utility,
            'conflict': Conflict,
            'stakeholder': Stakeholder,
            'user': User,
            'document': Document,
            'note': Note,
        }
        
        model = model_mapping.get(entity_type)
        if not model:
            return False
        
        try:
            model.objects.get(id=entity_id)
            return True
        except (model.DoesNotExist, ValueError):
            return False
    
    # ========================================
    # CHAIN CONTEXT MANAGEMENT
    # ========================================
    
    def _create_chain_contexts(self, components: List[Dict[str, str]], 
                             full_chain_path: str, user: User):
        """Create or update context objects for each entity in the chain"""
        try:
            for position, component in enumerate(components):
                # Create partial chain path for this position
                partial_path = '/'.join([c['component'] for c in components[:position + 1]])
                
                # Get or create context
                context, created = ChainedEntityContext.objects.get_or_create(
                    entity_type=component['type'],
                    entity_id=component['id'],
                    full_chain_path=partial_path,
                    defaults={
                        'chain_position': position,
                        'mention_frequency': 0,
                        'last_chain_mention': timezone.now(),
                    }
                )
                
                # Update mention statistics
                context.mention_frequency += 1
                context.last_chain_mention = timezone.now()
                
                # Update inherited context from parents
                if position > 0:
                    context.update_inherited_context()
                
                context.save()
                
        except Exception as e:
            logger.error(f"Error creating chain contexts: {str(e)}")
    
    def _ensure_hierarchy_relationships(self, components: List[Dict[str, str]], user: User):
        """Ensure EntityHierarchy records exist for valid chain relationships"""
        try:
            for i in range(len(components) - 1):
                parent = components[i]
                child = components[i + 1]
                
                # Create hierarchy path up to this point
                hierarchy_path = '/'.join([c['component'] for c in components[:i + 2]])
                
                # Create or get hierarchy relationship
                hierarchy, created = EntityHierarchy.objects.get_or_create(
                    parent_entity_type=parent['type'],
                    parent_entity_id=parent['id'],
                    child_entity_type=child['type'],
                    child_entity_id=child['id'],
                    defaults={
                        'hierarchy_level': i + 1,
                        'relationship_type': 'contains',
                        'hierarchy_path': hierarchy_path,
                        'is_active': True,
                        'created_by': user,
                    }
                )
                
                if not created:
                    # Update existing relationship
                    hierarchy.hierarchy_path = hierarchy_path
                    hierarchy.is_active = True
                    hierarchy.save()
                
        except Exception as e:
            logger.error(f"Error ensuring hierarchy relationships: {str(e)}")
    
    # ========================================
    # CASCADING SUGGESTIONS
    # ========================================
    
    def get_cascading_suggestions(self, partial_chain: str, context_entity: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get context-aware entity suggestions for building chains"""
        try:
            
            # Parse partial chain
            if not partial_chain:
                # Return top-level entity suggestions
                return self._get_root_entity_suggestions()
            
            # Handle partial chain with incomplete last component
            if partial_chain.endswith('/'):
                # User wants to add to the chain
                parent_chain = partial_chain.rstrip('/')
                return self._get_child_entity_suggestions(parent_chain)
            
            # Handle partial completion of current entity
            chain_parts = partial_chain.split('/')
            if len(chain_parts) == 1:
                # Completing root entity
                return self._get_entity_completions(chain_parts[0])
            else:
                # Completing child entity
                parent_chain = '/'.join(chain_parts[:-1])
                partial_entity = chain_parts[-1]
                return self._get_child_entity_completions(parent_chain, partial_entity)
            
        except Exception as e:
            logger.error(f"Error getting cascading suggestions: {str(e)}")
            return []
    
    def _get_root_entity_suggestions(self) -> List[Dict[str, Any]]:
        """Get suggestions for root-level entities"""
        suggestions = []
        
        # Get most frequently mentioned entities
        top_entities = KnowledgeGraphNode.objects.filter(
            importance_score__gt=0.1
        ).order_by('-importance_score', '-total_mentions')[:20]
        
        for entity in top_entities:
            suggestions.append({
                'type': entity.entity_type,
                'id': entity.entity_id,
                'name': entity.entity_name,
                'mention_text': f"@{entity.entity_type}-{entity.entity_id}",
                'description': f"Importance: {entity.importance_score:.2f}, Mentions: {entity.total_mentions}",
                'can_have_children': len(self.hierarchy_rules.get(entity.entity_type, [])) > 0
            })
        
        return suggestions
    
    def _get_child_entity_suggestions(self, parent_chain: str) -> List[Dict[str, Any]]:
        """Get suggestions for child entities given a parent chain"""
        suggestions = []
        
        try:
            # Parse parent chain to get the immediate parent
            parent_components = parent_chain.split('/')
            if not parent_components:
                return []
            
            parent_component = parent_components[-1]
            parent_info = self._parse_entity_component(parent_component)
            if not parent_info:
                return []
            
            # Get allowed child types
            allowed_child_types = self.hierarchy_rules.get(parent_info['type'], [])
            if not allowed_child_types:
                return []
            
            # Find existing hierarchy relationships
            existing_children = EntityHierarchy.objects.filter(
                parent_entity_type=parent_info['type'],
                parent_entity_id=parent_info['id'],
                is_active=True
            ).values_list('child_entity_type', 'child_entity_id', 'child_entity_type__name')
            
            # Add existing children first
            for child_type, child_id, child_name in existing_children:
                suggestions.append({
                    'type': child_type,
                    'id': child_id,
                    'name': child_name or f"{child_type}-{child_id}",
                    'mention_text': f"@{child_type}-{child_id}",
                    'description': f"Existing child of {parent_info['type']}-{parent_info['id']}",
                    'relationship_exists': True,
                    'can_have_children': len(self.hierarchy_rules.get(child_type, [])) > 0
                })
            
            # Add suggestions for potential new children
            for child_type in allowed_child_types:
                # Get most relevant entities of this type
                potential_children = self._get_potential_children(parent_info, child_type)
                suggestions.extend(potential_children)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting child entity suggestions: {str(e)}")
            return []
    
    def _get_potential_children(self, parent_info: Dict[str, str], child_type: str) -> List[Dict[str, Any]]:
        """Get potential child entities that could be linked to parent"""
        suggestions = []
        
        try:
            model_mapping = {
                'project': Project,
                'task': Task,
                'utility': Utility,
                'conflict': Conflict,
                'stakeholder': Stakeholder,
                'user': User,
                'document': Document,
                'note': Note,
            }
            
            child_model = model_mapping.get(child_type)
            if not child_model:
                return []
            
            # Get entities that might be related to the parent
            candidates = child_model.objects.all()
            
            # Filter based on parent relationship if possible
            if hasattr(child_model, f'{parent_info["type"]}_id'):
                candidates = candidates.filter(**{f'{parent_info["type"]}_id': parent_info['id']})
            elif hasattr(child_model, f'{parent_info["type"]}'):
                candidates = candidates.filter(**{f'{parent_info["type"]}__id': parent_info['id']})
            
            # Limit results
            candidates = candidates[:10]
            
            for candidate in candidates:
                name = getattr(candidate, 'name', None) or getattr(candidate, 'title', None) or str(candidate)
                suggestions.append({
                    'type': child_type,
                    'id': str(candidate.id),
                    'name': name,
                    'mention_text': f"@{child_type}-{candidate.id}",
                    'description': f"Potential child of {parent_info['type']}-{parent_info['id']}",
                    'relationship_exists': False,
                    'can_have_children': len(self.hierarchy_rules.get(child_type, [])) > 0
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting potential children: {str(e)}")
            return []
    
    # ========================================
    # KNOWLEDGE GRAPH UPDATES
    # ========================================
    
    def _update_knowledge_graph_with_chains(self, chains: List[ChainedEntityMention], user: User):
        """Update knowledge graph with entity chain relationships"""
        try:
            for chain in chains:
                components = chain.get_chain_components()
                
                # Create relationships between each adjacent pair
                for i in range(len(components) - 1):
                    parent_component = components[i]
                    child_component = components[i + 1]
                    
                    # Parse components
                    if '-' not in parent_component or '-' not in child_component:
                        continue
                    
                    parent_type, parent_id = parent_component.rsplit('-', 1)
                    child_type, child_id = child_component.rsplit('-', 1)
                    
                    # Create or update entity relationship
                    relationship, created = EntityRelationship.objects.get_or_create(
                        source_entity_type=parent_type,
                        source_entity_id=parent_id,
                        target_entity_type=child_type,
                        target_entity_id=child_id,
                        relationship_type='contains',
                        defaults={
                            'strength': 0.8,
                            'confidence': 0.9,
                            'evidence_count': 1,
                            'discovered_through': 'entity_mentions',
                            'discovered_by': user,
                        }
                    )
                    
                    if not created:
                        # Strengthen existing relationship
                        relationship.evidence_count += 1
                        relationship.strength = min(1.0, relationship.strength + 0.1)
                        relationship.last_confirmed_at = timezone.now()
                        relationship.save()
                
        except Exception as e:
            logger.error(f"Error updating knowledge graph with chains: {str(e)}")
    
    def _create_or_get_base_mention(self, entity_info: Dict[str, str], content_object: Any, 
                                  user: User, start_pos: int, end_pos: int, 
                                  original_text: str) -> EntityMention:
        """Create or get base EntityMention for the leaf entity"""
        try:
            # Extract context around the mention
            context_start = max(0, start_pos - 50)
            context_end = min(len(original_text), end_pos + 50)
            context_before = original_text[context_start:start_pos].strip()
            context_after = original_text[end_pos:context_end].strip()
            
            mention, created = EntityMention.objects.get_or_create(
                content_type=ContentType.objects.get_for_model(content_object),
                object_id=str(content_object.id),
                mentioned_entity_type=entity_info['type'],
                mentioned_entity_id=entity_info['id'],
                mention_text=f"@{entity_info['component']}",
                position_start=start_pos,
                defaults={
                    'position_end': end_pos,
                    'context_before': context_before,
                    'context_after': context_after,
                    'detected_by': user,
                    'confidence': 0.95,
                }
            )
            
            return mention
            
        except Exception as e:
            logger.error(f"Error creating base mention: {str(e)}")
            raise