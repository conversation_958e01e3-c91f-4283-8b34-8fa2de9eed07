"""
Message pagination service for efficient large message history handling.

Provides cursor-based pagination for messages with optimized queries and
thread handling for high-performance message loading.
"""
"""



from typing import Dict, List, Optional
from django.core.cache import cache
from django.db.models import Prefetch, Q
from django.utils import timezone
from ..models import ChatMessage, Conversation
import re
from ..models import MessageRead

"""




class MessagePaginationService:
    """Service for handling efficient message pagination and threading"""
    
    DEFAULT_PAGE_SIZE = 25
    MAX_PAGE_SIZE = 100
    CACHE_TIMEOUT = 300  # 5 minutes
    
    def __init__(self):
        self.page_size = self.DEFAULT_PAGE_SIZE
    
    def get_conversation_messages(
        self, 
        conversation_id: str, 
        user_id: int,
        cursor: Optional[str] = None,
        page_size: Optional[int] = None,
        thread_id: Optional[int] = None
    ) -> Dict:
        """
        Get paginated messages for a conversation with cursor-based pagination.
        
        Args:
            conversation_id: UUID of the conversation
            user_id: ID of the requesting user
            cursor: Cursor for pagination (message ID or timestamp)
            page_size: Number of messages per page
            thread_id: Optional message ID for thread-specific pagination
            
        Returns:
            Dict containing messages, pagination info, and metadata
        """
        page_size = min(page_size or self.DEFAULT_PAGE_SIZE, self.MAX_PAGE_SIZE)
        
        # Build cache key for this request
        cache_key = self._build_cache_key(
            'conversation', conversation_id, user_id, cursor, page_size, thread_id
        )
        
        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            conversation = Conversation.objects.get(id=conversation_id)
            
            # Verify user has access to conversation
            if not conversation.participants.filter(id=user_id).exists():
                raise PermissionError("User does not have access to this conversation")
            
            # Base query with optimizations
            query = self._build_base_query(conversation, thread_id)
            
            # Apply cursor pagination
            if cursor:
                query = self._apply_cursor_filter(query, cursor)
            
            # Get messages with pagination
            messages = list(query[:page_size + 1])  # +1 to check for next page
            
            has_more = len(messages) > page_size
            if has_more:
                messages = messages[:page_size]
            
            # Generate next cursor if there are more messages
            next_cursor = None
            if has_more and messages:
                next_cursor = self._generate_cursor(messages[-1])
            
            # Mark messages as read for user
            self._mark_messages_read(messages, user_id)
            
            # Process messages for display
            processed_messages = self._process_messages_for_display(messages, user_id)
            
            # Build result
            result = {
                'messages': processed_messages,
                'pagination': {
                    'has_more': has_more,
                    'next_cursor': next_cursor,
                    'page_size': page_size,
                    'total_count': self._get_total_count(conversation, thread_id)
                },
                'conversation': {
                    'id': str(conversation.id),
                    'name': str(conversation),
                    'type': conversation.conversation_type,
                    'participant_count': conversation.participants.count()
                },
                'thread_info': self._get_thread_info(thread_id) if thread_id else None
            }
            
            # Cache the result
            cache.set(cache_key, result, self.CACHE_TIMEOUT)
            
            return result
            
        except Exception as e:
            # Log error and return empty result
            return {
                'messages': [],
                'pagination': {
                    'has_more': False,
                    'next_cursor': None,
                    'page_size': page_size,
                    'total_count': 0
                },
                'error': str(e)
            }
    
    def get_channel_messages(
        self,
        channel: str,
        user_id: int,
        project_id: Optional[int] = None,
        cursor: Optional[str] = None,
        page_size: Optional[int] = None
    ) -> Dict:
        """
        Get paginated messages for a channel/legacy system.
        
        Args:
            channel: Channel name
            user_id: ID of the requesting user
            project_id: Optional project ID for project channels
            cursor: Cursor for pagination
            page_size: Number of messages per page
            
        Returns:
            Dict containing messages and pagination info
        """
        page_size = min(page_size or self.DEFAULT_PAGE_SIZE, self.MAX_PAGE_SIZE)
        
        cache_key = self._build_cache_key(
            'channel', channel, user_id, cursor, page_size, project_id
        )
        
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Build query for channel messages
        query = ChatMessage.objects.filter(
            channel=channel,
            conversation__isnull=True  # Legacy messages without conversation
        )
        
        if project_id:
            query = query.filter(project_id=project_id)
        
        # Apply optimizations
        query = query.select_related('user', 'project', 'reply_to__user').prefetch_related(
            'attachments',
            'read_by',
            Prefetch('replies', queryset=ChatMessage.objects.select_related('user'))
        ).order_by('-created_at')
        
        # Apply cursor pagination
        if cursor:
            query = self._apply_cursor_filter(query, cursor)
        
        # Get messages
        messages = list(query[:page_size + 1])
        
        has_more = len(messages) > page_size
        if has_more:
            messages = messages[:page_size]
        
        next_cursor = None
        if has_more and messages:
            next_cursor = self._generate_cursor(messages[-1])
        
        # Process messages
        processed_messages = self._process_messages_for_display(messages, user_id)
        
        result = {
            'messages': processed_messages,
            'pagination': {
                'has_more': has_more,
                'next_cursor': next_cursor,
                'page_size': page_size,
                'total_count': query.count()
            },
            'channel': {
                'name': channel,
                'project_id': project_id
            }
        }
        
        cache.set(cache_key, result, self.CACHE_TIMEOUT)
        return result
    
    def get_thread_messages(
        self,
        parent_message_id: int,
        user_id: int,
        cursor: Optional[str] = None,
        page_size: Optional[int] = None
    ) -> Dict:
        """
        Get paginated messages for a thread (replies to a parent message).
        
        Args:
            parent_message_id: ID of the parent message
            user_id: ID of the requesting user
            cursor: Cursor for pagination
            page_size: Number of messages per page
            
        Returns:
            Dict containing thread messages and pagination info
        """
        page_size = min(page_size or self.DEFAULT_PAGE_SIZE, self.MAX_PAGE_SIZE)
        
        cache_key = self._build_cache_key(
            'thread', parent_message_id, user_id, cursor, page_size
        )
        
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        try:
            parent_message = ChatMessage.objects.get(id=parent_message_id)
            
            # Build query for thread messages
            query = ChatMessage.objects.filter(
                reply_to=parent_message
            ).select_related('user', 'project', 'conversation').prefetch_related(
                'attachments',
                'read_by'
            ).order_by('created_at')  # Threads show oldest first
            
            # Apply cursor pagination
            if cursor:
                query = self._apply_cursor_filter(query, cursor, reverse=True)
            
            # Get messages
            messages = list(query[:page_size + 1])
            
            has_more = len(messages) > page_size
            if has_more:
                messages = messages[:page_size]
            
            next_cursor = None
            if has_more and messages:
                next_cursor = self._generate_cursor(messages[-1])
            
            # Mark messages as read
            self._mark_messages_read(messages, user_id)
            
            # Process messages
            processed_messages = self._process_messages_for_display(messages, user_id)
            
            result = {
                'messages': processed_messages,
                'pagination': {
                    'has_more': has_more,
                    'next_cursor': next_cursor,
                    'page_size': page_size,
                    'total_count': parent_message.replies.count()
                },
                'thread': {
                    'parent_message_id': parent_message_id,
                    'parent_content': parent_message.content[:100] + "..." if len(parent_message.content) > 100 else parent_message.content,
                    'parent_user': parent_message.user.get_full_name() or parent_message.user.username
                }
            }
            
            cache.set(cache_key, result, self.CACHE_TIMEOUT)
            return result
            
        except ChatMessage.DoesNotExist:
            return {
                'messages': [],
                'pagination': {
                    'has_more': False,
                    'next_cursor': None,
                    'page_size': page_size,
                    'total_count': 0
                },
                'error': 'Parent message not found'
            }
    
    def search_messages(
        self,
        query: str,
        user_id: int,
        conversation_id: Optional[str] = None,
        project_id: Optional[int] = None,
        cursor: Optional[str] = None,
        page_size: Optional[int] = None
    ) -> Dict:
        """
        Search messages with pagination.
        
        Args:
            query: Search query string
            user_id: ID of the requesting user
            conversation_id: Optional conversation to search within
            project_id: Optional project to search within
            cursor: Cursor for pagination
            page_size: Number of results per page
            
        Returns:
            Dict containing search results and pagination info
        """
        page_size = min(page_size or self.DEFAULT_PAGE_SIZE, self.MAX_PAGE_SIZE)
        
        if not query.strip():
            return {
                'messages': [],
                'pagination': {
                    'has_more': False,
                    'next_cursor': None,
                    'page_size': page_size,
                    'total_count': 0
                }
            }
        
        # Build search query
        search_filter = Q(content__icontains=query)
        
        message_query = ChatMessage.objects.filter(search_filter)
        
        # Apply filters
        if conversation_id:
            message_query = message_query.filter(conversation_id=conversation_id)
        
        if project_id:
            message_query = message_query.filter(project_id=project_id)
        
        # Only show messages user has access to
        user_conversations = Conversation.objects.filter(participants__id=user_id).values_list('id', flat=True)
        message_query = message_query.filter(
            Q(conversation_id__in=user_conversations) |
            Q(conversation__isnull=True)  # Include legacy messages
        )
        
        # Apply optimizations
        message_query = message_query.select_related(
            'user', 'project', 'conversation', 'reply_to__user'
        ).prefetch_related('attachments').order_by('-created_at')
        
        # Apply cursor pagination
        if cursor:
            message_query = self._apply_cursor_filter(message_query, cursor)
        
        # Get messages
        messages = list(message_query[:page_size + 1])
        
        has_more = len(messages) > page_size
        if has_more:
            messages = messages[:page_size]
        
        next_cursor = None
        if has_more and messages:
            next_cursor = self._generate_cursor(messages[-1])
        
        # Process messages
        processed_messages = self._process_messages_for_display(messages, user_id, highlight_query=query)
        
        return {
            'messages': processed_messages,
            'pagination': {
                'has_more': has_more,
                'next_cursor': next_cursor,
                'page_size': page_size,
                'total_count': min(message_query.count(), 1000)  # Cap count for performance
            },
            'search': {
                'query': query,
                'conversation_id': conversation_id,
                'project_id': project_id
            }
        }
    
    def _build_base_query(self, conversation: Conversation, thread_id: Optional[int] = None):
        """Build optimized base query for conversation messages"""
        if thread_id:
            # Get messages in a specific thread
            return ChatMessage.objects.filter(
                reply_to_id=thread_id
            ).select_related('user', 'project', 'reply_to__user').prefetch_related(
                'attachments',
                'read_by'
            ).order_by('created_at')  # Threads show oldest first
        else:
            # Get main conversation messages (not replies)
            return ChatMessage.objects.filter(
                conversation=conversation,
                reply_to__isnull=True  # Only top-level messages
            ).select_related('user', 'project').prefetch_related(
                'attachments',
                'read_by',
                Prefetch(
                    'replies',
                    queryset=ChatMessage.objects.select_related('user').order_by('created_at')[:3]
                )
            ).order_by('-created_at')
    
    def _apply_cursor_filter(self, query, cursor: str, reverse: bool = False):
        """Apply cursor-based pagination filter"""
        try:
            # Cursor format: "timestamp_id" or just "id"
            if '_' in cursor:
                timestamp, message_id = cursor.split('_', 1)
                cursor_time = timezone.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                
                if reverse:
                    # For threads (oldest first)
                    query = query.filter(
                        Q(created_at__gt=cursor_time) |
                        (Q(created_at=cursor_time) & Q(id__gt=int(message_id)))
                    )
                else:
                    # For regular messages (newest first)
                    query = query.filter(
                        Q(created_at__lt=cursor_time) |
                        (Q(created_at=cursor_time) & Q(id__lt=int(message_id)))
                    )
            else:
                # Simple ID-based cursor
                message_id = int(cursor)
                if reverse:
                    query = query.filter(id__gt=message_id)
                else:
                    query = query.filter(id__lt=message_id)
                    
        except (ValueError, TypeError):
            # Invalid cursor, ignore
            pass
        
        return query
    
    def _generate_cursor(self, message) -> str:
        """Generate cursor for pagination"""
        # Include timestamp for better ordering
        timestamp = message.created_at.isoformat()
        return f"{timestamp}_{message.id}"
    
    def _mark_messages_read(self, messages: List[ChatMessage], user_id: int):
        """Mark messages as read by user"""
        for message in messages:
            if not message.is_read_by_user_id(user_id):
                message.mark_read_by_user_id(user_id)
    
    def _process_messages_for_display(
        self, 
        messages: List[ChatMessage], 
        user_id: int,
        highlight_query: Optional[str] = None
    ) -> List[Dict]:
        """Process messages for frontend display"""
        processed = []
        
        for message in messages:
            message_data = {
                'id': message.id,
                'content': message.content,
                'user': {
                    'id': message.user.id,
                    'username': message.user.username,
                    'full_name': message.user.get_full_name(),
                    'initials': message.user.get_initials(),
                    'avatar_url': getattr(message.user, 'avatar_url', None)
                },
                'created_at': message.created_at,
                'updated_at': message.updated_at,
                'message_type': message.message_type,
                'is_urgent': message.is_urgent,
                'is_read': message.is_read_by_user_id(user_id),
                'read_count': message.get_read_count(),
                'reply_count': message.get_thread_count(),
                'attachments': [
                    {
                        'id': att.id,
                        'name': att.name,
                        'file_type': att.file_type,
                        'file_size': att.file_size,
                        'url': att.file.url if att.file else None
                    }
                    for att in message.attachments.all()
                ],
                'can_edit': message.user_id == user_id,
                'can_delete': message.user_id == user_id
            }
            
            # Add reply information
            if message.reply_to:
                message_data['reply_to'] = {
                    'id': message.reply_to.id,
                    'content': message.reply_to.content[:100] + "..." if len(message.reply_to.content) > 100 else message.reply_to.content,
                    'user': message.reply_to.user.get_full_name() or message.reply_to.user.username
                }
            
            # Add recent replies for top-level messages
            if hasattr(message, 'replies'):
                message_data['recent_replies'] = [
                    {
                        'id': reply.id,
                        'content': reply.content[:50] + "..." if len(reply.content) > 50 else reply.content,
                        'user': reply.user.get_initials(),
                        'created_at': reply.created_at
                    }
                    for reply in message.replies.all()
                ]
            
            # Highlight search terms if provided
            if highlight_query:
                message_data['content'] = self._highlight_search_terms(
                    message_data['content'], highlight_query
                )
            
            processed.append(message_data)
        
        return processed
    
    def _highlight_search_terms(self, content: str, query: str) -> str:
        """Highlight search terms in message content"""
        query_escaped = re.escape(query)
        pattern = re.compile(f'({query_escaped})', re.IGNORECASE)
        return pattern.sub(r'<mark>\1</mark>', content)
    
    def _get_total_count(self, conversation: Conversation, thread_id: Optional[int] = None) -> int:
        """Get total message count with caching"""
        cache_key = f"message_count_{conversation.id}_{thread_id or 'main'}"
        count = cache.get(cache_key)
        
        if count is None:
            if thread_id:
                count = ChatMessage.objects.filter(reply_to_id=thread_id).count()
            else:
                count = ChatMessage.objects.filter(
                    conversation=conversation,
                    reply_to__isnull=True
                ).count()
            cache.set(cache_key, count, 300)  # Cache for 5 minutes
        
        return count
    
    def _get_thread_info(self, thread_id: int) -> Optional[Dict]:
        """Get thread information"""
        try:
            parent_message = ChatMessage.objects.get(id=thread_id)
            return {
                'parent_id': parent_message.id,
                'parent_content': parent_message.content[:100] + "..." if len(parent_message.content) > 100 else parent_message.content,
                'parent_user': parent_message.user.get_full_name() or parent_message.user.username,
                'reply_count': parent_message.get_thread_count()
            }
        except ChatMessage.DoesNotExist:
            return None
    
    def _build_cache_key(self, *args) -> str:
        """Build cache key from arguments"""
        key_parts = ['msg_pagination'] + [str(arg) for arg in args if arg is not None]
        return '_'.join(key_parts)
    
    def invalidate_conversation_cache(self, conversation_id: str):
        """Invalidate cache for a conversation when new messages are added"""
        # This is a simplified version - in production you might want more sophisticated cache invalidation
        # Note: Django's default cache doesn't support pattern deletion
        # You might want to use Redis directly or implement a custom cache backend
        pass


# Helper methods for ChatMessage model
def add_message_methods():
    """Add performance-optimized methods to ChatMessage model"""
    
    def is_read_by_user_id(self, user_id: int) -> bool:
        """Optimized version that uses user_id directly"""
        return self.read_by.filter(id=user_id).exists()
    
    def mark_read_by_user_id(self, user_id: int):
        """Optimized version that uses user_id directly"""
        read_record, created = MessageRead.objects.get_or_create(
            message=self,
            user_id=user_id,
            defaults={'read_at': timezone.now()}
        )
        return read_record
    
    # Add methods to ChatMessage model
    ChatMessage.is_read_by_user_id = is_read_by_user_id
    ChatMessage.mark_read_by_user_id = mark_read_by_user_id


# Initialize the service
message_pagination_service = MessagePaginationService()