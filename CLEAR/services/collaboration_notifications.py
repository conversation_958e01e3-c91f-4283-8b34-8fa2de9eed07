"""
Collaboration notification service for handling @mentions and reactions.
Integrates with the existing notification system.
"""


import logging
from django.contrib.auth import get_user_model
from django.utils import timezone
from ..models import (
    CollaborationSettings,
    MessageMention,
    MessageReaction,
    Notification,
)

logger = logging.getLogger(__name__)
User = get_user_model()


class CollaborationNotificationService:
    """Service for handling collaboration-related notifications"""
    
    def notify_mentions(self, message):
        """Create notifications for all users mentioned in a message"""
        try:
            mentions = MessageMention.objects.filter(
                message=message,
                is_notified=False
            ).select_related('mentioned_user', 'message__user')
            
            for mention in mentions:
                self._create_mention_notification(mention)
                mention.is_notified = True
                mention.save(update_fields=['is_notified'])
                
            logger.info(f"Created notifications for {mentions.count()} mentions in message {message.id}")
            
        except Exception as e:
            logger.error(f"Error creating mention notifications for message {message.id}: {e}")
    
    def notify_reaction(self, reaction):
        """Create notification for message reaction"""
        try:
            # Don't notify if user reacted to their own message
            if reaction.user == reaction.message.user:
                return
            
            # Check if user wants reaction notifications
            try:
                settings = reaction.message.user.collaboration_settings
                if not settings.reaction_notifications:
                    return
            except CollaborationSettings.DoesNotExist:
                # Default to sending notifications if settings don't exist
                pass
            
            self._create_reaction_notification(reaction)
            logger.info(f"Created reaction notification for message {reaction.message.id}")
            
        except Exception as e:
            logger.error(f"Error creating reaction notification: {e}")
    
    def notify_thread_reply(self, reply_message):
        """Notify participants when someone replies to a thread"""
        try:
            if not reply_message.reply_to:
                return
            
            # Get thread participants
            root_message = reply_message.reply_to
            if hasattr(root_message, 'thread_root'):
                thread = root_message.thread_root
                participants = thread.get_participants().exclude(id=reply_message.user.id)
                
                for participant in participants:
                    self._create_thread_reply_notification(reply_message, participant)
                
                logger.info(f"Created thread reply notifications for {participants.count()} participants")
                
        except Exception as e:
            logger.error(f"Error creating thread reply notifications: {e}")
    
    def _create_mention_notification(self, mention):
        """Create a notification for a mention"""
        # Check user's mention notification preferences
        try:
            settings = mention.mentioned_user.collaboration_settings
            if not settings.mention_notifications:
                return
        except CollaborationSettings.DoesNotExist:
            # Default to sending notifications if settings don't exist
            pass
        
        # Create the notification
        notification = Notification.objects.create(
            user=mention.mentioned_user,
            type='message_mention',
            title=f'{mention.message.user.first_name} mentioned you',
            message=f'{mention.message.user.first_name} {mention.message.user.last_name} mentioned you in a message: "{mention.message.content[:100]}..."',
            data={
                'message_id': mention.message.id,
                'mention_id': mention.id,
                'conversation_id': mention.message.get_conversation_id(),
                'mention_text': mention.mention_text,
            },
            action_url=self._get_message_url(mention.message),
            action_label='View Message',
            priority='high'  # Mentions are high priority
        )
        
        return notification
    
    def _create_reaction_notification(self, reaction):
        """Create a notification for a reaction"""
        # Count how many reactions this message has
        reaction_count = MessageReaction.objects.filter(message=reaction.message).count()
        
        # Create the notification
        notification = Notification.objects.create(
            user=reaction.message.user,
            type='message_reaction',
            title='New reaction on your message',
            message=f'{reaction.user.first_name} {reaction.user.last_name} reacted {reaction.emoji} to your message',
            data={
                'message_id': reaction.message.id,
                'reaction_id': reaction.id,
                'emoji': reaction.emoji,
                'reaction_count': reaction_count,
                'conversation_id': reaction.message.get_conversation_id(),
            },
            action_url=self._get_message_url(reaction.message),
            action_label='View Message',
            priority='normal'
        )
        
        return notification
    
    def _create_thread_reply_notification(self, reply_message, participant):
        """Create a notification for a thread reply"""
        notification = Notification.objects.create(
            user=participant,
            type='thread_reply',
            title='New reply in thread',
            message=f'{reply_message.user.first_name} {reply_message.user.last_name} replied to a thread you\'re following',
            data={
                'message_id': reply_message.id,
                'parent_message_id': reply_message.reply_to.id,
                'conversation_id': reply_message.get_conversation_id(),
            },
            action_url=self._get_message_url(reply_message),
            action_label='View Thread',
            priority='normal'
        )
        
        return notification
    
    def _get_message_url(self, message):
        """Get the URL to view a specific message"""
        if message.conversation:
            return f'/messages/?conversation={message.conversation.id}&message={message.id}'
        elif message.project:
            return f'/projects/{message.project.id}/chat/?message={message.id}'
        else:
            return f'/messages/?channel={message.channel}&message={message.id}'
    
    def get_unread_mention_count(self, user):
        """Get count of unread mentions for a user"""
        try:
            return MessageMention.objects.filter(
                mentioned_user=user,
                is_notified=True
            ).select_related('message').filter(
                message__created_at__gt=user.last_seen_at or timezone.now() - timezone.timedelta(days=30)
            ).count()
        except Exception as e:
            logger.error(f"Error getting unread mention count for user {user.id}: {e}")
            return 0
    
    def mark_mentions_read(self, user, conversation_id=None, message_id=None):
        """Mark mentions as read for a user"""
        try:
            mentions = MessageMention.objects.filter(
                mentioned_user=user,
                is_notified=True
            ).select_related('message')
            
            if conversation_id:
                mentions = mentions.filter(message__conversation_id=conversation_id)
            
            if message_id:
                mentions = mentions.filter(message_id=message_id)
            
            count = mentions.update(is_notified=False)
            logger.info(f"Marked {count} mentions as read for user {user.id}")
            
            return count
            
        except Exception as e:
            logger.error(f"Error marking mentions as read for user {user.id}: {e}")
            return 0


# Global service instance
collaboration_notification_service = CollaborationNotificationService()