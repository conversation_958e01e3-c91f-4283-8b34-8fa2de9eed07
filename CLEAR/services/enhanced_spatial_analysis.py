"""
Enhanced Spatial Analysis Service for CLEAR

Provides advanced spatial analysis capabilities for utility coordination,
including real-time buffer analysis, clearance zone calculations, and
sophisticated conflict detection with risk assessment.
"""
"""



import json
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional
from django.contrib.gis.db.models.functions import Distance as DistanceFunction
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import Distance
from ..models import Project, Utility
from django.contrib.gis.geos import GEOSGeometry

"""




logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk levels for spatial conflicts"""
    CRITICAL = "critical"
    HIGH = "high" 
    MEDIUM = "medium"
    LOW = "low"
    MINIMAL = "minimal"


class UtilityType(Enum):
    """Utility types with clearance requirements"""
    ELECTRIC = "electric"
    GAS = "gas"
    WATER = "water"
    SEWER = "sewer"
    TELECOM = "telecom"
    CABLE = "cable"


@dataclass
class ClearanceRequirement:
    """Clearance requirements for different utility types"""
    horizontal_clearance: float  # feet
    vertical_clearance: float    # feet
    buffer_zone: float          # feet
    critical_distance: float    # feet for critical conflicts


@dataclass
class SpatialAnalysisResult:
    """Result of spatial analysis operations"""
    success: bool
    message: str
    data: Dict[str, Any]
    risk_level: RiskLevel
    confidence: float
    recommendations: List[str]


class EnhancedSpatialAnalysis:
    """Enhanced spatial analysis service for utility coordination"""
    
    # Clearance requirements by utility type (in feet)
    CLEARANCE_REQUIREMENTS = {
        UtilityType.ELECTRIC: ClearanceRequirement(
            horizontal_clearance=3.0,
            vertical_clearance=4.0, 
            buffer_zone=6.0,
            critical_distance=1.5
        ),
        UtilityType.GAS: ClearanceRequirement(
            horizontal_clearance=5.0,
            vertical_clearance=3.0,
            buffer_zone=8.0,
            critical_distance=2.0
        ),
        UtilityType.WATER: ClearanceRequirement(
            horizontal_clearance=2.0,
            vertical_clearance=2.0,
            buffer_zone=4.0,
            critical_distance=1.0
        ),
        UtilityType.SEWER: ClearanceRequirement(
            horizontal_clearance=3.0,
            vertical_clearance=2.5,
            buffer_zone=5.0,
            critical_distance=1.5
        ),
        UtilityType.TELECOM: ClearanceRequirement(
            horizontal_clearance=1.5,
            vertical_clearance=1.5,
            buffer_zone=3.0,
            critical_distance=0.75
        ),
        UtilityType.CABLE: ClearanceRequirement(
            horizontal_clearance=1.0,
            vertical_clearance=1.0,
            buffer_zone=2.0,
            critical_distance=0.5
        )
    }
    
    def __init__(self, project: Project):
        """Initialize spatial analysis for a specific project"""
        self.project = project
        self.coordinate_system = project.coordinate_systems.filter(is_primary=True).first()
        self.cache_timeout = 300  # 5 minutes
        
    def create_buffer_zones(self, utility_type: str, distance_feet: float = None) -> SpatialAnalysisResult:
        """
        Create buffer zones around utilities of a specific type
        
        Args:
            utility_type: Type of utility (electric, gas, water, etc.)
            distance_feet: Custom buffer distance, uses default if None
            
        Returns:
            SpatialAnalysisResult with buffer zone geometries
        """
        try:
            # Get utilities for this project and type
            utilities = Utility.objects.filter(
                project=self.project,
                utility_type=utility_type,
                geometry__isnull=False
            )
            
            if not utilities.exists():
                return SpatialAnalysisResult(
                    success=False,
                    message=f"No {utility_type} utilities found for this project",
                    data={},
                    risk_level=RiskLevel.MINIMAL,
                    confidence=0.0,
                    recommendations=[]
                )
            
            # Determine buffer distance
            if distance_feet is None:
                try:
                    utility_enum = UtilityType(utility_type)
                    distance_feet = self.CLEARANCE_REQUIREMENTS[utility_enum].buffer_zone
                except (ValueError, KeyError):
                    distance_feet = 5.0  # Default fallback
            
            # Convert feet to meters for PostGIS operations
            distance_meters = distance_feet * 0.3048
            
            buffer_zones = []
            for utility in utilities:
                # Create buffer zone
                buffer_geom = utility.geometry.buffer(distance_meters)
                buffer_zones.append({
                    'utility_id': utility.id,
                    'utility_name': utility.name,
                    'utility_type': utility_type,
                    'buffer_distance_feet': distance_feet,
                    'buffer_geometry': buffer_geom.geojson,
                    'original_geometry': utility.geometry.geojson,
                    'properties': {
                        'owner': utility.owner,
                        'description': utility.description,
                        'depth': getattr(utility, 'depth', None),
                        'diameter': getattr(utility, 'diameter', None),
                        'material': getattr(utility, 'material', None)
                    }
                })
            
            return SpatialAnalysisResult(
                success=True,
                message=f"Created {len(buffer_zones)} buffer zones for {utility_type} utilities",
                data={
                    'buffer_zones': buffer_zones,
                    'total_count': len(buffer_zones),
                    'buffer_distance_feet': distance_feet,
                    'utility_type': utility_type
                },
                risk_level=RiskLevel.LOW,
                confidence=0.95,
                recommendations=[
                    f"Review {utility_type} clearance zones for potential conflicts",
                    f"Verify {distance_feet}ft clearance meets local regulations"
                ]
            )
            
        except Exception as e:
            logger.error(f"Error creating buffer zones: {str(e)}")
            return SpatialAnalysisResult(
                success=False,
                message=f"Failed to create buffer zones: {str(e)}",
                data={},
                risk_level=RiskLevel.HIGH,
                confidence=0.0,
                recommendations=["Contact system administrator"]
            )
    
    def analyze_clearance_violations(self, tolerance_feet: float = 0.5) -> SpatialAnalysisResult:
        """
        Analyze clearance violations between different utility types
        
        Args:
            tolerance_feet: Additional tolerance for clearance calculations
            
        Returns:
            SpatialAnalysisResult with clearance violation analysis
        """
        try:
            violations = []
            risk_scores = []
            
            # Get all utilities for this project
            utilities = Utility.objects.filter(
                project=self.project,
                geometry__isnull=False
            ).select_related()
            
            if utilities.count() < 2:
                return SpatialAnalysisResult(
                    success=True,
                    message="Insufficient utilities for clearance analysis",
                    data={'violations': []},
                    risk_level=RiskLevel.MINIMAL,
                    confidence=1.0,
                    recommendations=["Add more utility data for comprehensive analysis"]
                )
            
            # Compare each utility with every other utility
            utility_list = list(utilities)
            for i, utility1 in enumerate(utility_list):
                for utility2 in utility_list[i+1:]:
                    violation = self._check_clearance_violation(
                        utility1, utility2, tolerance_feet
                    )
                    if violation:
                        violations.append(violation)
                        risk_scores.append(violation['risk_score'])
            
            # Calculate overall risk assessment
            if violations:
                avg_risk = sum(risk_scores) / len(risk_scores)
                max_risk = max(risk_scores)
                sum(1 for score in risk_scores if score >= 8)
                
                if max_risk >= 9:
                    risk_level = RiskLevel.CRITICAL
                elif max_risk >= 7:
                    risk_level = RiskLevel.HIGH
                elif avg_risk >= 5:
                    risk_level = RiskLevel.MEDIUM
                else:
                    risk_level = RiskLevel.LOW
                    
                confidence = min(0.9, 0.6 + (len(violations) * 0.05))
            else:
                risk_level = RiskLevel.MINIMAL
                confidence = 0.85
            
            # Generate recommendations
            recommendations = self._generate_clearance_recommendations(violations)
            
            return SpatialAnalysisResult(
                success=True,
                message=f"Found {len(violations)} clearance violations",
                data={
                    'violations': violations,
                    'total_violations': len(violations),
                    'critical_violations': sum(1 for v in violations if v['risk_score'] >= 8),
                    'average_risk_score': sum(risk_scores) / len(risk_scores) if risk_scores else 0,
                    'max_risk_score': max(risk_scores) if risk_scores else 0,
                    'utilities_analyzed': len(utility_list)
                },
                risk_level=risk_level,
                confidence=confidence,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error analyzing clearance violations: {str(e)}")
            return SpatialAnalysisResult(
                success=False,
                message=f"Failed to analyze clearance violations: {str(e)}",
                data={},
                risk_level=RiskLevel.HIGH,
                confidence=0.0,
                recommendations=["Contact system administrator"]
            )
    
    def _check_clearance_violation(self, utility1: Utility, utility2: Utility, tolerance: float) -> Optional[Dict]:
        """Check if two utilities violate clearance requirements"""
        try:
            # Calculate distance between utilities
            distance = utility1.geometry.distance(utility2.geometry)
            distance_feet = distance * 3.28084  # Convert to feet
            
            # Get clearance requirements for both utilities
            req1 = self._get_clearance_requirement(utility1.utility_type)
            req2 = self._get_clearance_requirement(utility2.utility_type)
            
            # Determine minimum required clearance
            required_clearance = max(req1.horizontal_clearance, req2.horizontal_clearance)
            required_clearance += tolerance
            
            # Check for violation
            if distance_feet < required_clearance:
                # Calculate risk score (1-10 scale)
                clearance_ratio = distance_feet / required_clearance
                risk_score = min(10, max(1, 10 - (clearance_ratio * 8)))
                
                # Determine violation severity
                if distance_feet < max(req1.critical_distance, req2.critical_distance):
                    severity = "critical"
                elif distance_feet < required_clearance * 0.5:
                    severity = "high"
                elif distance_feet < required_clearance * 0.75:
                    severity = "medium"
                else:
                    severity = "low"
                
                return {
                    'utility1_id': utility1.id,
                    'utility1_type': utility1.utility_type,
                    'utility1_name': utility1.name,
                    'utility2_id': utility2.id,
                    'utility2_type': utility2.utility_type,
                    'utility2_name': utility2.name,
                    'actual_distance_feet': round(distance_feet, 2),
                    'required_clearance_feet': round(required_clearance, 2),
                    'clearance_deficit_feet': round(required_clearance - distance_feet, 2),
                    'risk_score': round(risk_score, 1),
                    'severity': severity,
                    'violation_type': 'horizontal_clearance',
                    'geometry_intersection': utility1.geometry.intersection(utility2.geometry).geojson if utility1.geometry.intersects(utility2.geometry) else None
                }
                
            return None
            
        except Exception as e:
            logger.error(f"Error checking clearance violation: {str(e)}")
            return None
    
    def _get_clearance_requirement(self, utility_type: str) -> ClearanceRequirement:
        """Get clearance requirement for a utility type"""
        try:
            utility_enum = UtilityType(utility_type)
            return self.CLEARANCE_REQUIREMENTS[utility_enum]
        except (ValueError, KeyError):
            # Default clearance for unknown types
            return ClearanceRequirement(
                horizontal_clearance=3.0,
                vertical_clearance=3.0,
                buffer_zone=5.0,
                critical_distance=1.5
            )
    
    def _generate_clearance_recommendations(self, violations: List[Dict]) -> List[str]:
        """Generate recommendations based on clearance violations"""
        if not violations:
            return ["No clearance violations detected", "Continue regular monitoring"]
        
        recommendations = []
        critical_count = sum(1 for v in violations if v['severity'] == 'critical')
        high_count = sum(1 for v in violations if v['severity'] == 'high')
        
        if critical_count > 0:
            recommendations.append(f"URGENT: {critical_count} critical clearance violations require immediate attention")
            recommendations.append("Stop construction activities near critical violation areas")
            recommendations.append("Contact utility owners for emergency coordination")
        
        if high_count > 0:
            recommendations.append(f"{high_count} high-risk violations need resolution before construction")
            recommendations.append("Request utility relocations or protection measures")
        
        # Type-specific recommendations
        utility_types = set()
        for violation in violations:
            utility_types.add(violation['utility1_type'])
            utility_types.add(violation['utility2_type'])
        
        if 'gas' in utility_types:
            recommendations.append("Extra caution required around gas utilities - contact 811")
        if 'electric' in utility_types:
            recommendations.append("Ensure electrical safety protocols for high-voltage lines")
        
        recommendations.append("Update project plans to reflect clearance requirements")
        recommendations.append("Schedule utility coordination meeting with all affected owners")
        
        return recommendations
    
    def perform_spatial_query(self, query_type: str, parameters: Dict) -> SpatialAnalysisResult:
        """
        Perform advanced spatial queries on utility data
        
        Args:
            query_type: Type of spatial query ('within_distance', 'intersects', 'contains', etc.)
            parameters: Query parameters including geometry, distance, filters
            
        Returns:
            SpatialAnalysisResult with query results
        """
        try:
            utilities = Utility.objects.filter(project=self.project, geometry__isnull=False)
            
            if query_type == 'within_distance':
                return self._query_within_distance(utilities, parameters)
            elif query_type == 'intersects':
                return self._query_intersects(utilities, parameters)
            elif query_type == 'contains':
                return self._query_contains(utilities, parameters)
            elif query_type == 'nearest':
                return self._query_nearest(utilities, parameters)
            else:
                return SpatialAnalysisResult(
                    success=False,
                    message=f"Unknown query type: {query_type}",
                    data={},
                    risk_level=RiskLevel.LOW,
                    confidence=0.0,
                    recommendations=["Use supported query types"]
                )
                
        except Exception as e:
            logger.error(f"Error performing spatial query: {str(e)}")
            return SpatialAnalysisResult(
                success=False,
                message=f"Failed to perform spatial query: {str(e)}",
                data={},
                risk_level=RiskLevel.HIGH,
                confidence=0.0,
                recommendations=["Contact system administrator"]
            )
    
    def _query_within_distance(self, utilities, parameters) -> SpatialAnalysisResult:
        """Query utilities within distance of a point or geometry"""
        point_coords = parameters.get('point')
        distance_feet = parameters.get('distance', 100)
        utility_types = parameters.get('types', [])
        
        if not point_coords:
            return SpatialAnalysisResult(
                success=False,
                message="Point coordinates required for distance query",
                data={},
                risk_level=RiskLevel.LOW,
                confidence=0.0,
                recommendations=["Provide point coordinates"]
            )
        
        # Create point geometry
        point = Point(point_coords[0], point_coords[1], srid=4326)
        
        # Convert distance to meters
        distance_meters = distance_feet * 0.3048
        
        # Build query
        query = utilities.filter(
            geometry__distance_lte=(point, Distance(m=distance_meters))
        )
        
        if utility_types:
            query = query.filter(utility_type__in=utility_types)
        
        # Execute query with distance annotation
        results = query.annotate(
            distance_meters=DistanceFunction('geometry', point)
        ).order_by('distance_meters')
        
        # Format results
        utilities_data = []
        for utility in results:
            distance_feet_calc = utility.distance_meters.m * 3.28084
            utilities_data.append({
                'id': utility.id,
                'name': utility.name,
                'type': utility.utility_type,
                'distance_feet': round(distance_feet_calc, 2),
                'owner': utility.owner,
                'description': utility.description,
                'geometry': utility.geometry.geojson
            })
        
        return SpatialAnalysisResult(
            success=True,
            message=f"Found {len(utilities_data)} utilities within {distance_feet} feet",
            data={
                'utilities': utilities_data,
                'query_point': point_coords,
                'search_distance_feet': distance_feet,
                'total_found': len(utilities_data)
            },
            risk_level=RiskLevel.LOW if len(utilities_data) < 5 else RiskLevel.MEDIUM,
            confidence=0.9,
            recommendations=[
                f"Review {len(utilities_data)} utilities in search area",
                "Verify clearance requirements for nearby utilities"
            ]
        )
    
    def _query_intersects(self, utilities, parameters) -> SpatialAnalysisResult:
        """Query utilities that intersect with a geometry"""
        geometry_data = parameters.get('geometry')
        utility_types = parameters.get('types', [])
        
        if not geometry_data:
            return SpatialAnalysisResult(
                success=False,
                message="Geometry required for intersection query",
                data={},
                risk_level=RiskLevel.LOW,
                confidence=0.0,
                recommendations=["Provide search geometry"]
            )
        
        # Parse geometry (assuming GeoJSON format)
        try:
            search_geom = GEOSGeometry(json.dumps(geometry_data))
        except Exception as e:
            return SpatialAnalysisResult(
                success=False,
                message=f"Invalid geometry: {str(e)}",
                data={},
                risk_level=RiskLevel.LOW,
                confidence=0.0,
                recommendations=["Provide valid GeoJSON geometry"]
            )
        
        # Build query
        query = utilities.filter(geometry__intersects=search_geom)
        
        if utility_types:
            query = query.filter(utility_type__in=utility_types)
        
        results = list(query)
        
        # Format results
        utilities_data = []
        for utility in results:
            intersection = utility.geometry.intersection(search_geom)
            utilities_data.append({
                'id': utility.id,
                'name': utility.name,
                'type': utility.utility_type,
                'owner': utility.owner,
                'description': utility.description,
                'geometry': utility.geometry.geojson,
                'intersection_geometry': intersection.geojson if intersection else None
            })
        
        return SpatialAnalysisResult(
            success=True,
            message=f"Found {len(utilities_data)} intersecting utilities",
            data={
                'utilities': utilities_data,
                'search_geometry': geometry_data,
                'total_found': len(utilities_data)
            },
            risk_level=RiskLevel.MEDIUM if len(utilities_data) > 0 else RiskLevel.LOW,
            confidence=0.95,
            recommendations=[
                f"Review {len(utilities_data)} intersecting utilities",
                "Plan construction to avoid utility conflicts"
            ]
        )
    
    def _query_nearest(self, utilities, parameters) -> SpatialAnalysisResult:
        """Find nearest utilities to a point"""
        point_coords = parameters.get('point')
        limit = parameters.get('limit', 5)
        utility_types = parameters.get('types', [])
        
        if not point_coords:
            return SpatialAnalysisResult(
                success=False,
                message="Point coordinates required for nearest query",
                data={},
                risk_level=RiskLevel.LOW,
                confidence=0.0,
                recommendations=["Provide point coordinates"]
            )
        
        # Create point geometry
        point = Point(point_coords[0], point_coords[1], srid=4326)
        
        # Build query
        query = utilities
        if utility_types:
            query = query.filter(utility_type__in=utility_types)
        
        # Get nearest utilities
        results = query.annotate(
            distance_meters=DistanceFunction('geometry', point)
        ).order_by('distance_meters')[:limit]
        
        # Format results
        utilities_data = []
        for utility in results:
            distance_feet = utility.distance_meters.m * 3.28084
            utilities_data.append({
                'id': utility.id,
                'name': utility.name,
                'type': utility.utility_type,
                'distance_feet': round(distance_feet, 2),
                'owner': utility.owner,
                'description': utility.description,
                'geometry': utility.geometry.geojson
            })
        
        return SpatialAnalysisResult(
            success=True,
            message=f"Found {len(utilities_data)} nearest utilities",
            data={
                'utilities': utilities_data,
                'query_point': point_coords,
                'limit': limit,
                'total_found': len(utilities_data)
            },
            risk_level=RiskLevel.LOW,
            confidence=0.9,
            recommendations=[
                "Review nearest utilities for potential conflicts",
                "Consider clearance requirements for closest utilities"
            ]
        )


def get_spatial_analysis_service(project: Project) -> EnhancedSpatialAnalysis:
    """Factory function to get spatial analysis service for a project"""
    return EnhancedSpatialAnalysis(project)