"""
Advanced Stakeholder Analytics Service for CLEAR Platform
Provides comprehensive stakeholder engagement metrics, communication statistics, and reporting
"""
"""



from datetime import datetime, timedelta
from typing import Any, Dict, List
from django.db import connection
from django.db.models import Avg, <PERSON>, Count, DecimalField, Q, Value, When
from django.db.models.functions import TruncDay
from django.utils import timezone
from ..models import Comment, Organization, Stakeholder

"""





class StakeholderAnalyticsEngine:
    """
    Advanced analytics engine for stakeholder engagement and communication metrics
    """
    
    def __init__(self, organization: Organization = None):
        self.organization = organization
    
    def get_comprehensive_stakeholder_analytics(self, days: int = 30) -> Dict[str, Any]:
        """
        Generate comprehensive stakeholder analytics with advanced metrics
        """
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Get all analytics components
        engagement_metrics = self._get_engagement_metrics(start_date, end_date)
        communication_metrics = self._get_communication_analytics(start_date, end_date)
        project_involvement = self._get_project_involvement_analytics(start_date, end_date)
        performance_insights = self._get_stakeholder_performance_insights(start_date, end_date)
        risk_assessment = self._get_stakeholder_risk_assessment(start_date, end_date)
        geographic_analysis = self._get_geographic_distribution_analytics()
        relationship_mapping = self._get_stakeholder_relationship_mapping()
        predictive_insights = self._get_predictive_engagement_insights(start_date, end_date)
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'days': days
            },
            'engagement': engagement_metrics,
            'communication': communication_metrics,
            'project_involvement': project_involvement,
            'performance': performance_insights,
            'risk_assessment': risk_assessment,
            'geographic': geographic_analysis,
            'relationships': relationship_mapping,
            'predictive': predictive_insights,
            'summary': self._generate_executive_summary(
                engagement_metrics, communication_metrics, 
                project_involvement, performance_insights
            )
        }
    
    def _get_engagement_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Calculate comprehensive stakeholder engagement metrics
        """
        base_filter = Q()
        if self.organization:
            base_filter = Q(contact_company__in=self.organization.stakeholder_companies.all())
        
        # Current period metrics
        current_stakeholders = Stakeholder.objects.filter(base_filter)
        total_stakeholders = current_stakeholders.count()
        
        # Engagement categories
        highly_engaged = self._get_stakeholders_by_engagement_level('high', start_date, end_date)
        moderately_engaged = self._get_stakeholders_by_engagement_level('medium', start_date, end_date)
        low_engaged = self._get_stakeholders_by_engagement_level('low', start_date, end_date)
        inactive = total_stakeholders - (highly_engaged + moderately_engaged + low_engaged)
        
        # Engagement score calculation
        engagement_score = self._calculate_engagement_score(start_date, end_date)
        
        # Previous period comparison
        prev_start = start_date - timedelta(days=(end_date - start_date).days)
        prev_engagement_score = self._calculate_engagement_score(prev_start, start_date)
        engagement_trend = engagement_score - prev_engagement_score
        
        # Communication frequency analysis
        avg_communications_per_stakeholder = self._get_avg_communications_per_stakeholder(start_date, end_date)
        
        # Response rate analysis
        response_metrics = self._calculate_response_rates(start_date, end_date)
        
        return {
            'total_stakeholders': total_stakeholders,
            'engagement_distribution': {
                'highly_engaged': highly_engaged,
                'moderately_engaged': moderately_engaged,
                'low_engaged': low_engaged,
                'inactive': inactive
            },
            'engagement_score': round(engagement_score, 2),
            'engagement_trend': round(engagement_trend, 2),
            'avg_communications_per_stakeholder': round(avg_communications_per_stakeholder, 2),
            'response_metrics': response_metrics,
            'engagement_categories': self._get_engagement_by_category(start_date, end_date)
        }
    
    def _get_communication_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Analyze communication patterns and effectiveness
        """
        # Communication volume analysis
        communication_filter = Q(
            content__startswith='[COMMUNICATION]',
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        total_communications = Comment.objects.filter(communication_filter).count()
        
        # Communication by method
        communication_methods = self._analyze_communication_methods(start_date, end_date)
        
        # Communication timing analysis
        timing_analysis = self._analyze_communication_timing(start_date, end_date)
        
        # Response time analysis
        response_time_metrics = self._calculate_response_times(start_date, end_date)
        
        # Communication effectiveness
        effectiveness_metrics = self._analyze_communication_effectiveness(start_date, end_date)
        
        # Trending topics/themes
        communication_themes = self._extract_communication_themes(start_date, end_date)
        
        return {
            'total_communications': total_communications,
            'communication_methods': communication_methods,
            'timing_analysis': timing_analysis,
            'response_times': response_time_metrics,
            'effectiveness': effectiveness_metrics,
            'themes': communication_themes,
            'volume_trend': self._get_communication_volume_trend(start_date, end_date)
        }
    
    def _get_project_involvement_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Analyze stakeholder involvement in projects
        """
        # Project assignment analysis
        assignment_filter = Q(
            content__startswith='[STAKEHOLDER_ASSIGNMENT]',
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        total_assignments = Comment.objects.filter(assignment_filter).count()
        
        # Project types and stakeholder involvement
        project_type_involvement = self._analyze_project_type_involvement(start_date, end_date)
        
        # Stakeholder roles in projects
        role_distribution = self._analyze_stakeholder_roles(start_date, end_date)
        
        # Project success correlation
        success_correlation = self._analyze_project_success_correlation(start_date, end_date)
        
        # Collaboration patterns
        collaboration_metrics = self._analyze_collaboration_patterns(start_date, end_date)
        
        return {
            'total_assignments': total_assignments,
            'project_type_involvement': project_type_involvement,
            'role_distribution': role_distribution,
            'success_correlation': success_correlation,
            'collaboration_metrics': collaboration_metrics,
            'involvement_trend': self._get_involvement_trend(start_date, end_date)
        }
    
    def _get_stakeholder_performance_insights(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Generate performance insights for stakeholders
        """
        # Top performers identification
        top_performers = self._identify_top_performing_stakeholders(start_date, end_date)
        
        # Performance metrics by category
        performance_by_category = self._analyze_performance_by_category(start_date, end_date)
        
        # Impact assessment
        impact_assessment = self._assess_stakeholder_impact(start_date, end_date)
        
        # Efficiency metrics
        efficiency_metrics = self._calculate_stakeholder_efficiency(start_date, end_date)
        
        return {
            'top_performers': top_performers,
            'performance_by_category': performance_by_category,
            'impact_assessment': impact_assessment,
            'efficiency_metrics': efficiency_metrics,
            'performance_trends': self._get_performance_trends(start_date, end_date)
        }
    
    def _get_stakeholder_risk_assessment(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Assess risks related to stakeholder engagement
        """
        # Identify at-risk stakeholder relationships
        at_risk_stakeholders = self._identify_at_risk_stakeholders(start_date, end_date)
        
        # Communication gaps analysis
        communication_gaps = self._analyze_communication_gaps(start_date, end_date)
        
        # Project dependency risks
        dependency_risks = self._assess_project_dependency_risks(start_date, end_date)
        
        # Stakeholder turnover analysis
        turnover_analysis = self._analyze_stakeholder_turnover(start_date, end_date)
        
        return {
            'at_risk_stakeholders': at_risk_stakeholders,
            'communication_gaps': communication_gaps,
            'dependency_risks': dependency_risks,
            'turnover_analysis': turnover_analysis,
            'risk_score': self._calculate_overall_risk_score(
                at_risk_stakeholders, communication_gaps, dependency_risks
            )
        }
    
    def _get_geographic_distribution_analytics(self) -> Dict[str, Any]:
        """
        Analyze geographic distribution of stakeholders
        """
        # Distribution by location/company
        geographic_distribution = Stakeholder.objects.values('contact_company').annotate(
            count=Count('id'),
            avg_engagement=Avg(
                Case(
                    When(id__in=self._get_engaged_stakeholder_ids(), then=Value(1)),
                    default=Value(0),
                    output_field=DecimalField()
                )
            )
        ).order_by('-count')
        
        return {
            'distribution': list(geographic_distribution),
            'total_companies': geographic_distribution.count(),
            'concentration_analysis': self._analyze_stakeholder_concentration()
        }
    
    def _get_stakeholder_relationship_mapping(self) -> Dict[str, Any]:
        """
        Map relationships between stakeholders
        """
        # Analyze collaborative relationships
        collaboration_network = self._build_collaboration_network()
        
        # Identify key connectors
        key_connectors = self._identify_key_connector_stakeholders()
        
        # Relationship strength analysis
        relationship_strengths = self._analyze_relationship_strengths()
        
        return {
            'collaboration_network': collaboration_network,
            'key_connectors': key_connectors,
            'relationship_strengths': relationship_strengths,
            'network_metrics': self._calculate_network_metrics()
        }
    
    def _get_predictive_engagement_insights(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Generate predictive insights for stakeholder engagement
        """
        # Engagement trend prediction
        engagement_forecast = self._forecast_engagement_trends(start_date, end_date)
        
        # Risk prediction
        risk_forecast = self._predict_engagement_risks(start_date, end_date)
        
        # Opportunity identification
        opportunities = self._identify_engagement_opportunities(start_date, end_date)
        
        return {
            'engagement_forecast': engagement_forecast,
            'risk_forecast': risk_forecast,
            'opportunities': opportunities,
            'recommended_actions': self._generate_recommended_actions(
                engagement_forecast, risk_forecast, opportunities
            )
        }
    
    # Helper methods for advanced analytics
    
    def _get_stakeholders_by_engagement_level(self, level: str, start_date: datetime, end_date: datetime) -> int:
        """Categorize stakeholders by engagement level"""
        if level == 'high':
            pass  # 10+ interactions
        elif level == 'medium':
            pass   # 5-9 interactions
        else:  # low
            pass   # 1-4 interactions
        
        engaged_stakeholders = self._get_stakeholder_interaction_counts(start_date, end_date)
        
        if level == 'high':
            return len([s for s in engaged_stakeholders if s['count'] >= 10])
        elif level == 'medium':
            return len([s for s in engaged_stakeholders if 5 <= s['count'] < 10])
        else:  # low
            return len([s for s in engaged_stakeholders if 1 <= s['count'] < 5])
    
    def _get_stakeholder_interaction_counts(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Get interaction counts for all stakeholders"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    s.id,
                    s.full_name,
                    s.contact_company,
                    COUNT(c.id) as interaction_count
                FROM CLEAR_stakeholder s
                LEFT JOIN CLEAR_comment c ON (
                    c.commentable_id = s.id 
                    AND c.commentable_type = 'stakeholder'
                    AND (c.content LIKE '[COMMUNICATION]%%' OR c.content LIKE '[STAKEHOLDER_ASSIGNMENT]%%')
                    AND c.created_at >= %s 
                    AND c.created_at <= %s
                )
                GROUP BY s.id, s.full_name, s.contact_company
                ORDER BY interaction_count DESC
            """, [start_date, end_date])
            
            return [
                {
                    'id': row[0],
                    'name': row[1],
                    'company': row[2],
                    'count': row[3]
                }
                for row in cursor.fetchall()
            ]
    
    def _calculate_engagement_score(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate overall engagement score"""
        total_stakeholders = Stakeholder.objects.count()
        if total_stakeholders == 0:
            return 0.0
        
        interaction_counts = self._get_stakeholder_interaction_counts(start_date, end_date)
        sum(s['count'] for s in interaction_counts)
        
        # Weighted engagement score
        score = 0
        for stakeholder in interaction_counts:
            count = stakeholder['count']
            if count >= 10:
                score += 100  # Highly engaged
            elif count >= 5:
                score += 60   # Moderately engaged
            elif count >= 1:
                score += 30   # Low engaged
            # Inactive = 0 points
        
        return (score / total_stakeholders) if total_stakeholders > 0 else 0.0
    
    def _get_avg_communications_per_stakeholder(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average communications per stakeholder"""
        total_stakeholders = Stakeholder.objects.count()
        if total_stakeholders == 0:
            return 0.0
        
        total_communications = Comment.objects.filter(
            content__startswith='[COMMUNICATION]',
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()
        
        return total_communications / total_stakeholders
    
    def _calculate_response_rates(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate response rates and timing"""
        # Simplified response rate calculation
        # In a real implementation, this would track actual responses
        return {
            'response_rate': 85.5,  # Percentage
            'avg_response_time': 2.3,  # Hours
            'same_day_responses': 78.2  # Percentage
        }
    
    def _get_engagement_by_category(self, start_date: datetime, end_date: datetime) -> Dict[str, int]:
        """Get engagement metrics by stakeholder category"""
        categories = {}
        
        stakeholder_types = Stakeholder.objects.values('stakeholder_type').annotate(
            engagement_count=Count(
                'id',
                filter=Q(
                    id__in=Comment.objects.filter(
                        content__startswith='[COMMUNICATION]',
                        created_at__gte=start_date,
                        created_at__lte=end_date
                    ).values_list('commentable_id', flat=True)
                )
            )
        )
        
        for item in stakeholder_types:
            category = item['stakeholder_type'] or 'Unspecified'
            categories[category] = item['engagement_count']
        
        return categories
    
    def _analyze_communication_methods(self, start_date: datetime, end_date: datetime) -> Dict[str, int]:
        """Analyze communication methods used"""
        # Mock data - in real implementation, would parse communication content
        return {
            'email': 45,
            'phone': 28,
            'meeting': 22,
            'document_sharing': 15,
            'instant_message': 12
        }
    
    def _analyze_communication_timing(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Analyze when communications typically occur"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    EXTRACT(hour FROM created_at) as hour,
                    COUNT(*) as count
                FROM CLEAR_comment 
                WHERE content LIKE '[COMMUNICATION]%%'
                AND created_at >= %s 
                AND created_at <= %s
                GROUP BY EXTRACT(hour FROM created_at)
                ORDER BY hour
            """, [start_date, end_date])
            
            hourly_distribution = {str(i): 0 for i in range(24)}
            for row in cursor.fetchall():
                hourly_distribution[str(int(row[0]))] = row[1]
            
            return {
                'hourly_distribution': hourly_distribution,
                'peak_hours': [h for h, count in hourly_distribution.items() if count == max(hourly_distribution.values())],
                'business_hours_percentage': sum(hourly_distribution[str(h)] for h in range(8, 18)) / sum(hourly_distribution.values()) * 100 if sum(hourly_distribution.values()) > 0 else 0
            }
    
    def _calculate_response_times(self, start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """Calculate communication response time metrics"""
        # Mock implementation - would track actual response chains
        return {
            'avg_response_time_hours': 4.2,
            'median_response_time_hours': 2.1,
            'response_rate_percentage': 87.3
        }
    
    def _analyze_communication_effectiveness(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Analyze effectiveness of communications"""
        # Mock implementation - would analyze outcomes
        return {
            'resolution_rate': 82.5,
            'follow_up_rate': 45.2,
            'satisfaction_score': 4.3
        }
    
    def _extract_communication_themes(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Extract common themes from communications"""
        # Mock implementation - would use NLP for actual theme extraction
        return [
            {'theme': 'Project Timeline', 'frequency': 35, 'sentiment': 'neutral'},
            {'theme': 'Budget Concerns', 'frequency': 28, 'sentiment': 'negative'},
            {'theme': 'Technical Requirements', 'frequency': 42, 'sentiment': 'positive'},
            {'theme': 'Regulatory Compliance', 'frequency': 23, 'sentiment': 'neutral'},
            {'theme': 'Resource Allocation', 'frequency': 31, 'sentiment': 'negative'}
        ]
    
    def _get_communication_volume_trend(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get communication volume trend over time"""
        daily_communications = Comment.objects.filter(
            content__startswith='[COMMUNICATION]',
            created_at__gte=start_date,
            created_at__lte=end_date
        ).annotate(
            day=TruncDay('created_at')
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        return [
            {
                'date': item['day'].isoformat(),
                'count': item['count']
            }
            for item in daily_communications
        ]
    
    # Additional helper methods would continue here...
    # For brevity, including key methods only
    
    def _generate_executive_summary(self, engagement_metrics, communication_metrics, 
                                  project_involvement, performance_insights) -> Dict[str, Any]:
        """Generate executive summary of stakeholder analytics"""
        return {
            'overall_health_score': self._calculate_overall_health_score(
                engagement_metrics, communication_metrics
            ),
            'key_insights': self._extract_key_insights(
                engagement_metrics, communication_metrics, project_involvement
            ),
            'recommendations': self._generate_recommendations(
                engagement_metrics, performance_insights
            ),
            'risk_level': self._assess_overall_risk_level(engagement_metrics)
        }
    
    def _calculate_overall_health_score(self, engagement_metrics, communication_metrics) -> int:
        """Calculate overall stakeholder relationship health score"""
        engagement_score = engagement_metrics.get('engagement_score', 0)
        communication_volume = communication_metrics.get('total_communications', 0)
        
        # Weighted calculation
        health_score = (engagement_score * 0.6) + (min(communication_volume / 10, 40) * 0.4)
        return min(100, int(health_score))
    
    def _extract_key_insights(self, engagement_metrics, communication_metrics, project_involvement) -> List[str]:
        """Extract key insights from analytics"""
        insights = []
        
        if engagement_metrics.get('engagement_trend', 0) > 5:
            insights.append("Stakeholder engagement is trending upward")
        elif engagement_metrics.get('engagement_trend', 0) < -5:
            insights.append("Stakeholder engagement is declining and needs attention")
        
        highly_engaged = engagement_metrics.get('engagement_distribution', {}).get('highly_engaged', 0)
        total = engagement_metrics.get('total_stakeholders', 1)
        
        if highly_engaged / total > 0.3:
            insights.append("Strong core of highly engaged stakeholders")
        elif highly_engaged / total < 0.1:
            insights.append("Limited highly engaged stakeholders - focus on relationship building")
        
        return insights
    
    def _generate_recommendations(self, engagement_metrics, performance_insights) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        inactive_count = engagement_metrics.get('engagement_distribution', {}).get('inactive', 0)
        total = engagement_metrics.get('total_stakeholders', 1)
        
        if inactive_count / total > 0.4:
            recommendations.append("Implement re-engagement campaign for inactive stakeholders")
        
        if engagement_metrics.get('engagement_trend', 0) < 0:
            recommendations.append("Review communication strategy and stakeholder feedback")
        
        recommendations.append("Schedule quarterly stakeholder review meetings")
        recommendations.append("Implement stakeholder feedback collection system")
        
        return recommendations
    
    def _assess_overall_risk_level(self, engagement_metrics) -> str:
        """Assess overall risk level"""
        score = engagement_metrics.get('engagement_score', 0)
        trend = engagement_metrics.get('engagement_trend', 0)
        
        if score > 70 and trend >= 0:
            return 'low'
        elif score > 50 and trend > -5:
            return 'medium'
        else:
            return 'high'
    
    # Placeholder methods for advanced features
    def _analyze_project_type_involvement(self, start_date, end_date):
        return {}
    
    def _analyze_stakeholder_roles(self, start_date, end_date):
        return {}
    
    def _analyze_project_success_correlation(self, start_date, end_date):
        return {}
    
    def _analyze_collaboration_patterns(self, start_date, end_date):
        return {}
    
    def _get_involved_stakeholder_ids(self):
        return []
    
    def _get_engaged_stakeholder_ids(self):
        return Comment.objects.filter(
            content__startswith='[COMMUNICATION]'
        ).values_list('commentable_id', flat=True).distinct()
    
    def _identify_top_performing_stakeholders(self, start_date, end_date):
        return []
    
    def _analyze_performance_by_category(self, start_date, end_date):
        return {}
    
    def _assess_stakeholder_impact(self, start_date, end_date):
        return {}
    
    def _calculate_stakeholder_efficiency(self, start_date, end_date):
        return {}
    
    def _get_performance_trends(self, start_date, end_date):
        return []
    
    def _identify_at_risk_stakeholders(self, start_date, end_date):
        return []
    
    def _analyze_communication_gaps(self, start_date, end_date):
        return {}
    
    def _assess_project_dependency_risks(self, start_date, end_date):
        return {}
    
    def _analyze_stakeholder_turnover(self, start_date, end_date):
        return {}
    
    def _calculate_overall_risk_score(self, at_risk, gaps, dependencies):
        return 'medium'
    
    def _analyze_stakeholder_concentration(self):
        return {}
    
    def _build_collaboration_network(self):
        return {}
    
    def _identify_key_connector_stakeholders(self):
        return []
    
    def _analyze_relationship_strengths(self):
        return {}
    
    def _calculate_network_metrics(self):
        return {}
    
    def _forecast_engagement_trends(self, start_date, end_date):
        return {}
    
    def _predict_engagement_risks(self, start_date, end_date):
        return {}
    
    def _identify_engagement_opportunities(self, start_date, end_date):
        return []
    
    def _generate_recommended_actions(self, forecast, risks, opportunities):
        return []
    
    def _get_involvement_trend(self, start_date, end_date):
        return []