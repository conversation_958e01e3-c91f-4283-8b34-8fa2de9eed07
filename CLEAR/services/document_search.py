"""
Document Search Service for CLEAR application.

Provides advanced document search, filtering, and indexing capabilities
using PostgreSQL full-text search and custom search algorithms.
"""
"""



import logging
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List
from django.contrib.auth import get_user_model
from django.contrib.postgres.search import SearchQuery, SearchRank, SearchVector
from django.core.cache import cache
from django.db import connection
from django.db.models import Count, F, Q
from django.utils import timezone
from ..models import Document, DocumentActivity
import math
import os

"""




User = get_user_model()
logger = logging.getLogger(__name__)


class DocumentSearchService:
    """
    Advanced document search service with full-text search,
    filtering, ranking, and analytics.
    """
    
    def __init__(self, user: User = None):
        self.user = user
        self.cache_timeout = 300  # 5 minutes
        
    def search_documents(self, 
                        query: str = '', 
                        filters: Dict[str, Any] = None,
                        sort_by: str = 'relevance',
                        page: int = 1,
                        per_page: int = 20) -> Dict[str, Any]:
        """
        Perform advanced document search with filters and pagination.
        
        Args:
            query: Search query string
            filters: Dictionary of search filters
            sort_by: Sorting method ('relevance', 'date', 'name', 'size', 'activity')
            page: Page number for pagination
            per_page: Number of results per page
            
        Returns:
            Dictionary with search results and metadata
        """
        try:
            # Build base queryset
            queryset = Document.objects.all()
            
            # Apply user access permissions
            if self.user:
                queryset = queryset.filter(
                    Q(project__egis_project_manager=self.user.username) |
                    Q(project__coordinator_id=str(self.user.id)) |
                    Q(created_by=self.user) |
                    Q(is_public=True)
                ).distinct()
            else:
                queryset = queryset.filter(is_public=True)
            
            # Apply text search if query provided
            search_rank = None
            if query.strip():
                search_results = self._perform_fulltext_search(queryset, query)
                queryset = search_results['queryset']
                search_rank = search_results['rank_annotation']
            
            # Apply filters
            if filters:
                queryset = self._apply_filters(queryset, filters)
            
            # Apply sorting
            queryset = self._apply_sorting(queryset, sort_by, search_rank)
            
            # Count total results before pagination
            total_count = queryset.count()
            
            # Apply pagination
            offset = (page - 1) * per_page
            paginated_queryset = queryset[offset:offset + per_page]
            
            # Serialize results
            results = []
            for doc in paginated_queryset:
                result = self._serialize_document_result(doc)
                
                # Add search rank if available
                if hasattr(doc, 'search_rank'):
                    result['search_rank'] = doc.search_rank
                
                # Add relevance highlighting if query was provided
                if query.strip():
                    result['highlights'] = self._generate_highlights(doc, query)
                
                results.append(result)
            
            # Calculate pagination metadata
            total_pages = (total_count + per_page - 1) // per_page
            has_next = page < total_pages
            has_previous = page > 1
            
            # Generate search suggestions if low results
            suggestions = []
            if total_count < 3 and query.strip():
                suggestions = self._generate_search_suggestions(query)
            
            return {
                'results': results,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': has_next,
                    'has_previous': has_previous
                },
                'query': query,
                'filters': filters or {},
                'sort_by': sort_by,
                'suggestions': suggestions,
                'search_time_ms': 0  # TODO: Add timing
            }
            
        except Exception as e:
            logger.error(f"Error in document search: {e}")
            return {
                'results': [],
                'pagination': {
                    'page': 1,
                    'per_page': per_page,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_previous': False
                },
                'error': str(e)
            }
    
    def _perform_fulltext_search(self, queryset, query: str) -> Dict[str, Any]:
        """
        Perform PostgreSQL full-text search with ranking.
        
        Args:
            queryset: Base queryset to search
            query: Search query string
            
        Returns:
            Dictionary with filtered queryset and rank annotation
        """
        try:
            # Clean and prepare search query
            clean_query = self._clean_search_query(query)
            
            # Create search vector for multiple fields
            search_vector = SearchVector('name', weight='A') + \
                          SearchVector('description', weight='B') + \
                          SearchVector('extracted_content', weight='C') + \
                          SearchVector('tags', weight='D')
            
            # Create search query
            search_query = SearchQuery(clean_query)
            
            # Apply search and ranking
            queryset = queryset.annotate(
                search=search_vector,
                search_rank=SearchRank(search_vector, search_query)
            ).filter(search=search_query)
            
            return {
                'queryset': queryset,
                'rank_annotation': 'search_rank'
            }
            
        except Exception as e:
            logger.error(f"Error in full-text search: {e}")
            # Fallback to basic text search
            return self._fallback_text_search(queryset, query)
    
    def _fallback_text_search(self, queryset, query: str) -> Dict[str, Any]:
        """
        Fallback text search using icontains when full-text search fails.
        
        Args:
            queryset: Base queryset to search
            query: Search query string
            
        Returns:
            Dictionary with filtered queryset
        """
        search_terms = query.strip().split()
        q_objects = Q()
        
        for term in search_terms:
            q_objects |= (
                Q(name__icontains=term) |
                Q(description__icontains=term) |
                Q(extracted_content__icontains=term) |
                Q(tags__icontains=term)
            )
        
        return {
            'queryset': queryset.filter(q_objects),
            'rank_annotation': None
        }
    
    def _clean_search_query(self, query: str) -> str:
        """
        Clean and prepare search query for PostgreSQL full-text search.
        
        Args:
            query: Raw search query
            
        Returns:
            Cleaned search query
        """
        # Remove special characters that might break search
        clean_query = re.sub(r'[^\w\s\-\.]', ' ', query)
        
        # Normalize whitespace
        clean_query = ' '.join(clean_query.split())
        
        # Handle phrases in quotes
        if '"' in query:
            # Extract quoted phrases
            phrases = re.findall(r'"([^"]*)"', query)
            for phrase in phrases:
                clean_query = clean_query.replace(f'"{phrase}"', phrase.replace(' ', '<->'))
        
        return clean_query
    
    def _apply_filters(self, queryset, filters: Dict[str, Any]):
        """
        Apply search filters to the queryset.
        
        Args:
            queryset: Base queryset
            filters: Dictionary of filters to apply
            
        Returns:
            Filtered queryset
        """
        # Project filter
        if 'project_id' in filters and filters['project_id']:
            queryset = queryset.filter(project_id=filters['project_id'])
        
        # File type filter
        if 'file_type' in filters and filters['file_type']:
            file_types = filters['file_type'] if isinstance(filters['file_type'], list) else [filters['file_type']]
            file_type_q = Q()
            for file_type in file_types:
                file_type_q |= Q(name__iendswith=f'.{file_type}')
            queryset = queryset.filter(file_type_q)
        
        # Date range filter
        if 'date_from' in filters and filters['date_from']:
            try:
                date_from = datetime.strptime(filters['date_from'], '%Y-%m-%d')
                queryset = queryset.filter(created_at__gte=date_from)
            except ValueError:
                pass
        
        if 'date_to' in filters and filters['date_to']:
            try:
                date_to = datetime.strptime(filters['date_to'], '%Y-%m-%d')
                queryset = queryset.filter(created_at__lte=date_to + timedelta(days=1))
            except ValueError:
                pass
        
        # Size filter
        if 'min_size' in filters and filters['min_size']:
            try:
                min_size = int(filters['min_size'])
                queryset = queryset.filter(file_size__gte=min_size)
            except (ValueError, TypeError):
                pass
        
        if 'max_size' in filters and filters['max_size']:
            try:
                max_size = int(filters['max_size'])
                queryset = queryset.filter(file_size__lte=max_size)
            except (ValueError, TypeError):
                pass
        
        # Author filter
        if 'author' in filters and filters['author']:
            queryset = queryset.filter(created_by__username__icontains=filters['author'])
        
        # Tags filter
        if 'tags' in filters and filters['tags']:
            tags = filters['tags'] if isinstance(filters['tags'], list) else [filters['tags']]
            for tag in tags:
                queryset = queryset.filter(tags__icontains=tag)
        
        # Status filter
        if 'status' in filters and filters['status']:
            if filters['status'] == 'active':
                queryset = queryset.filter(is_archived=False)
            elif filters['status'] == 'archived':
                queryset = queryset.filter(is_archived=True)
        
        # Activity filter
        if 'has_activity' in filters and filters['has_activity']:
            if filters['has_activity']:
                queryset = queryset.filter(documentactivity__isnull=False).distinct()
            else:
                queryset = queryset.filter(documentactivity__isnull=True)
        
        # Discussion filter
        if 'has_discussions' in filters and filters['has_discussions']:
            if filters['has_discussions']:
                queryset = queryset.filter(documentdiscussion__isnull=False).distinct()
            else:
                queryset = queryset.filter(documentdiscussion__isnull=True)
        
        return queryset
    
    def _apply_sorting(self, queryset, sort_by: str, search_rank_field: str = None):
        """
        Apply sorting to the queryset.
        
        Args:
            queryset: Queryset to sort
            sort_by: Sorting method
            search_rank_field: Field name for search ranking
            
        Returns:
            Sorted queryset
        """
        if sort_by == 'relevance' and search_rank_field:
            return queryset.order_by(f'-{search_rank_field}', '-created_at')
        elif sort_by == 'date':
            return queryset.order_by('-created_at')
        elif sort_by == 'date_asc':
            return queryset.order_by('created_at')
        elif sort_by == 'name':
            return queryset.order_by('name')
        elif sort_by == 'name_desc':
            return queryset.order_by('-name')
        elif sort_by == 'size':
            return queryset.order_by('-file_size')
        elif sort_by == 'size_asc':
            return queryset.order_by('file_size')
        elif sort_by == 'activity':
            # Sort by most recent activity
            return queryset.annotate(
                last_activity=F('updated_at')
            ).order_by('-last_activity')
        else:
            # Default sorting
            return queryset.order_by('-created_at')
    
    def _serialize_document_result(self, document) -> Dict[str, Any]:
        """
        Serialize document for search results.
        
        Args:
            document: Document instance
            
        Returns:
            Serialized document data
        """
        return {
            'id': str(document.id),
            'name': document.name,
            'description': document.description or '',
            'file_size': document.file_size,
            'file_size_human': self._format_file_size(document.file_size),
            'file_type': self._get_file_type(document.name),
            'created_at': document.created_at.isoformat(),
            'updated_at': document.updated_at.isoformat(),
            'created_by': document.created_by.username if document.created_by else 'Unknown',
            'project_name': document.project.name if document.project else 'No Project',
            'project_id': str(document.project.id) if document.project else None,
            'tags': document.tags or '',
            'is_archived': document.is_archived,
            'has_thumbnail': bool(getattr(document, 'thumbnail_path', None)),
            'has_preview': bool(getattr(document, 'preview_path', None)),
            'url': f'/documents/{document.id}/',
            'download_url': f'/documents/{document.id}/download/',
        }
    
    def _generate_highlights(self, document, query: str) -> Dict[str, str]:
        """
        Generate search result highlights for the document.
        
        Args:
            document: Document instance
            query: Search query
            
        Returns:
            Dictionary with highlighted text snippets
        """
        highlights = {}
        search_terms = query.lower().split()
        
        # Highlight name
        if any(term in document.name.lower() for term in search_terms):
            highlights['name'] = self._highlight_text(document.name, search_terms)
        
        # Highlight description
        if document.description and any(term in document.description.lower() for term in search_terms):
            highlights['description'] = self._highlight_text(document.description, search_terms, 200)
        
        # Highlight content
        if hasattr(document, 'extracted_content') and document.extracted_content:
            if any(term in document.extracted_content.lower() for term in search_terms):
                highlights['content'] = self._highlight_text(document.extracted_content, search_terms, 300)
        
        return highlights
    
    def _highlight_text(self, text: str, search_terms: List[str], max_length: int = None) -> str:
        """
        Highlight search terms in text.
        
        Args:
            text: Text to highlight
            search_terms: List of search terms
            max_length: Maximum length of highlighted text
            
        Returns:
            Highlighted text with HTML tags
        """
        if not text or not search_terms:
            return text
        
        highlighted = text
        
        # Find best snippet if max_length is specified
        if max_length and len(text) > max_length:
            # Find the position of the first search term
            best_pos = 0
            for term in search_terms:
                pos = text.lower().find(term.lower())
                if pos != -1:
                    best_pos = max(0, pos - max_length // 4)
                    break
            
            # Extract snippet
            end_pos = min(len(text), best_pos + max_length)
            snippet = text[best_pos:end_pos]
            
            # Add ellipsis if truncated
            if best_pos > 0:
                snippet = '...' + snippet
            if end_pos < len(text):
                snippet = snippet + '...'
            
            highlighted = snippet
        
        # Apply highlighting
        for term in search_terms:
            if term.strip():
                pattern = re.compile(re.escape(term), re.IGNORECASE)
                highlighted = pattern.sub('<mark>\\g<0></mark>', highlighted)
        
        return highlighted
    
    def _generate_search_suggestions(self, query: str) -> List[str]:
        """
        Generate search suggestions for queries with few results.
        
        Args:
            query: Original search query
            
        Returns:
            List of suggested search terms
        """
        suggestions = []
        
        try:
            # Get popular search terms from cache
            popular_terms = cache.get('popular_search_terms', [])
            
            # Find similar terms
            query_words = query.lower().split()
            for term in popular_terms:
                if any(word in term.lower() for word in query_words):
                    suggestions.append(term)
            
            # Add document name suggestions
            similar_docs = Document.objects.filter(
                name__icontains=query[:10]
            )[:5]
            
            for doc in similar_docs:
                words = doc.name.split()
                for word in words:
                    if len(word) > 3 and word.lower() not in suggestions:
                        suggestions.append(word.lower())
            
            return suggestions[:5]
            
        except Exception as e:
            logger.error(f"Error generating search suggestions: {e}")
            return []
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def _get_file_type(self, filename: str) -> str:
        """Extract file type from filename."""
        return os.path.splitext(filename)[1].lower().lstrip('.')
    
    def get_search_analytics(self, timeframe_days: int = 30) -> Dict[str, Any]:
        """
        Get search analytics data.
        
        Args:
            timeframe_days: Number of days to analyze
            
        Returns:
            Dictionary with analytics data
        """
        try:
            since_date = timezone.now() - timedelta(days=timeframe_days)
            
            # Get search queries from cache or logs
            search_queries = cache.get('recent_search_queries', [])
            
            # Analyze query patterns
            query_analysis = self._analyze_search_queries(search_queries)
            
            # Get document access patterns
            access_patterns = self._analyze_document_access(since_date)
            
            # Get popular file types
            popular_file_types = self._get_popular_file_types(since_date)
            
            return {
                'timeframe_days': timeframe_days,
                'total_searches': len(search_queries),
                'unique_queries': len(set(q.lower() for q in search_queries)),
                'query_analysis': query_analysis,
                'access_patterns': access_patterns,
                'popular_file_types': popular_file_types,
                'search_trends': self._get_search_trends(search_queries)
            }
            
        except Exception as e:
            logger.error(f"Error getting search analytics: {e}")
            return {}
    
    def _analyze_search_queries(self, queries: List[str]) -> Dict[str, Any]:
        """Analyze search query patterns."""
        if not queries:
            return {}
        
        # Count query frequency
        query_counts = {}
        word_counts = {}
        
        for query in queries:
            query_lower = query.lower().strip()
            query_counts[query_lower] = query_counts.get(query_lower, 0) + 1
            
            # Count individual words
            words = query_lower.split()
            for word in words:
                if len(word) > 2:  # Ignore very short words
                    word_counts[word] = word_counts.get(word, 0) + 1
        
        # Get top queries and words
        top_queries = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        top_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:15]
        
        return {
            'total_queries': len(queries),
            'unique_queries': len(query_counts),
            'top_queries': top_queries,
            'top_words': top_words,
            'avg_query_length': sum(len(q.split()) for q in queries) / len(queries)
        }
    
    def _analyze_document_access(self, since_date: datetime) -> Dict[str, Any]:
        """Analyze document access patterns."""
        try:
            # Get document activities
            activities = DocumentActivity.objects.filter(
                created_at__gte=since_date,
                activity_type__in=['viewed', 'downloaded', 'searched']
            ).values('document_id').annotate(
                access_count=Count('id')
            ).order_by('-access_count')[:10]
            
            # Get document details
            popular_docs = []
            for activity in activities:
                try:
                    doc = Document.objects.get(id=activity['document_id'])
                    popular_docs.append({
                        'document_id': str(doc.id),
                        'document_name': doc.name,
                        'access_count': activity['access_count'],
                        'file_type': self._get_file_type(doc.name)
                    })
                except Document.DoesNotExist:
                    continue
            
            return {
                'popular_documents': popular_docs,
                'total_accesses': sum(a['access_count'] for a in activities)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing document access: {e}")
            return {}
    
    def _get_popular_file_types(self, since_date: datetime) -> List[Dict[str, Any]]:
        """Get popular file types based on search and access."""
        try:
            # Count documents by file type
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        LOWER(SUBSTRING(name FROM '\\.([^.]*)$')) as file_type,
                        COUNT(*) as count
                    FROM clear_document 
                    WHERE created_at >= %s 
                    GROUP BY file_type 
                    ORDER BY count DESC 
                    LIMIT 10
                """, [since_date])
                
                results = []
                for row in cursor.fetchall():
                    if row[0]:  # Skip null file types
                        results.append({
                            'file_type': row[0],
                            'count': row[1]
                        })
                
                return results
                
        except Exception as e:
            logger.error(f"Error getting popular file types: {e}")
            return []
    
    def _get_search_trends(self, queries: List[str]) -> Dict[str, Any]:
        """Analyze search trends over time."""
        # This would require storing query timestamps
        # For now, return basic trend data
        return {
            'daily_searches': [],  # Would need timestamp data
            'trending_terms': [],  # Would need historical comparison
            'search_volume_change': 0  # Would need historical data
        }
    
    def log_search_query(self, query: str, user_id: str = None, results_count: int = 0):
        """
        Log search query for analytics.
        
        Args:
            query: Search query string
            user_id: ID of the user performing search
            results_count: Number of results returned
        """
        try:
            # Store in cache for analytics
            cache_key = 'recent_search_queries'
            queries = cache.get(cache_key, [])
            queries.append(query)
            
            # Keep only recent queries (last 1000)
            if len(queries) > 1000:
                queries = queries[-1000:]
            
            cache.set(cache_key, queries, timeout=86400)  # 24 hours
            
            # Update popular terms
            self._update_popular_terms(query)
            
        except Exception as e:
            logger.error(f"Error logging search query: {e}")
    
    def _update_popular_terms(self, query: str):
        """Update popular search terms cache."""
        try:
            terms = cache.get('popular_search_terms', [])
            words = [word.lower() for word in query.split() if len(word) > 2]
            
            for word in words:
                if word not in terms:
                    terms.append(word)
            
            # Keep only recent terms (last 100)
            if len(terms) > 100:
                terms = terms[-100:]
            
            cache.set('popular_search_terms', terms, timeout=86400)
            
        except Exception as e:
            logger.error(f"Error updating popular terms: {e}")


def create_search_index():
    """
    Create or update PostgreSQL search indexes for better performance.
    This should be run as a Django management command.
    """
    try:
        with connection.cursor() as cursor:
            # Create GIN index for full-text search
            cursor.execute("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS clear_document_search_idx 
                ON clear_document USING GIN (
                    to_tsvector('english', 
                        COALESCE(name, '') || ' ' || 
                        COALESCE(description, '') || ' ' || 
                        COALESCE(extracted_content, '') || ' ' || 
                        COALESCE(tags, '')
                    )
                );
            """)
            
            # Create indexes for common filter fields
            cursor.execute("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS clear_document_created_at_idx 
                ON clear_document (created_at DESC);
            """)
            
            cursor.execute("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS clear_document_file_size_idx 
                ON clear_document (file_size);
            """)
            
            cursor.execute("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS clear_document_project_created_idx 
                ON clear_document (project_id, created_at DESC);
            """)
            
        logger.info("Search indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating search indexes: {e}")


# Global service instance
def get_document_search_service(user: User = None) -> DocumentSearchService:
    """Get document search service instance."""
    return DocumentSearchService(user=user)