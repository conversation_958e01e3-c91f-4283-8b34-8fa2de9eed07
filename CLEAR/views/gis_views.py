"""
GIS and Mapping Views for CLEAR Application.

This module contains all GIS-related views including:
- Utility management and visualization
- Conflict detection and management  
- Two and three dimensional mapping interfaces
- Spatial analysis tools
"""


import logging
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import CreateView, DetailView, ListView, TemplateView
from ..models import Conflict, Project, Utility


logger = logging.getLogger(__name__)



class UtilityListView(LoginRequiredMixin, ListView):
    """List utilities for a specific project"""
    model = Utility
    template_name = 'CLEAR/utilities/list.html'
    context_object_name = 'utilities'

    def get_queryset(self):
        self.project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        return self.project.utilities.all().order_by('type', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project'] = self.project
        return context


class UtilityDetailView(LoginRequiredMixin, DetailView):
    """Detailed utility view with spatial data"""
    model = Utility
    template_name = 'CLEAR/utilities/detail.html'
    context_object_name = 'utility'


class UtilityCreateView(LoginRequiredMixin, CreateView):
    """Create new utility for a project"""
    model = Utility
    template_name = 'CLEAR/utilities/create.html'
    fields = ['name', 'type', 'contact_name', 'contact_email', 'contact_phone', 'notes']

    def form_valid(self, form):
        project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        form.instance.project = project
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('CLEAR:utility_list', kwargs={'project_id': self.kwargs['project_id']})


class ConflictListView(LoginRequiredMixin, ListView):
    """List conflicts for a project"""
    model = Conflict
    template_name = 'CLEAR/conflicts/list.html'
    context_object_name = 'conflicts'

    def get_queryset(self):
        self.project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        return self.project.conflicts.select_related('utility', 'utility2').order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project'] = self.project
        return context


class ConflictDetailView(LoginRequiredMixin, DetailView):
    """Detailed conflict view with resolution tracking"""
    model = Conflict
    template_name = 'CLEAR/conflicts/detail.html'
    context_object_name = 'conflict'


class ConflictDetectionView(LoginRequiredMixin, TemplateView):
    """Run automated conflict detection for a project"""
    template_name = 'CLEAR/conflicts/detection.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project'] = get_object_or_404(Project, pk=self.kwargs['project_id'])
        return context


class ProjectMapView(LoginRequiredMixin, TemplateView):
    """Two-dimensional mapping interface for project"""
    template_name = 'CLEAR/mapping/map.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        context['project'] = project
        return context


class ProjectThreeDView(LoginRequiredMixin, TemplateView):
    """Three-dimensional visualization interface for project"""
    template_name = 'CLEAR/mapping/3d.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, pk=self.kwargs['project_id'])
        context['project'] = project
        
        # Calculate project center for three-dimensional coordinate system
        if project.geometry:
            centroid = project.geometry.centroid
            context['project_center'] = {
                'lat': centroid.y,
                'lng': centroid.x
            }
        else:
            # Default center for Indiana
            context['project_center'] = {
                'lat': 39.7684,
                'lng': -86.1581
            }
        
        return context


# ============================================================================
# MISSING GIS/MEETINGS VIEWS - ADDED TO UNBLOCK SERVER STARTUP  
# ============================================================================

class MeetingsView(LoginRequiredMixin, TemplateView):
    """Meetings and collaboration view"""
    template_name = 'CLEAR/meetings.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['meetings'] = []
        return context