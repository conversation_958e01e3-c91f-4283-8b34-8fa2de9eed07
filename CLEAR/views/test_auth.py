import logging
import time

from django.contrib import messages
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.core.cache import cache
from django.db import connection
from django.http import HttpResponse
from django.shortcuts import render

logger = logging.getLogger(__name__)




def test_login_view(request):
    """
    HTMX-enhanced test login view with progressive enhancement
    Supports validation, prefilling, status checks, and hypermedia responses
    # Handle HTMX-specific requests
    if request.headers.get('HX-Request'):
        return handle_htmx_request(request)
    
    # Regular form handling for progressive enhancement
    debug_info = []
    username = ""
    
    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        password = request.POST.get('password', '').strip()
        
        debug_info.append("POST data received:")
        debug_info.append(f"  Username: '{username}'")
        debug_info.append(f"  Password: {'*' * len(password) if password else 'EMPTY'}")
        
        if username and password:
            # Try authentication
            debug_info.append("\nAttempting authentication...")
            user = authenticate(request, username=username, password=password)
            debug_info.append(f"  Authentication result: {user}")
            
            if user:
                debug_info.append(f"  User is active: {user.is_active}")
                
                if user.is_active:
                    login(request, user)
                    messages.success(request, f'Successfully logged in as {user.username}!')
                    debug_info.append("  Login successful, user logged in")
                    
                    # For HTMX requests, return success partial
                    if request.headers.get('HX-Request'):
                        return render(request, 'components/test_login_success.html', {
                            'user': user,
                            'request': request
                        })
                    
                    # Regular redirect for non-HTMX
                    return HttpResponse(f"""
<h1>SUCCESS!</h1>
<p>User {user.username} ({user.email}) logged in successfully!</p>
<p>Is authenticated: {request.user.is_authenticated}</p>
<p>Current user: {request.user}</p>
<a href="/test-login/">Try again</a>
""")
                else:
                    messages.error(request, 'User account is disabled')
                    debug_info.append("  User account is disabled")
            else:
                messages.error(request, 'Invalid username or password')
                debug_info.append("  Authentication failed - invalid credentials")
        else:
            messages.error(request, 'Username and password are required')
            debug_info.append("  Missing username or password")
    
    return render(request, 'test_login.html', {
        'username': username,
        'debug_info': '\n'.join(debug_info) if debug_info else None
    })


def handle_htmx_request(request):
    """Handle HTMX-specific requests with hypermedia responses"""
    
    # Username validation on blur
    if request.GET.get('validate') == 'username':
        username = request.GET.get('username', '').strip()
        if not username:
            return HttpResponse('<small class="text-muted">Enter a username or email</small>')
        
        # Check if user exists (basic validation)
        try:
            user = User.objects.get(username=username)
            return HttpResponse(f'<small class="text-success"><i class="fas fa-check me-1"></i>User found: {user.get_full_name() or user.username}</small>')
        except User.DoesNotExist:
            try:
                user = User.objects.get(email=username)
                return HttpResponse(f'<small class="text-success"><i class="fas fa-check me-1"></i>User found: {user.get_full_name() or user.username}</small>')
            except User.DoesNotExist:
                return HttpResponse('<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>User not found (test anyway)</small>')
    
    # User prefill functionality
    prefill_type = request.GET.get('prefill')
    if prefill_type:
        test_users = get_test_user_data()
        user_data = test_users.get(prefill_type, {})
        
        return render(request, 'components/test_login_form.html', {
            'username': user_data.get('username', ''),
            'prefilled': True,
            'prefill_type': prefill_type
        })
    
    # System status check
    if request.GET.get('check') == 'status':
        return render(request, 'components/test_system_status.html', {
            'status': get_system_status()
        })
    
    # Default HTMX response
    return HttpResponse('<div class="alert alert-info">HTMX request received</div>')


def get_test_user_data():
    """Get test user data for prefilling"""
    return {
        'admin': {
            'username': 'admin',
            'hint': 'Administrative user with full permissions'
        },
        'test': {
            'username': 'testuser',
            'hint': 'Standard test user account'
        },
        'demo': {
            'username': '<EMAIL>',
            'hint': 'Demo user with sample data'
        }
    }


def get_system_status():
    """Get current system status for monitoring"""
    status = {
        'database': 'unknown',
        'cache': 'unknown',
        'users_online': 0,
        'response_time': 0,
        'last_check': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    start_time = time.time()
    
    # Check database connectivity
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            status['database'] = 'connected'
    except Exception as e:
        status['database'] = f'error: {str(e)[:50]}'
    
    # Check cache connectivity
    try:
        cache.set('test_key', 'test_value', 10)
        test_val = cache.get('test_key')
        if test_val == 'test_value':
            status['cache'] = 'connected'
        else:
            status['cache'] = 'error: value mismatch'
    except Exception as e:
        status['cache'] = f'error: {str(e)[:50]}'
    
    # Get user count (simple approximation)
    try:
        status['users_online'] = User.objects.filter(is_active=True).count()
    except Exception:
        status['users_online'] = 'unknown'
    
    # Calculate response time
    status['response_time'] = round((time.time() - start_time) * 1000, 2)
    
    return status