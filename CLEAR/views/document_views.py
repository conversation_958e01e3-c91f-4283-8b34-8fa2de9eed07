"""
Document management views for the CLEAR application.

This module contains all views related to document upload, viewing, sharing,
and management functionality, extracted from the main views.py file for
better organization and maintainability.
"""


import logging
import os
import re
from datetime import datetime
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Count, Q, Sum
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView, TemplateView
from django.conf import settings
from django.utils import timezone
from django.db import models
import uuid
import logging

from ..models import (
    Document,
    DocumentDiscussion,
    DocumentDiscussionMessage,
    DocumentDiscussionParticipant,
    DocumentSearchIndex,
    DocumentShare,
    DocumentVersion,
    DocumentVersionBranch,
    DocumentVersionDiff,
    Project,
    ProjectMember,
    Task,
    User,
)
from ..services.document_processing import document_processor
from ..services.file_security import FileSecurityValidator

logger = logging.getLogger(__name__)

# ========== DOCUMENT MANAGEMENT VIEWS ==========

class DocumentListView(LoginRequiredMixin, ListView):
    """Main document management interface"""
    model = Document
    template_name = 'documents/documents.html'
    context_object_name = 'documents'
    paginate_by = 50

    def get_queryset(self):
        folder = self.request.GET.get('folder', '/')
        search = self.request.GET.get('search', '')
        
        queryset = Document.objects.select_related(
            'uploaded_by', 'project', 'task'
        ).prefetch_related(
            'versions', 'shares'
        ).filter(
            folder__startswith=folder
        )
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(description__icontains=search) |
                Q(tags__icontains=search)
            )
        
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        folder = self.request.GET.get('folder', '/')
        
        # Get folder structure for navigation
        context.update({
            'current_folder': folder,
            'breadcrumbs': self.get_breadcrumbs(folder),
            'subfolders': self.get_subfolders(folder),
            'folder_stats': self.get_folder_stats(folder),
            'recent_documents': self.get_recent_documents(),
        })
        
        return context
    
    def get_breadcrumbs(self, folder):
        """Generate breadcrumb navigation"""
        if folder == '/':
            return [{'name': 'Documents', 'path': '/'}]
        
        parts = folder.strip('/').split('/')
        breadcrumbs = [{'name': 'Documents', 'path': '/'}]
        
        current_path = ''
        for part in parts:
            current_path += '/' + part
            breadcrumbs.append({
                'name': part.title(),
                'path': current_path + '/'
            })
        
        return breadcrumbs
    
    def get_subfolders(self, folder):
        """Get immediate subfolders"""
        len(folder.strip('/').split('/')) if folder != '/' else 0
        
        subfolders = Document.objects.filter(
            folder__startswith=folder,
            folder__regex=rf'^{folder}[^/]+/$'
        ).values_list('folder', flat=True).distinct()
        
        return [{'name': sf.split('/')[-2], 'path': sf} for sf in subfolders]
    
    def get_folder_stats(self, folder):
        """Get statistics for current folder"""
        documents = Document.objects.filter(folder=folder)
        
        return {
            'total_files': documents.count(),
            'total_size': documents.aggregate(
                total=Sum('file_size'))['total'] or 0,
            'file_types': documents.values('file_type').annotate(
                count=Count('id')).order_by('-count')[:5]
        }
    
    def get_recent_documents(self):
        """Get recently uploaded documents"""
        return Document.objects.select_related(
            'uploaded_by'
        ).order_by('-created_at')[:10]


@require_http_methods(["POST"])
@login_required
def document_upload_htmx(request):
    """Handle file uploads via HTMX with comprehensive security validation"""

    
    # Allowed file types and their MIME types
    ALLOWED_FILE_TYPES = {
        'pdf': ['application/pdf'],
        'doc': ['application/msword'],
        'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'xls': ['application/vnd.ms-excel'],
        'xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        'txt': ['text/plain'],
        'jpg': ['image/jpeg'],
        'jpeg': ['image/jpeg'],
        'png': ['image/png'],
        'gif': ['image/gif'],
        'dwg': ['application/acad', 'image/vnd.dwg'],
        'dxf': ['application/dxf', 'image/vnd.dxf'],
        'zip': ['application/zip'],
        'csv': ['text/csv', 'application/csv']
    }
    
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    try:
        files = request.FILES.getlist('files')
        folder = request.POST.get('folder', '/')
        project_id = request.POST.get('project_id')
        task_id = request.POST.get('task_id')
        
        if not files:
            return HttpResponse('No files provided', status=400)
        
        # Validate and sanitize folder path to prevent directory traversal
        folder = _sanitize_folder_path(folder)
        
        # Get project and task objects if provided
        project = None
        task = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
                # Check if user has permission to upload to this project
                if not _user_can_upload_to_project(request.user, project):
                    return HttpResponse('Permission denied for this project', status=403)
            except Project.DoesNotExist:
                return HttpResponse('Project not found', status=404)
        
        if task_id:
            try:
                task = Task.objects.get(id=task_id)
                # Check if user has permission to upload to this task
                if not _user_can_upload_to_task(request.user, task):
                    return HttpResponse('Permission denied for this task', status=403)
            except Task.DoesNotExist:
                return HttpResponse('Task not found', status=404)
        
        uploaded_documents = []
        security_validator = FileSecurityValidator()
        
        for file in files:
            try:
                # Comprehensive file validation
                validation_result = _validate_uploaded_file(file, ALLOWED_FILE_TYPES, MAX_FILE_SIZE)
                if not validation_result['valid']:
                    logger.warning(f"File validation failed for {file.name}: {validation_result['error']}")
                    continue
                
                # Advanced security scanning
                security_result = security_validator.scan_file(file)
                if not security_result['safe']:
                    logger.warning(f"Security scan failed for {file.name}: {security_result['reason']}")
                    continue
                
                # Generate secure filename
                secure_filename = _generate_secure_filename(file.name)
                
                # Create document record
                document = Document.objects.create(
                    name=file.name,  # Keep original name for display
                    description=f"Uploaded by {request.user.get_full_name() or request.user.username}",
                    file_path=f"documents/{folder}/{secure_filename}",
                    file_size=file.size,
                    file_type=validation_result['file_type'],
                    mime_type=validation_result['detected_mime_type'],
                    folder=folder,
                    project=project,
                    task=task,
                    uploaded_by=request.user
                )
                
                # Create secure upload directory
                upload_dir = os.path.join(settings.MEDIA_ROOT, 'documents', folder)
                os.makedirs(upload_dir, exist_ok=True, mode=0o755)
                
                # Save the file with secure filename
                file_path = os.path.join(upload_dir, secure_filename)
                with open(file_path, 'wb+') as destination:
                    for chunk in file.chunks():
                        destination.write(chunk)
                
                # Set secure file permissions
                os.chmod(file_path, 0o644)
                
                # Update document with actual file path
                document.file_path = file_path
                document.save()
                
                # Process document for thumbnails and previews
                try:
                    processing_results = document_processor.process_document(document)
                    if processing_results.get('errors'):
                        logger.warning(f"Document processing warnings for {document.id}: {processing_results['errors']}")
                except Exception as e:
                    logger.error(f"Document processing failed for {document.id}: {e}")
                
                uploaded_documents.append(document)
                
            except Exception as e:
                logger.error(f"Failed to process file {file.name}: {e}")
                continue
        
        if not uploaded_documents:
            return HttpResponse('No files were successfully uploaded due to security restrictions', status=400)
        
        return render(request, 'components/documents/file_grid_items.html', {
            'documents': uploaded_documents
        })
        
    except Exception as e:
        logger.error(f"Document upload failed: {e}")
        return HttpResponse('Upload failed due to server error', status=500)


def _sanitize_folder_path(folder):
    """Sanitize folder path to prevent directory traversal attacks"""
    
    # Remove any path traversal attempts
    folder = folder.replace('..', '').replace('//', '/')
    
    # Remove leading/trailing slashes and whitespace
    folder = folder.strip('/ ')
    
    # Only allow alphanumeric characters, hyphens, underscores, and forward slashes
    folder = re.sub(r'[^a-zA-Z0-9\-_/]', '', folder)
    
    # Ensure folder doesn't start with slash
    folder = folder.lstrip('/')
    
    return folder or 'general'


def _validate_uploaded_file(file, allowed_types, max_size):
    """Comprehensive file validation including MIME type detection"""
    result = {'valid': False, 'error': '', 'file_type': '', 'detected_mime_type': ''}
    
    # Check file size
    if file.size > max_size:
        result['error'] = f'File size ({file.size} bytes) exceeds maximum allowed size ({max_size} bytes)'
        return result
    
    # Check if file has an extension
    if '.' not in file.name:
        result['error'] = 'File must have an extension'
        return result
    
    # Get file extension
    file_extension = file.name.split('.')[-1].lower()
    
    # Check if extension is allowed
    if file_extension not in allowed_types:
        result['error'] = f'File type .{file_extension} is not allowed'
        return result
    
    # Read file content to detect actual MIME type
    try:
        file.seek(0)
        file_content = file.read(1024)  # Read first 1KB for MIME detection
        file.seek(0)  # Reset file pointer
        
        # Detect MIME type using python-magic
        detected_mime = magic.from_buffer(file_content, mime=True)
        
        # Verify detected MIME type matches allowed types for this extension
        allowed_mimes = allowed_types[file_extension]
        if detected_mime not in allowed_mimes:
            result['error'] = f'File content ({detected_mime}) does not match extension .{file_extension}'
            return result
        
        result['detected_mime_type'] = detected_mime
        
    except Exception as e:
        result['error'] = f'Could not validate file content: {e}'
        return result
    
    result['valid'] = True
    result['file_type'] = file_extension
    return result


def _generate_secure_filename(original_filename):
    """Generate a secure filename to prevent various attacks"""
    
    # Get file extension
    name, ext = os.path.splitext(original_filename)
    
    # Generate UUID-based filename
    secure_name = str(uuid.uuid4())
    
    # Ensure extension is lowercase and clean
    ext = ext.lower()
    ext = re.sub(r'[^a-zA-Z0-9.]', '', ext)
    
    return f"{secure_name}{ext}"


def _user_can_upload_to_project(user, project):
    """Check if user has permission to upload files to the project"""
    # Project manager can always upload
    if project.egis_project_manager == user.username:
        return True
    
    # Check if user is a project member
    try:
        member = ProjectMember.objects.get(project=project, user=user)
        return member.role in ['manager', 'editor', 'contributor']
    except ProjectMember.DoesNotExist:
        return False


def _user_can_upload_to_task(user, task):
    """Check if user has permission to upload files to the task"""
    # Task assignee can upload
    if task.assigned_to == user:
        return True
    
    # Task creator can upload
    if task.created_by == user:
        return True
    
    # Project permissions also apply
    if task.project:
        return _user_can_upload_to_project(user, task.project)
    
    return False




@require_http_methods(["GET"])
@login_required
def document_preview_htmx(request, document_id):
    """Generate enhanced file preview content with content reading and metadata"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions (basic implementation)
        if not (document.is_public or 
                document.uploaded_by == request.user or
                document.shares.filter(shared_with=request.user).exists()):
            raise PermissionDenied
        
        # Log view activity
        document.log_activity(
            user=request.user,
            activity_type='viewed',
            description=f"Previewed document: {document.name}"
        )
        
        # Determine preview type and handle content reading
        preview_type = 'unsupported'
        file_content = None
        preview_metadata = {}
        
        file_ext = document.file_type.lower() if document.file_type else ''
        
        # Image files
        if file_ext in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg']:
            preview_type = 'image'
            preview_metadata = {
                'supports_full_screen': True,
                'supports_zoom': True,
                'file_category': 'Image'
            }
            
        # PDF files
        elif file_ext == 'pdf':
            preview_type = 'pdf'
            preview_metadata = {
                'supports_full_screen': True,
                'supports_print': True,
                'file_category': 'Document'
            }
            
        # Text files - read content for preview
        elif file_ext in ['txt', 'md', 'py', 'js', 'html', 'css', 'json', 'xml', 'csv', 'log']:
            preview_type = 'text'
            preview_metadata = {
                'supports_search': True,
                'supports_copy': True,
                'file_category': 'Text',
                'syntax_highlighting': file_ext in ['py', 'js', 'html', 'css', 'json', 'xml']
            }
            
            # Try to read text content (limit to prevent memory issues)
            try:
                if document.file_path and hasattr(document, 'get_file_content'):
                    file_content = document.get_file_content(max_size=50000)  # 50KB limit
                elif document.file_path:
                    # Fallback: try to read from file path
                    if os.path.exists(document.file_path) and os.path.getsize(document.file_path) < 50000:
                        with open(document.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            file_content = f.read()
            except Exception:
                file_content = "Content could not be loaded for preview."
                
        # Video files
        elif file_ext in ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv']:
            preview_type = 'video'
            preview_metadata = {
                'supports_full_screen': True,
                'supports_controls': True,
                'file_category': 'Video'
            }
            
        # Audio files
        elif file_ext in ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']:
            preview_type = 'audio'
            preview_metadata = {
                'supports_controls': True,
                'file_category': 'Audio'
            }
            
        # Office documents
        elif file_ext in ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']:
            preview_type = 'office'
            preview_metadata = {
                'supports_download': True,
                'file_category': 'Office Document',
                'requires_external_app': True
            }
            
        # CAD files
        elif file_ext in ['dwg', 'dxf', 'dwf']:
            preview_type = 'cad'
            preview_metadata = {
                'supports_download': True,
                'file_category': 'CAD Drawing',
                'requires_external_app': True
            }
            
        # Code files
        elif file_ext in ['cpp', 'c', 'h', 'java', 'php', 'rb', 'go', 'rs', 'swift']:
            preview_type = 'text'  # Treat as text with syntax highlighting
            preview_metadata = {
                'supports_search': True,
                'supports_copy': True,
                'file_category': 'Source Code',
                'syntax_highlighting': True,
                'language': file_ext
            }
            
        # Archive files
        elif file_ext in ['zip', 'rar', '7z', 'tar', 'gz']:
            preview_type = 'archive'
            preview_metadata = {
                'supports_download': True,
                'file_category': 'Archive',
                'requires_extraction': True
            }
        
        context = {
            'document': document,
            'preview_type': preview_type,
            'file_content': file_content,
            'preview_metadata': preview_metadata,
            'can_edit': document.uploaded_by == request.user or request.user.is_staff,
            'can_share': True,  # Basic sharing permission
        }
        
        return render(request, 'components/documents/file_preview.html', context)
        
    except PermissionDenied:
        return HttpResponse('<div class="text-center py-8 text-red-600"><i data-lucide="lock" class="h-12 w-12 mx-auto mb-3"></i><p>Access denied</p></div>', status=403)
    except Exception as e:
        return HttpResponse(f'<div class="text-center py-8 text-red-600"><i data-lucide="alert-triangle" class="h-12 w-12 mx-auto mb-3"></i><p>Preview failed: {str(e)}</p></div>', status=400)


@require_http_methods(["POST"])
@login_required
def document_delete_htmx(request, document_id):
    """Delete document with HTMX confirmation"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not (document.uploaded_by == request.user or request.user.is_staff):
            raise PermissionDenied
        
        if request.POST.get('confirm') == 'true':
            # TODO: Delete actual file from storage
            document.delete()
            
            return HttpResponse(
                '<div class="alert alert-success">Document deleted successfully</div>'
            )
        else:
            # Return confirmation dialog
            return render(request, 'components/documents/delete_confirmation.html', {
                'document': document
            })
            
    except PermissionDenied:
        return HttpResponse('Access denied', status=403)
    except Exception as e:
        return HttpResponse(f'Delete failed: {str(e)}', status=400)


@require_http_methods(["POST"])
@login_required
def document_create_folder_htmx(request):
    """Create new folder via HTMX"""
    try:
        folder_name = request.POST.get('folder_name', '').strip()
        parent_folder = request.POST.get('parent_folder', '/')
        
        if not folder_name:
            return HttpResponse('Folder name required', status=400)
        
        # Sanitize folder name
        folder_name = re.sub(r'[^a-zA-Z0-9\-_\s]', '', folder_name)
        folder_name = folder_name.replace(' ', '_')
        
        new_folder_path = f"{parent_folder.rstrip('/')}/{folder_name}/"
        
        # Check if folder already exists by checking for documents in that path
        if Document.objects.filter(folder=new_folder_path).exists():
            return HttpResponse('Folder already exists', status=400)
        
        # Create a placeholder document to establish the folder
        # This is a common pattern when folders are virtual
        Document.objects.create(
            name='.folder_placeholder',
            description=f'Placeholder for folder {folder_name}',
            file_path=f"{new_folder_path}.folder_placeholder",
            file_size=0,
            file_type='folder',
            mime_type='application/x-folder',
            folder=new_folder_path,
            is_public=False,
            uploaded_by=request.user
        )
        
        return render(request, 'components/documents/folder_item.html', {
            'folder': {'name': folder_name, 'path': new_folder_path}
        })
        
    except Exception as e:
        return HttpResponse(f'Folder creation failed: {str(e)}', status=400)


@require_http_methods(["GET"])
@login_required
def document_search_htmx(request):
    """Search documents via HTMX"""
    query = request.GET.get('q', '').strip()
    folder = request.GET.get('folder', '/')
    
    if not query:
        return HttpResponse('')
    
    documents = Document.objects.filter(
        Q(name__icontains=query) |
        Q(description__icontains=query) |
        Q(tags__icontains=query),
        folder__startswith=folder
    ).select_related('uploaded_by').order_by('-created_at')[:20]
    
    return render(request, 'components/documents/search_results.html', {
        'documents': documents,
        'query': query
    })


@require_http_methods(["POST"])
@login_required 
def document_share_htmx(request, document_id):
    """Share document with users via HTMX"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not (document.uploaded_by == request.user or 
                document.shares.filter(shared_with=request.user, permission_level='admin').exists()):
            raise PermissionDenied
        
        if request.method == 'POST':
            user_id = request.POST.get('user_id')
            permission_level = request.POST.get('permission_level', 'view')
            
            try:
                user = User.objects.get(id=user_id)
                
                share, created = DocumentShare.objects.get_or_create(
                    document=document,
                    shared_with=user,
                    defaults={
                        'shared_by': request.user,
                        'permission_level': permission_level
                    }
                )
                
                if not created:
                    share.permission_level = permission_level
                    share.save()
                
                return render(request, 'components/documents/share_item.html', {
                    'share': share
                })
                
            except User.DoesNotExist:
                return HttpResponse('User not found', status=404)
        
        # Return share form
        return render(request, 'components/documents/share_form.html', {
            'document': document,
            'users': User.objects.filter(is_active=True).order_by('first_name', 'last_name'),
            'existing_shares': document.shares.select_related('shared_with').all()
        })
        
    except PermissionDenied:
        return HttpResponse('Access denied', status=403)
    except Exception as e:
        return HttpResponse(f'Sharing failed: {str(e)}', status=400)


# ========== DOCUMENT HOME AND WORKSPACE VIEWS ==========

class DocumentsHomeView(LoginRequiredMixin, TemplateView):
    """Document management home"""
    template_name = 'documents/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get user's recent documents
        context['recent_documents'] = Document.objects.filter(
            Q(uploaded_by=self.request.user) |
            Q(shares__shared_with=self.request.user)
        ).distinct().order_by('-created_at')[:10]

        # Get storage statistics
        user_docs = Document.objects.filter(uploaded_by=self.request.user)
        context['storage_used'] = sum(doc.file_size for doc in user_docs) / (1024*1024)  # MB

        return context


class DocumentWorkspaceView(LoginRequiredMixin, DetailView):
    """Comprehensive document workspace with collaboration features"""
    model = Document
    template_name = 'documents/document_workspace.html'
    context_object_name = 'document'
    pk_url_kwarg = 'document_id'

    def get_object(self, queryset=None):
        document = super().get_object(queryset)
        # Check permissions
        if not document.can_user_access(self.request.user):
            raise PermissionDenied("Access denied")
        return document

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        document = self.object
        
        # Get collaboration data
        context.update({
            'discussions': document.discussions.select_related('created_by').prefetch_related('participants').order_by('-updated_at')[:10],
            'recent_activities': document.get_recent_activities(limit=15),
            'active_reviews': document.get_active_review_processes(),
            'document_versions': document.versions.select_related('uploaded_by').order_by('-version_number')[:10],
            'shared_users': document.shares.select_related('shared_with', 'shared_by')[:20],
            'user_permission': document.get_user_permission_level(self.request.user),
            'can_edit': document.get_user_permission_level(self.request.user) in ['edit', 'admin'],
            'can_admin': document.get_user_permission_level(self.request.user) == 'admin',
            'comment_count': document.get_comment_count(),
            'discussion_count': document.get_discussion_count(),
            'activity_count': document.get_activity_count(),
        })
        
        return context


# ========== DOCUMENT DISCUSSION VIEWS ==========

@login_required
@require_http_methods(["GET"])
def document_discussion_list_htmx(request, document_id):
    """Load document discussions via HTMX"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        discussions = document.discussions.select_related('created_by').prefetch_related('participants').order_by('-updated_at')
        
        # Filter by status if specified
        status_filter = request.GET.get('status')
        if status_filter == 'resolved':
            discussions = discussions.filter(is_resolved=True)
        elif status_filter == 'active':
            discussions = discussions.filter(is_resolved=False)
        
        # Pagination
        page_num = request.GET.get('page', 1)
        paginator = Paginator(discussions, 10)
        page_obj = paginator.get_page(page_num)
        
        return render(request, 'components/documents/discussion_list.html', {
            'discussions': page_obj,
            'document': document,
            'user': request.user,
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)


@login_required
@require_http_methods(["POST"])
def document_discussion_create_htmx(request, document_id):
    """Create a new document discussion via HTMX"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions - user must have at least view access
        if not document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        priority = request.POST.get('priority', 'normal')
        document_version_id = request.POST.get('document_version')
        
        if not title:
            return HttpResponse('<div class="alert alert-danger">Discussion title is required</div>', status=400)
        
        # Get document version if specified
        document_version = None
        if document_version_id:
            try:
                document_version = DocumentVersion.objects.get(id=document_version_id, document=document)
            except DocumentVersion.DoesNotExist:
                pass
        
        # Create discussion
        discussion = DocumentDiscussion.objects.create(
            document=document,
            document_version=document_version,
            title=title,
            description=description,
            priority=priority,
            created_by=request.user
        )
        
        # Add creator as participant
        DocumentDiscussionParticipant.objects.create(
            discussion=discussion,
            user=request.user,
            role='creator'
        )
        
        # Log activity
        document.log_activity(
            user=request.user,
            activity_type='commented',
            description=f"Started discussion: {title}",
            document_version=document_version
        )
        
        # Return updated discussion list
        discussions = document.discussions.select_related('created_by').prefetch_related('participants').order_by('-updated_at')[:10]
        
        return render(request, 'components/documents/discussion_list.html', {
            'discussions': discussions,
            'document': document,
            'user': request.user,
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)


@login_required
@require_http_methods(["GET"])
def document_discussion_messages_htmx(request, discussion_id):
    """Load discussion messages via HTMX"""
    try:
        discussion = get_object_or_404(DocumentDiscussion, id=discussion_id)
        
        # Check permissions
        if not discussion.can_participate(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        messages = discussion.messages.filter(deleted_at__isnull=True).select_related('user').order_by('created_at')
        
        # Update last read timestamp for current user
        participant, created = DocumentDiscussionParticipant.objects.get_or_create(
            discussion=discussion,
            user=request.user,
            defaults={'role': 'participant'}
        )
        participant.last_read_at = timezone.now()
        participant.save(update_fields=['last_read_at'])
        
        return render(request, 'components/documents/discussion_messages.html', {
            'messages': messages,
            'discussion': discussion,
            'user': request.user,
        })
        
    except DocumentDiscussion.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Discussion not found</div>', status=404)


@login_required
@require_http_methods(["POST"])
def document_discussion_message_create_htmx(request, discussion_id):
    """Create a new message in a discussion via HTMX"""
    try:
        discussion = get_object_or_404(DocumentDiscussion, id=discussion_id)
        
        # Check permissions
        if not discussion.can_participate(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        content = request.POST.get('content', '').strip()
        parent_message_id = request.POST.get('parent_message')
        
        if not content:
            return HttpResponse('<div class="alert alert-danger">Message content is required</div>', status=400)
        
        # Get parent message if this is a reply
        parent_message = None
        if parent_message_id:
            try:
                parent_message = DocumentDiscussionMessage.objects.get(
                    id=parent_message_id, 
                    discussion=discussion,
                    deleted_at__isnull=True
                )
            except DocumentDiscussionMessage.DoesNotExist:
                pass
        
        # Create message
        DocumentDiscussionMessage.objects.create(
            discussion=discussion,
            user=request.user,
            content=content,
            parent_message=parent_message
        )
        
        # Update discussion timestamp
        discussion.updated_at = timezone.now()
        discussion.save(update_fields=['updated_at'])
        
        # Log activity
        discussion.document.log_activity(
            user=request.user,
            activity_type='commented',
            description=f"Commented in discussion: {discussion.title}",
            document_version=discussion.document_version
        )
        
        # Return updated message list
        messages = discussion.messages.filter(deleted_at__isnull=True).select_related('user').order_by('created_at')
        
        return render(request, 'components/documents/discussion_messages.html', {
            'messages': messages,
            'discussion': discussion,
            'user': request.user,
        })
        
    except DocumentDiscussion.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Discussion not found</div>', status=404)


@login_required
@require_http_methods(["POST"])
def document_discussion_resolve_htmx(request, discussion_id):
    """Mark a discussion as resolved via HTMX"""
    try:
        discussion = get_object_or_404(DocumentDiscussion, id=discussion_id)
        
        # Check permissions - user must be creator or have admin access to document
        user_permission = discussion.document.get_user_permission_level(request.user)
        if request.user != discussion.created_by and user_permission not in ['admin', 'edit']:
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        action = request.POST.get('action')
        
        if action == 'resolve':
            discussion.is_resolved = True
            discussion.resolved_by = request.user
            discussion.resolved_at = timezone.now()
            
            # Add system message
            DocumentDiscussionMessage.objects.create(
                discussion=discussion,
                user=request.user,
                content="Discussion marked as resolved",
                message_type='status_update',
                is_system_message=True
            )
            
        elif action == 'reopen':
            discussion.is_resolved = False
            discussion.resolved_by = None
            discussion.resolved_at = None
            
            # Add system message
            DocumentDiscussionMessage.objects.create(
                discussion=discussion,
                user=request.user,
                content="Discussion reopened",
                message_type='status_update',
                is_system_message=True
            )
        
        discussion.updated_at = timezone.now()
        discussion.save()
        
        # Log activity
        activity_type = 'commented'
        description = f"Discussion '{discussion.title}' {'resolved' if action == 'resolve' else 'reopened'}"
        discussion.document.log_activity(
            user=request.user,
            activity_type=activity_type,
            description=description,
            document_version=discussion.document_version
        )
        
        # Return updated discussion list
        discussions = discussion.document.discussions.select_related('created_by').prefetch_related('participants').order_by('-updated_at')[:10]
        
        return render(request, 'components/documents/discussion_list.html', {
            'discussions': discussions,
            'document': discussion.document,
            'user': request.user,
        })
        
    except DocumentDiscussion.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Discussion not found</div>', status=404)


# ========== DOCUMENT COLLABORATION AND ACTIVITY VIEWS ==========

@login_required
@require_http_methods(["GET"])
def document_activity_feed_htmx(request, document_id):
    """Load document activity feed via HTMX"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        activities = document.get_recent_activities(limit=20)
        
        return render(request, 'components/documents/activity_feed.html', {
            'activities': activities,
            'document': document,
            'user': request.user,
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)


@login_required
@require_http_methods(["GET"])
def document_collaboration_interface_htmx(request, document_id):
    """Load the full document collaboration interface via HTMX"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        # Get discussion count and recent discussions
        discussions = document.discussions.select_related('created_by').order_by('-updated_at')[:5]
        
        # Get recent activities
        activities = document.get_recent_activities(limit=10)
        
        # Get active review processes
        active_reviews = document.get_active_review_processes()
        
        context = {
            'document': document,
            'discussions': discussions,
            'activities': activities,
            'active_reviews': active_reviews,
            'user': request.user,
            'user_permission': document.get_user_permission_level(request.user),
        }
        
        return render(request, 'components/documents/collaboration_interface.html', context)
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)


# ========== DOCUMENT VERSION MANAGEMENT VIEWS ==========

@require_http_methods(["POST"])
@login_required
def document_version_upload_htmx(request, document_id):
    """Handle new version upload for existing document"""
    
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions - user must have edit access
        user_permission = document.get_user_permission_level(request.user)
        if user_permission not in ['edit', 'admin']:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)
        
        uploaded_file = request.FILES.get('version_file')
        change_notes = request.POST.get('change_notes', '')
        
        if not uploaded_file:
            return HttpResponse('<div class="alert alert-danger">No file provided</div>', status=400)
        
        # Validate file size
        if uploaded_file.size > 100 * 1024 * 1024:  # 100MB limit
            return HttpResponse('<div class="alert alert-danger">File size exceeds 100MB limit</div>', status=400)
        
        # Validate file type matches original (optional - you might want to allow different formats)
        document.get_file_extension()
        os.path.splitext(uploaded_file.name)[1].lower()
        
        # Import required modules
        
        # Create new version number
        latest_version = document.versions.order_by('-version_number').first()
        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        
        # Create upload directory for versions
        version_dir = os.path.join(settings.MEDIA_ROOT, 'documents', 'versions', str(document.id))
        os.makedirs(version_dir, exist_ok=True)
        
        # Generate version filename
        file_base, file_ext = os.path.splitext(uploaded_file.name)
        version_filename = f"{file_base}_v{new_version_number}{file_ext}"
        version_file_path = os.path.join(version_dir, version_filename)
        
        # Save the new version file
        with open(version_file_path, 'wb+') as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)
        
        # Create DocumentVersion record
        document_version = DocumentVersion.objects.create(
            document=document,
            version_number=new_version_number,
            file_path=version_file_path,
            file_size=uploaded_file.size,
            change_notes=change_notes,
            uploaded_by=request.user
        )
        
        # Update main document to point to latest version
        document.version = new_version_number
        document.file_path = version_file_path
        document.file_size = uploaded_file.size
        document.updated_at = timezone.now()
        document.save()
        
        # Process new version for thumbnails/previews
        try:
            processing_results = document_processor.process_document(document)
            if processing_results.get('errors'):
                logger.warning(f"Version processing warnings for {document.id} v{new_version_number}: {processing_results['errors']}")
        except Exception as e:
            logger.error(f"Version processing failed for {document.id} v{new_version_number}: {e}")
        
        # Log activity
        document.log_activity(
            user=request.user,
            activity_type='version_uploaded',
            description=f"Uploaded version {new_version_number}",
            document_version=document_version
        )
        
        # Return success response
        return render(request, 'components/documents/version_upload_success.html', {
            'document': document,
            'version': document_version,
            'change_notes': change_notes
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)
    except Exception as e:
        logger.error(f"Version upload failed for document {document_id}: {e}")
        return HttpResponse(f'<div class="alert alert-danger">Upload failed: {str(e)}</div>', status=500)


@login_required
@require_http_methods(["GET"])
def document_version_history_htmx(request, document_id):
    """Advanced version history with branching and diff capabilities"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if not document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        # Get all branches for this document
        branches = document.branches.filter(is_active=True).order_by('-created_at')
        
        # Get version history organized by branch
        branch_data = []
        for branch in branches:
            versions = branch.versions.select_related('uploaded_by').order_by('-version_number')[:10]
            branch_data.append({
                'branch': branch,
                'versions': versions,
                'latest_version': versions.first() if versions else None
            })
        
        # Also get main branch versions (versions without a branch)
        main_versions = document.versions.filter(branch__isnull=True).select_related('uploaded_by').order_by('-version_number')[:20]
        
        return render(request, 'components/documents/version_history.html', {
            'document': document,
            'branch_data': branch_data,
            'main_versions': main_versions,
            'can_edit': document.get_user_permission_level(request.user) in ['edit', 'admin']
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)
    except Exception as e:
        return HttpResponse(f'<div class="alert alert-danger">Error loading version history: {str(e)}</div>', status=500)


@login_required
@require_http_methods(["POST"])
def document_create_branch_htmx(request, document_id):
    """Create a new document branch for advanced version control"""
    try:
        document = get_object_or_404(Document, id=document_id)
        
        # Check permissions
        if document.get_user_permission_level(request.user) not in ['edit', 'admin']:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)
        
        branch_name = request.POST.get('branch_name', '').strip()
        description = request.POST.get('description', '').strip()
        base_version_id = request.POST.get('base_version')
        
        # Validation
        if not branch_name:
            return HttpResponse('<div class="alert alert-danger">Branch name is required</div>', status=400)
        
        if document.branches.filter(name=branch_name, is_active=True).exists():
            return HttpResponse('<div class="alert alert-danger">Branch name already exists</div>', status=400)
        
        # Get base version
        if base_version_id:
            base_version = get_object_or_404(DocumentVersion, id=base_version_id, document=document)
        else:
            # Use latest version as base
            base_version = document.versions.order_by('-version_number').first()
            if not base_version:
                return HttpResponse('<div class="alert alert-danger">No base version available</div>', status=400)
        
        # Create the branch
        branch = DocumentVersionBranch.objects.create(
            document=document,
            name=branch_name,
            description=description,
            base_version=base_version,
            created_by=request.user,
            is_active=True,
            is_default=False
        )
        
        # Log activity
        document.log_activity(
            user=request.user,
            activity_type='branch_created',
            description=f"Created branch '{branch_name}' from version {base_version.version_number}"
        )
        
        return render(request, 'components/documents/branch_created.html', {
            'document': document,
            'branch': branch,
            'base_version': base_version
        })
        
    except Document.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Document not found</div>', status=404)
    except Exception as e:
        return HttpResponse(f'<div class="alert alert-danger">Error creating branch: {str(e)}</div>', status=500)


@login_required
@require_http_methods(["GET"])
def document_version_diff_htmx(request, from_version_id, to_version_id):
    """Display detailed diff between two document versions"""
    try:
        from_version = get_object_or_404(DocumentVersion, id=from_version_id)
        to_version = get_object_or_404(DocumentVersion, id=to_version_id)
        
        # Check permissions
        if not from_version.document.can_user_access(request.user):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)
        
        # Check if these versions belong to the same document
        if from_version.document != to_version.document:
            return HttpResponse('<div class="alert alert-danger">Versions must belong to the same document</div>', status=400)
        
        # Get or create diff
        diff_obj, created = DocumentVersionDiff.objects.get_or_create(
            from_version=from_version,
            to_version=to_version,
            defaults={
                'diff_type': 'metadata',  # Start with metadata diff
                'diff_data': {},
                'similarity_score': 0.0,
                'changes_summary': {}
            }
        )
        
        # Calculate diff if not already computed or if older than document updates
        if created or not diff_obj.diff_data:
            diff_data = {
                'metadata_changes': {
                    'name': {
                        'from': from_version.document.name if from_version.version_number < to_version.version_number else to_version.document.name,
                        'to': to_version.document.name if from_version.version_number < to_version.version_number else from_version.document.name,
                        'changed': from_version.document.name != to_version.document.name
                    },
                    'size': {
                        'from': from_version.file_size,
                        'to': to_version.file_size,
                        'changed': from_version.file_size != to_version.file_size,
                        'size_diff': (to_version.file_size or 0) - (from_version.file_size or 0)
                    },
                    'upload_date': {
                        'from': from_version.created_at,
                        'to': to_version.created_at,
                        'time_diff': (to_version.created_at - from_version.created_at).total_seconds()
                    },
                    'uploaded_by': {
                        'from': from_version.uploaded_by.get_full_name() if from_version.uploaded_by else 'Unknown',
                        'to': to_version.uploaded_by.get_full_name() if to_version.uploaded_by else 'Unknown',
                        'changed': from_version.uploaded_by != to_version.uploaded_by
                    }
                },
                'version_info': {
                    'from_version': from_version.version_number,
                    'to_version': to_version.version_number,
                    'version_diff': to_version.version_number - from_version.version_number
                }
            }
            
            # Calculate similarity score based on metadata
            similarity_factors = []
            if not diff_data['metadata_changes']['name']['changed']:
                similarity_factors.append(0.3)
            if abs(diff_data['metadata_changes']['size']['size_diff']) < 1024:  # Less than 1KB difference
                similarity_factors.append(0.2)
            if diff_data['metadata_changes']['uploaded_by']['changed']:
                similarity_factors.append(0.1)
            
            similarity_score = sum(similarity_factors) if similarity_factors else 0.1
            
            # Update diff object
            diff_obj.diff_data = diff_data
            diff_obj.similarity_score = min(similarity_score, 1.0)
            diff_obj.changes_summary = {
                'total_changes': sum(1 for change in diff_data['metadata_changes'].values() if change.get('changed', False)),
                'size_change': diff_data['metadata_changes']['size']['size_diff'],
                'version_jump': diff_data['version_info']['version_diff']
            }
            diff_obj.save()
        
        return render(request, 'components/documents/version_diff_view.html', {
            'from_version': from_version,
            'to_version': to_version,
            'diff': diff_obj,
            'document': from_version.document
        })
        
    except (DocumentVersion.DoesNotExist, Document.DoesNotExist):
        return HttpResponse('<div class="alert alert-danger">Version not found</div>', status=404)
    except Exception as e:
        return HttpResponse(f'<div class="alert alert-danger">Error loading diff: {str(e)}</div>', status=500)


@login_required
@require_http_methods(["POST"])
def document_rollback_version_htmx(request, document_id, version_id):
    """Rollback document to a specific version"""
    
    try:
        document = get_object_or_404(Document, id=document_id)
        version = get_object_or_404(DocumentVersion, id=version_id, document=document)
        
        # Check permissions
        if document.get_user_permission_level(request.user) not in ['edit', 'admin']:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)
        
        # Check if document is locked
        active_locks = document.locks.filter(is_active=True)
        for lock in active_locks:
            if not lock.is_expired() and lock.locked_by != request.user:
                return HttpResponse(f'<div class="alert alert-warning">Document is locked by {lock.locked_by.get_full_name()}</div>', status=423)
        
        # Create new version based on rollback target
        new_version_number = (document.versions.aggregate(models.Max('version_number'))['version_number__max'] or 0) + 1
        
        rollback_version = DocumentVersion.objects.create(
            document=document,
            version_number=new_version_number,
            file_path=version.file_path,  # Use the same file
            file_size=version.file_size,
            change_notes=f"Rollback to version {version.version_number}",
            uploaded_by=request.user,
            parent_version=document.versions.order_by('-version_number').first(),  # Current latest becomes parent
            branch=version.branch  # Maintain branch context
        )
        
        # Update document to point to rollback version
        document.version = new_version_number
        document.file_path = version.file_path
        document.file_size = version.file_size
        document.updated_at = timezone.now()
        document.save()
        
        # Log activity
        document.log_activity(
            user=request.user,
            activity_type='version_rollback',
            description=f"Rolled back to version {version.version_number} (new version {new_version_number})",
            document_version=rollback_version
        )
        
        return render(request, 'components/documents/rollback_success.html', {
            'document': document,
            'rollback_version': rollback_version,
            'original_version': version
        })
        
    except (Document.DoesNotExist, DocumentVersion.DoesNotExist):
        return HttpResponse('<div class="alert alert-danger">Document or version not found</div>', status=404)
    except Exception as e:
        return HttpResponse(f'<div class="alert alert-danger">Rollback failed: {str(e)}</div>', status=500)


# ========== DOCUMENT SEARCH VIEWS ==========

@login_required
@require_http_methods(["GET"])
def document_advanced_search_htmx(request):
    """Advanced document search with full-text search and filters"""
    
    try:
        query = request.GET.get('q', '').strip()
        file_type = request.GET.get('file_type', '')
        project_id = request.GET.get('project_id', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        request.GET.get('size_min', '')
        request.GET.get('size_max', '')
        
        # Start with documents the user can access
        documents = Document.objects.filter(
            models.Q(is_public=True) | 
            models.Q(uploaded_by=request.user) |
            models.Q(shares__shared_with=request.user)
        ).distinct()
        
        search_results = []
        
        if query:
            # Full-text search using search index
            search_indexes = DocumentSearchIndex.objects.filter(
                models.Q(content_text__icontains=query) |
                models.Q(metadata_text__icontains=query) |
                models.Q(tags_text__icontains=query) |
                models.Q(document__name__icontains=query)
            ).select_related('document')
            
            for search_index in search_indexes:
                if search_index.document in documents:
                    # Calculate relevance score
                    score = 0
                    if query.lower() in search_index.document.name.lower():
                        score += 10
                    if query.lower() in (search_index.content_text or '').lower():
                        score += 5
                    if query.lower() in (search_index.metadata_text or '').lower():
                        score += 3
                    
                    search_results.append({
                        'document': search_index.document,
                        'score': score,
                        'search_index': search_index
                    })
        else:
            # No query, return recent documents
            for doc in documents.order_by('-updated_at')[:20]:
                search_results.append({
                    'document': doc,
                    'score': 1,
                    'search_index': None
                })
        
        # Apply filters
        if file_type:
            search_results = [r for r in search_results if r['document'].file_type == file_type]
        
        if project_id:
            search_results = [r for r in search_results if str(r['document'].project_id) == project_id]
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                search_results = [r for r in search_results if r['document'].created_at.date() >= date_from_obj]
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                search_results = [r for r in search_results if r['document'].created_at.date() <= date_to_obj]
            except ValueError:
                pass
        
        # Sort by relevance score
        search_results.sort(key=lambda x: x['score'], reverse=True)
        
        # Limit results
        search_results = search_results[:50]
        
        return render(request, 'components/documents/advanced_search_results.html', {
            'search_results': search_results,
            'query': query,
            'total_results': len(search_results),
            'filters': {
                'file_type': file_type,
                'project_id': project_id,
                'date_from': date_from,
                'date_to': date_to
            }
        })
        
    except Exception as e:
        return HttpResponse(f'<div class="alert alert-danger">Search failed: {str(e)}</div>', status=500)