"""
Profile Views

This module contains views related to user profiles, settings, and preferences.
"""


import logging
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
import secrets
from datetime import timedelta
from django.db.models.functions import ExtractWeekDay
from ..models import (
    Activity,
    APIToken,
    ConnectedAccount,
    Document,
    LoginHistory,
    Project,
    Task,
)
from .imports_base import *

logger = logging.getLogger(__name__)


class ProfileView(LoginRequiredMixin, TemplateView):
    template_name = 'profiles/profile.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user profile data
        user = self.request.user
        
        # Get user's projects
        projects = Project.objects.filter(
            Q(members=user) | 
            Q(created_by=user)
        ).distinct().order_by('-created_at')[:5]
        
        # Get user's recent activities
        activities = Activity.objects.filter(
            user=user
        ).order_by('-timestamp')[:10]
        
        # Get user's notifications
        notifications = Notification.objects.filter(
            user=user,
            is_read=False
        ).order_by('-created_at')[:5]
        
        # Get user's organization if any
        organization = None
        if hasattr(user, 'organization') and user.organization:
            organization = user.organization
            
        context.update({
            'user_profile': user,
            'projects': projects,
            'activities': activities,
            'notifications': notifications,
            'organization': organization,
            'project_count': Project.objects.filter(
                Q(members=user) | 
                Q(created_by=user)
            ).distinct().count(),
            'task_count': Task.objects.filter(
                Q(assigned_to=user) | 
                Q(created_by=user)
            ).count(),
            'document_count': Document.objects.filter(
                Q(created_by=user) | 
                Q(shared_with=user)
            ).distinct().count()
        })
        
        return context

@login_required
def profile_view(request, user_id=None):
    """View a user's profile."""
    if user_id:
        user_profile = get_object_or_404(User, pk=user_id)
        # Check if the current user has permission to view this profile
        if not (request.user.is_staff or user_profile == request.user or 
                Project.objects.filter(
                    Q(members=request.user) & Q(members=user_profile)
                ).exists()):
            return render(request, '403.html', status=403)
    else:
        user_profile = request.user
    
    # Get user's projects
    projects = Project.objects.filter(
        Q(members=user_profile) | 
        Q(created_by=user_profile)
    ).distinct().order_by('-created_at')[:5]
    
    # Get user's recent activities
    activities = Activity.objects.filter(
        user=user_profile
    ).order_by('-timestamp')[:10]
    
    # Get user's organization if any
    organization = None
    if hasattr(user_profile, 'organization') and user_profile.organization:
        organization = user_profile.organization
    
    context = {
        'user_profile': user_profile,
        'projects': projects,
        'activities': activities,
        'organization': organization,
        'project_count': Project.objects.filter(
            Q(members=user_profile) | 
            Q(created_by=user_profile)
        ).distinct().count(),
        'task_count': Task.objects.filter(
            Q(assigned_to=user_profile) | 
            Q(created_by=user_profile)
        ).count(),
        'document_count': Document.objects.filter(
            Q(created_by=user_profile) | 
            Q(shared_with=user_profile)
        ).distinct().count(),
        'is_own_profile': user_profile == request.user
    }
    
    return render(request, 'profiles/profile.html', context)

@login_required
def edit_profile(request):
    """Edit user's own profile."""
    user = request.user
    
    if request.method == 'POST':
        # Process form data
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        job_title = request.POST.get('job_title')
        bio = request.POST.get('bio')
        
        # Update user profile
        user.first_name = first_name
        user.last_name = last_name
        user.email = email
        user.phone = phone
        user.job_title = job_title
        user.bio = bio
        
        # Handle profile picture upload
        if 'profile_picture' in request.FILES:
            user.profile_picture = request.FILES['profile_picture']
        
        user.save()
        
        messages.success(request, 'Profile updated successfully.')
        return redirect('profile_view')
    
    context = {
        'user': user
    }
    
    return render(request, 'profiles/edit_profile.html', context)

@login_required
def change_password(request):
    """Change user's password."""
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        # Validate current password
        if not request.user.check_password(current_password):
            messages.error(request, 'Current password is incorrect.')
            return redirect('change_password')
        
        # Validate new password
        if new_password != confirm_password:
            messages.error(request, 'New passwords do not match.')
            return redirect('change_password')
        
        # Update password
        request.user.set_password(new_password)
        request.user.save()
        
        # Update session to prevent logout
        update_session_auth_hash(request, request.user)
        
        messages.success(request, 'Password changed successfully.')
        return redirect('profile_view')
    
    return render(request, 'profiles/change_password.html')

@login_required
def notification_settings(request):
    """Manage notification settings."""
    user = request.user
    
    if request.method == 'POST':
        # Process form data
        email_notifications = request.POST.get('email_notifications') == 'on'
        project_updates = request.POST.get('project_updates') == 'on'
        task_assignments = request.POST.get('task_assignments') == 'on'
        document_shares = request.POST.get('document_shares') == 'on'
        system_announcements = request.POST.get('system_announcements') == 'on'
        
        # Update notification settings
        user.notification_settings = {
            'email_notifications': email_notifications,
            'project_updates': project_updates,
            'task_assignments': task_assignments,
            'document_shares': document_shares,
            'system_announcements': system_announcements
        }
        
        user.save()
        
        messages.success(request, 'Notification settings updated successfully.')
        return redirect('profile_view')
    
    # Get current notification settings
    notification_settings = user.notification_settings if hasattr(user, 'notification_settings') else {
        'email_notifications': True,
        'project_updates': True,
        'task_assignments': True,
        'document_shares': True,
        'system_announcements': True
    }
    
    context = {
        'notification_settings': notification_settings
    }
    
    return render(request, 'profiles/notification_settings.html', context)

@login_required
def user_preferences(request):
    """Manage user preferences."""
    user = request.user
    
    if request.method == 'POST':
        # Process form data
        theme = request.POST.get('theme', 'light')
        language = request.POST.get('language', 'en')
        timezone_name = request.POST.get('timezone', 'UTC')
        date_format = request.POST.get('date_format', 'MM/DD/YYYY')
        
        # Update user preferences
        user.preferences = {
            'theme': theme,
            'language': language,
            'timezone': timezone_name,
            'date_format': date_format
        }
        
        user.save()
        
        messages.success(request, 'Preferences updated successfully.')
        return redirect('profile_view')
    
    # Get current user preferences
    preferences = user.preferences if hasattr(user, 'preferences') else {
        'theme': 'light',
        'language': 'en',
        'timezone': 'UTC',
        'date_format': 'MM/DD/YYYY'
    }
    
    context = {
        'preferences': preferences,
        'available_themes': [
            ('light', 'Light'),
            ('dark', 'Dark'),
            ('blue', 'Blue'),
            ('high-contrast', 'High Contrast')
        ],
        'available_languages': [
            ('en', 'English'),
            ('es', 'Spanish'),
            ('fr', 'French'),
            ('de', 'German')
        ],
        'available_timezones': [
            ('UTC', 'UTC'),
            ('US/Eastern', 'US Eastern'),
            ('US/Central', 'US Central'),
            ('US/Mountain', 'US Mountain'),
            ('US/Pacific', 'US Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris')
        ],
        'available_date_formats': [
            ('MM/DD/YYYY', 'MM/DD/YYYY'),
            ('DD/MM/YYYY', 'DD/MM/YYYY'),
            ('YYYY-MM-DD', 'YYYY-MM-DD')
        ]
    }
    
    return render(request, 'profiles/user_preferences.html', context)

@login_required
def api_tokens(request):
    """Manage API tokens."""
    user = request.user
    
    # Get user's API tokens
    tokens = APIToken.objects.filter(user=user).order_by('-created_at')
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'create':
            # Create new token
            name = request.POST.get('token_name')
            expiry_days = int(request.POST.get('expiry_days', 30))
            
            if not name:
                messages.error(request, 'Token name is required.')
                return redirect('api_tokens')
            
            # Generate token
            token_value = secrets.token_hex(32)
            expiry_date = timezone.now() + timedelta(days=expiry_days)
            
            # Save token
            token = APIToken.objects.create(
                user=user,
                name=name,
                token=token_value,
                expires_at=expiry_date
            )
            
            messages.success(request, f'API token "{name}" created successfully.')
            
            # Show token value only once
            context = {
                'tokens': tokens,
                'new_token': token,
                'token_value': token_value
            }
            
            return render(request, 'profiles/api_tokens.html', context)
            
        elif action == 'revoke':
            # Revoke token
            token_id = request.POST.get('token_id')
            
            try:
                token = APIToken.objects.get(id=token_id, user=user)
                token_name = token.name
                token.delete()
                
                messages.success(request, f'API token "{token_name}" revoked successfully.')
            except APIToken.DoesNotExist:
                messages.error(request, 'Token not found.')
            
            return redirect('api_tokens')
    
    context = {
        'tokens': tokens
    }
    
    return render(request, 'profiles/api_tokens.html', context)

@login_required
def connected_accounts(request):
    """Manage connected accounts."""
    user = request.user
    
    # Get user's connected accounts
    connected_accounts = ConnectedAccount.objects.filter(user=user)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'disconnect':
            # Disconnect account
            account_id = request.POST.get('account_id')
            
            try:
                account = ConnectedAccount.objects.get(id=account_id, user=user)
                provider_name = account.provider
                account.delete()
                
                messages.success(request, f'{provider_name} account disconnected successfully.')
            except ConnectedAccount.DoesNotExist:
                messages.error(request, 'Connected account not found.')
            
            return redirect('connected_accounts')
    
    context = {
        'connected_accounts': connected_accounts,
        'available_providers': [
            'Google',
            'Microsoft',
            'GitHub',
            'LinkedIn'
        ]
    }
    
    return render(request, 'profiles/connected_accounts.html', context)

@login_required
def security_settings(request):
    """Manage security settings."""
    user = request.user
    
    # Check if MFA is enabled
    mfa_enabled = MFASession.objects.filter(user=user, is_active=True).exists()
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'enable_mfa':
            # Redirect to MFA setup
            return redirect('mfa_setup')
            
        elif action == 'disable_mfa':
            # Disable MFA
            MFASession.objects.filter(user=user).update(is_active=False)
            messages.success(request, 'Multi-factor authentication disabled successfully.')
            
            return redirect('security_settings')
            
        elif action == 'update_security_questions':
            # Update security questions
            question1 = request.POST.get('security_question1')
            answer1 = request.POST.get('security_answer1')
            question2 = request.POST.get('security_question2')
            answer2 = request.POST.get('security_answer2')
            
            if not all([question1, answer1, question2, answer2]):
                messages.error(request, 'All security questions and answers are required.')
                return redirect('security_settings')
            
            # Update security questions
            user.security_questions = {
                'question1': question1,
                'answer1': answer1,
                'question2': question2,
                'answer2': answer2
            }
            
            user.save()
            
            messages.success(request, 'Security questions updated successfully.')
            return redirect('security_settings')
    
    # Get current security questions
    security_questions = user.security_questions if hasattr(user, 'security_questions') else {
        'question1': '',
        'answer1': '',
        'question2': '',
        'answer2': ''
    }
    
    context = {
        'mfa_enabled': mfa_enabled,
        'security_questions': security_questions,
        'available_questions': [
            'What was the name of your first pet?',
            'What was the name of your first school?',
            'What was your childhood nickname?',
            'In what city were you born?',
            'What is your mother\'s maiden name?',
            'What was the make of your first car?',
            'What is your favorite movie?',
            'What is your favorite book?'
        ]
    }
    
    return render(request, 'profiles/security_settings.html', context)

@login_required
def account_activity(request):
    """View account activity."""
    user = request.user
    
    # Get user's login history
    login_history = LoginHistory.objects.filter(user=user).order_by('-timestamp')
    
    # Paginate results
    paginator = Paginator(login_history, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj
    }
    
    return render(request, 'profiles/account_activity.html', context)

@login_required
def download_personal_data(request):
    """Download personal data."""
    user = request.user
    
    if request.method == 'POST':
        # Create data export
        data_export = {
            'user': {
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None
            },
            'projects': [],
            'tasks': [],
            'comments': [],
            'documents': [],
            'activities': []
        }
        
        # Get user's projects
        projects = Project.objects.filter(
            Q(members=user) | 
            Q(created_by=user)
        ).distinct()
        
        for project in projects:
            data_export['projects'].append({
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'created_at': project.created_at.isoformat(),
                'status': project.status
            })
        
        # Get user's tasks
        tasks = Task.objects.filter(
            Q(assigned_to=user) | 
            Q(created_by=user)
        )
        
        for task in tasks:
            data_export['tasks'].append({
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'status': task.status,
                'priority': task.priority,
                'created_at': task.created_at.isoformat(),
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'project': task.project.name if task.project else None
            })
        
        # Get user's comments
        comments = Comment.objects.filter(user=user)
        
        for comment in comments:
            data_export['comments'].append({
                'id': comment.id,
                'text': comment.text,
                'created_at': comment.created_at.isoformat(),
                'target_type': comment.target_type,
                'target_id': comment.target_id
            })
        
        # Get user's documents
        documents = Document.objects.filter(
            Q(created_by=user) | 
            Q(shared_with=user)
        ).distinct()
        
        for document in documents:
            data_export['documents'].append({
                'id': document.id,
                'name': document.name,
                'description': document.description,
                'created_at': document.created_at.isoformat(),
                'file_type': document.file_type,
                'project': document.project.name if document.project else None
            })
        
        # Get user's activities
        activities = Activity.objects.filter(user=user)
        
        for activity in activities:
            data_export['activities'].append({
                'id': activity.id,
                'action': activity.action,
                'target_model': activity.target_model,
                'target_id': activity.target_id,
                'target_name': activity.target_name,
                'description': activity.description,
                'timestamp': activity.timestamp.isoformat(),
                'project': activity.project.name if activity.project else None
            })
        
        # Create JSON response
        response = HttpResponse(
            json.dumps(data_export, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="{user.username}_data_export.json"'
        
        # Log data export
        Activity.objects.create(
            user=user,
            action='exported',
            target_model='PersonalData',
            target_id=user.id,
            target_name=user.username,
            description='Exported personal data'
        )
        
        return response
    
    return render(request, 'profiles/download_personal_data.html')

@login_required
def delete_account(request):
    """Delete user account."""
    user = request.user
    
    if request.method == 'POST':
        password = request.POST.get('password')
        
        # Validate password
        if not user.check_password(password):
            messages.error(request, 'Password is incorrect.')
            return redirect('delete_account')
        
        # Log the account deletion
        logger.warning(f"User {user.username} (ID: {user.id}) has deleted their account.")
        
        # Logout user
        logout(request)
        
        # Delete user account
        user.delete()
        
        messages.success(request, 'Your account has been deleted successfully.')
        return redirect('login')
    
    return render(request, 'profiles/delete_account.html')

@login_required
def htmx_profile_projects(request):
    """HTMX endpoint for user's projects."""
    user = request.user
    
    # Get user's projects
    projects = Project.objects.filter(
        Q(members=user) | 
        Q(created_by=user)
    ).distinct().order_by('-created_at')
    
    # Apply filters if provided
    status_filter = request.GET.get('status')
    if status_filter:
        projects = projects.filter(status=status_filter)
    
    search_query = request.GET.get('search')
    if search_query:
        projects = projects.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(projects, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query
    }
    
    return render(request, 'profiles/partials/profile_projects.html', context)

@login_required
def htmx_profile_tasks(request):
    """HTMX endpoint for user's tasks."""
    user = request.user
    
    # Get user's tasks
    tasks = Task.objects.filter(
        Q(assigned_to=user) | 
        Q(created_by=user)
    ).order_by('-created_at')
    
    # Apply filters if provided
    status_filter = request.GET.get('status')
    if status_filter:
        tasks = tasks.filter(status=status_filter)
    
    priority_filter = request.GET.get('priority')
    if priority_filter:
        tasks = tasks.filter(priority=priority_filter)
    
    search_query = request.GET.get('search')
    if search_query:
        tasks = tasks.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(tasks, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'search_query': search_query
    }
    
    return render(request, 'profiles/partials/profile_tasks.html', context)

@login_required
def htmx_profile_documents(request):
    """HTMX endpoint for user's documents."""
    user = request.user
    
    # Get user's documents
    documents = Document.objects.filter(
        Q(created_by=user) | 
        Q(shared_with=user)
    ).distinct().order_by('-created_at')
    
    # Apply filters if provided
    file_type_filter = request.GET.get('file_type')
    if file_type_filter:
        documents = documents.filter(file_type=file_type_filter)
    
    search_query = request.GET.get('search')
    if search_query:
        documents = documents.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(documents, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'file_type_filter': file_type_filter,
        'search_query': search_query
    }
    
    return render(request, 'profiles/partials/profile_documents.html', context)

@login_required
def htmx_profile_activities(request):
    """HTMX endpoint for user's activities."""
    user = request.user
    
    # Get user's activities
    activities = Activity.objects.filter(user=user).order_by('-timestamp')
    
    # Apply filters if provided
    action_filter = request.GET.get('action')
    if action_filter:
        activities = activities.filter(action=action_filter)
    
    target_model_filter = request.GET.get('target_model')
    if target_model_filter:
        activities = activities.filter(target_model=target_model_filter)
    
    date_filter = request.GET.get('date')
    if date_filter:
        try:
            date_obj = datetime.strptime(date_filter, '%Y-%m-%d').date()
            activities = activities.filter(
                timestamp__date=date_obj
            )
        except ValueError:
            pass
    
    # Paginate results
    paginator = Paginator(activities, 20)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'action_filter': action_filter,
        'target_model_filter': target_model_filter,
        'date_filter': date_filter
    }
    
    return render(request, 'profiles/partials/profile_activities.html', context)

@login_required
def htmx_profile_notifications(request):
    """HTMX endpoint for user's notifications."""
    user = request.user
    
    # Get user's notifications
    notifications = Notification.objects.filter(user=user).order_by('-created_at')
    
    # Apply filters if provided
    is_read_filter = request.GET.get('is_read')
    if is_read_filter == 'read':
        notifications = notifications.filter(is_read=True)
    elif is_read_filter == 'unread':
        notifications = notifications.filter(is_read=False)
    
    notification_type_filter = request.GET.get('notification_type')
    if notification_type_filter:
        notifications = notifications.filter(notification_type=notification_type_filter)
    
    # Paginate results
    paginator = Paginator(notifications, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'is_read_filter': is_read_filter,
        'notification_type_filter': notification_type_filter,
        'unread_count': Notification.objects.filter(user=user, is_read=False).count()
    }
    
    return render(request, 'profiles/partials/profile_notifications.html', context)

@login_required
def htmx_mark_notification_read(request, notification_id):
    """HTMX endpoint to mark a notification as read."""
    notification = get_object_or_404(Notification, pk=notification_id, user=request.user)
    
    # Mark notification as read
    notification.is_read = True
    notification.read_at = timezone.now()
    notification.save()
    
    return HttpResponse(
        '<span class="badge bg-secondary">Read</span>'
    )

@login_required
def htmx_mark_all_notifications_read(request):
    """HTMX endpoint to mark all notifications as read."""
    # Mark all notifications as read
    Notification.objects.filter(user=request.user, is_read=False).update(
        is_read=True,
        read_at=timezone.now()
    )
    
    # Get updated notifications
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')
    
    # Paginate results
    paginator = Paginator(notifications, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'unread_count': 0
    }
    
    return render(request, 'profiles/partials/profile_notifications.html', context)

@login_required
def htmx_profile_stats(request):
    """HTMX endpoint for user's statistics."""
    user = request.user
    
    # Get user's statistics
    project_count = Project.objects.filter(
        Q(members=user) | 
        Q(created_by=user)
    ).distinct().count()
    
    task_count = Task.objects.filter(
        Q(assigned_to=user) | 
        Q(created_by=user)
    ).count()
    
    completed_task_count = Task.objects.filter(
        Q(assigned_to=user) | 
        Q(created_by=user),
        status='completed'
    ).count()
    
    document_count = Document.objects.filter(
        Q(created_by=user) | 
        Q(shared_with=user)
    ).distinct().count()
    
    comment_count = Comment.objects.filter(user=user).count()
    
    # Get task completion rate
    task_completion_rate = 0
    if task_count > 0:
        task_completion_rate = (completed_task_count / task_count) * 100
    
    # Get activity by day of week
    activities_by_day = Activity.objects.filter(
        user=user,
        timestamp__gte=timezone.now() - timedelta(days=30)
    ).annotate(
        day_of_week=ExtractWeekDay('timestamp')
    ).values('day_of_week').annotate(
        count=Count('id')
    ).order_by('day_of_week')
    
    # Convert to list for easier handling in template
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    activity_data = [0] * 7
    
    for item in activities_by_day:
        # Django's ExtractWeekDay returns 1-7 where 1=Sunday, 2=Monday, etc.
        # Convert to 0-6 where 0=Monday, 1=Tuesday, etc.
        day_index = (item['day_of_week'] - 2) % 7
        activity_data[day_index] = item['count']
    
    context = {
        'project_count': project_count,
        'task_count': task_count,
        'completed_task_count': completed_task_count,
        'document_count': document_count,
        'comment_count': comment_count,
        'task_completion_rate': task_completion_rate,
        'days_of_week': days_of_week,
        'activity_data': activity_data
    }
    
    return render(request, 'profiles/partials/profile_stats.html', context)

@login_required
def htmx_profile_update_theme(request):
    """HTMX endpoint to update user's theme preference."""
    user = request.user
    
    if request.method == 'POST':
        theme = request.POST.get('theme', 'light')
        
        # Update user preferences
        preferences = user.preferences if hasattr(user, 'preferences') else {}
        preferences['theme'] = theme
        user.preferences = preferences
        user.save()
        
        return HttpResponse(
            f'<div class="alert alert-success">Theme updated to {theme}</div>'
        )
    
    return HttpResponse(status=400)

@login_required
def htmx_profile_update_language(request):
    """HTMX endpoint to update user's language preference."""
    user = request.user
    
    if request.method == 'POST':
        language = request.POST.get('language', 'en')
        
        # Update user preferences
        preferences = user.preferences if hasattr(user, 'preferences') else {}
        preferences['language'] = language
        user.preferences = preferences
        user.save()
        
        # Get language name
        language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German'
        }
        
        language_name = language_names.get(language, language)
        
        return HttpResponse(
            f'<div class="alert alert-success">Language updated to {language_name}</div>'
        )
    
    return HttpResponse(status=400)

@login_required
def htmx_profile_picture_upload(request):
    """HTMX endpoint to upload profile picture."""
    user = request.user
    
    if request.method == 'POST' and 'profile_picture' in request.FILES:
        # Handle profile picture upload
        user.profile_picture = request.FILES['profile_picture']
        user.save()
        
        # Return updated profile picture
        return render(request, 'profiles/partials/profile_picture.html', {
            'user_profile': user
        })
    
    return HttpResponse(status=400)

@login_required
def htmx_profile_picture_remove(request):
    """HTMX endpoint to remove profile picture."""
    user = request.user
    
    if request.method == 'POST':
        # Remove profile picture
        user.profile_picture = None
        user.save()
        
        # Return default profile picture
        return render(request, 'profiles/partials/profile_picture.html', {
            'user_profile': user
        })
    
    return HttpResponse(status=400)


@login_required
def notifications(request):
    """Display the notifications page with filtering and pagination."""
    user = request.user
    
    # Get filter parameters
    notification_type = request.GET.get("type", "")
    priority = request.GET.get("priority", "")
    status = request.GET.get("status", "")
    
    # Base queryset
    notifications_qs = user.notifications.all()
    
    # Apply filters
    if notification_type and notification_type != "all":
        notifications_qs = notifications_qs.filter(notification_type=notification_type)
    
    if priority and priority != "all":
        notifications_qs = notifications_qs.filter(priority=priority)
    
    if status == "unread":
        notifications_qs = notifications_qs.filter(is_read=False)
    elif status == "read":
        notifications_qs = notifications_qs.filter(is_read=True)
    
    # Order by creation date
    notifications_qs = notifications_qs.order_by("-created_at")
    
    # Pagination
    paginator = Paginator(notifications_qs, 20)  # 20 notifications per page
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)
    
    # Get available notification types
    notification_types = [
        "message", "mention", "comment", "task_assigned", "task_due",
        "document_shared", "project_updated", "conflict_detected",
        "reminder", "approval_needed", "system"
    ]
    
    context = {
        "notifications": page_obj,
        "notification_types": notification_types,
        "current_type": notification_type,
        "current_priority": priority,
        "current_status": status,
        "unread_count": user.notifications.filter(is_read=False).count(),
        "page_obj": page_obj,
        "is_paginated": page_obj.has_other_pages(),
    }
    
    # Handle HTMX requests
    if request.headers.get("HX-Request"):
        return render(request, "components/notifications/notification_list_items.html", context)
    
    return render(request, "CLEAR/notifications/list.html", context)


# ========== ADDITIONAL PROFILE VIEWS FOR HTMX ==========

@login_required
def my_profile(request):
    """Display the user's profile page."""
    user = request.user
    
    # Get user statistics
    projects = Project.objects.filter(
        Q(members=user) | Q(created_by=user)
    ).distinct()
    
    tasks = Task.objects.filter(
        Q(assigned_to=user) | Q(created_by=user)
    )
    
    documents = Document.objects.filter(
        Q(uploaded_by=user) | Q(shared_with=user)
    ).distinct()
    
    # Calculate completion rates
    completed_tasks = tasks.filter(status='completed').count()
    total_tasks = tasks.count()
    completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    context = {
        'user': user,
        'project_count': projects.count(),
        'task_count': total_tasks,
        'document_count': documents.count(),
        'completion_rate': completion_rate,
        'recent_projects': projects.order_by('-updated_at')[:3],
        'recent_activities': Activity.objects.filter(user=user).order_by('-timestamp')[:10]
    }
    
    return render(request, 'CLEAR/profile/my_profile.html', context)


@login_required
def notebook(request):
    """Display the user's notebook page."""
    user = request.user
    
    # TODO: NotebookEntry model needs to be created
    # For now, use empty data
    notebook_entries = []
    categories = ['General', 'Ideas', 'Meeting Notes', 'To-Do', 'References']
    unique_tags = []
    
    context = {
        'user': user,
        'notebook_entries': notebook_entries,
        'categories': categories,
        'tags': unique_tags,
        'selected_category': request.GET.get('category', ''),
        'selected_tag': request.GET.get('tag', ''),
        'search_query': request.GET.get('search', '')
    }
    
    return render(request, 'CLEAR/profile/notebook.html', context)


@login_required
def settings(request):
    """Display the user's settings page."""
    user = request.user
    
    # Get user preferences
    preferences = getattr(user, 'preferences', {})
    
    # Get notification settings
    notification_settings = getattr(user, 'notification_settings', {
        'email_notifications': True,
        'push_notifications': True,
        'sms_notifications': False,
        'project_updates': True,
        'task_assignments': True,
        'message_notifications': True,
        'whisper_notifications': True,
        'mention_notifications': True,
        'document_shares': True,
        'comment_replies': True
    })
    
    # Get security settings
    has_mfa = hasattr(user, 'mfa_enabled') and user.mfa_enabled
    
    # Get connected accounts (simplified for now)
    connected_accounts = []
    
    # Get API tokens (TODO: APIToken model needs to be created)
    api_tokens = []
    
    context = {
        'user': user,
        'preferences': preferences,
        'notification_settings': notification_settings,
        'has_mfa': has_mfa,
        'connected_accounts': connected_accounts,
        'api_tokens': api_tokens,
        'themes': [
            {'value': 'light', 'label': 'Light', 'icon': 'bi-sun'},
            {'value': 'dark', 'label': 'Dark', 'icon': 'bi-moon'},
            {'value': 'auto', 'label': 'Auto', 'icon': 'bi-circle-half'}
        ],
        'languages': [
            {'value': 'en', 'label': 'English'},
            {'value': 'es', 'label': 'Español'},
            {'value': 'fr', 'label': 'Français'},
            {'value': 'de', 'label': 'Deutsch'}
        ]
    }
    
    return render(request, 'CLEAR/profile/settings.html', context)


@login_required
@require_http_methods(["GET"])
def profile_activities(request):
    """HTMX endpoint for profile activities list."""
    user = request.user
    page = request.GET.get('page', 1)
    
    # Get user activities
    activities = Activity.objects.filter(user=user).order_by('-timestamp')
    
    # Add additional activity details
    for activity in activities:
        activity.icon = get_activity_icon(activity.action)
        activity.color = get_activity_color(activity.action)
        
        # Try to get the target URL
        try:
            if activity.target_model == 'Project' and activity.target_id:
                activity.target_url = f"/projects/{activity.target_id}/"
            elif activity.target_model == 'Task' and activity.target_id:
                activity.target_url = f"/tasks/{activity.target_id}/"
            elif activity.target_model == 'Document' and activity.target_id:
                activity.target_url = f"/documents/{activity.target_id}/"
            else:
                activity.target_url = "#"
        except Exception:
            activity.target_url = "#"
    
    # Paginate
    paginator = Paginator(activities, 15)
    page_obj = paginator.get_page(page)
    
    context = {
        'activities': page_obj.object_list,
        'page_obj': page_obj,
        'user': user
    }
    
    return render(request, 'profiles/partials/profile_activities.html', context)


@login_required
@require_http_methods(["GET"])
def profile_projects(request):
    """HTMX endpoint for profile projects list."""
    user = request.user
    page = request.GET.get('page', 1)
    
    # Get user projects
    projects = Project.objects.filter(
        Q(members=user) | Q(created_by=user)
    ).distinct().order_by('-updated_at')
    
    # Add additional project details
    for project in projects:
        # Calculate progress
        if hasattr(project, 'tasks'):
            total_tasks = project.tasks.count()
            completed_tasks = project.tasks.filter(status='completed').count()
            project.progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        else:
            project.progress = 0
        
        # Get status color
        status_colors = {
            'planning': 'secondary',
            'active': 'egis-green',
            'on_hold': 'warning',
            'completed': 'success',
            'cancelled': 'danger'
        }
        project.status_color = status_colors.get(project.status, 'secondary')
        
        # Task completion rate
        project.task_completion_rate = int(project.progress)
    
    # Paginate
    paginator = Paginator(projects, 6)
    page_obj = paginator.get_page(page)
    
    context = {
        'projects': page_obj.object_list,
        'page_obj': page_obj,
        'user': user
    }
    
    return render(request, 'profiles/partials/profile_projects.html', context)


@login_required
@require_http_methods(["POST"])
def update_profile_htmx(request):
    """HTMX endpoint to update profile information."""
    user = request.user
    
    # Update basic information
    user.first_name = request.POST.get('first_name', user.first_name)
    user.last_name = request.POST.get('last_name', user.last_name)
    user.email = request.POST.get('email', user.email)
    
    # Update additional fields if they exist
    if hasattr(user, 'phone'):
        user.phone = request.POST.get('phone', '')
    if hasattr(user, 'job_title'):
        user.job_title = request.POST.get('job_title', '')
    if hasattr(user, 'bio'):
        user.bio = request.POST.get('bio', '')
    if hasattr(user, 'location'):
        user.location = request.POST.get('location', '')
    
    user.save()
    
    return HttpResponse(
        '<div class="alert alert-success alert-dismissible fade show" role="alert">'
        '<i class="bi bi-check-circle me-2"></i>Profile updated successfully!'
        '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>'
        '</div>'
    )


@login_required
@require_http_methods(["POST"])
def notebook_create_htmx(request):
    """HTMX endpoint to create a new notebook entry."""
    # TODO: NotebookEntry model needs to be created
    return HttpResponse(
        '<div class="alert alert-info">Notebook feature is coming soon!</div>'
    )


@login_required
@require_http_methods(["POST"])
def notebook_update_htmx(request, entry_id):
    """HTMX endpoint to update a notebook entry."""
    # TODO: NotebookEntry model needs to be created
    return HttpResponse(
        '<div class="alert alert-info">Notebook feature is coming soon!</div>'
    )


@login_required
@require_http_methods(["DELETE", "POST"])
def notebook_delete_htmx(request, entry_id):
    """HTMX endpoint to delete a notebook entry."""
    # TODO: NotebookEntry model needs to be created
    return HttpResponse('')


# Helper functions
def get_activity_icon(action):
    """Get icon for activity action."""
    icons = {
        'created': 'bi-plus-circle',
        'updated': 'bi-pencil',
        'deleted': 'bi-trash',
        'completed': 'bi-check-circle',
        'commented': 'bi-chat-dots',
        'shared': 'bi-share',
        'assigned': 'bi-person-plus',
        'uploaded': 'bi-cloud-upload',
        'downloaded': 'bi-cloud-download',
        'viewed': 'bi-eye',
        'approved': 'bi-check2-square',
        'rejected': 'bi-x-square'
    }
    return icons.get(action, 'bi-activity')


def get_activity_color(action):
    """Get color class for activity action."""
    colors = {
        'created': 'egis-green',
        'updated': 'egis-blue',
        'deleted': 'danger',
        'completed': 'success',
        'commented': 'info',
        'shared': 'egis-teal',
        'assigned': 'primary',
        'uploaded': 'secondary',
        'downloaded': 'secondary',
        'viewed': 'secondary',
        'approved': 'success',
        'rejected': 'danger'
    }
    return colors.get(action, 'secondary')