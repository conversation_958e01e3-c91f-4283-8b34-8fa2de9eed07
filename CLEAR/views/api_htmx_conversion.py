"""
HTMX conversion views for JSON API endpoints.

This module contains HTMX equivalents of the JSON API endpoints,
returning HTML fragments instead of JSON responses.

"""

import logging
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from datetime import timedelta
from ..models import (
    ChatMessage,
    Comment,
    Conflict,
    Document,
    Notification,
    Project,
    Task,
    User,
    Utility,
)

logger = logging.getLogger(__name__)


# ========== USER MANAGEMENT ==========

@login_required
def user_profile_htmx(request, user_id=None):
    """Get user profile information as HTML fragment."""
    if user_id:
        user = get_object_or_404(User, id=user_id)
    else:
        user = request.user
    
    context = {
        'user': user,
        'is_own_profile': user == request.user
    }
    
    return render(request, 'components/users/user_profile.html', context)


@login_required
def user_list_htmx(request):
    """List users with filtering and pagination."""
    queryset = User.objects.filter(is_active=True)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(username__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(email__icontains=search)
        )
    
    # Role filter
    role = request.GET.get('role')
    if role:
        queryset = queryset.filter(role=role)
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset.order_by('last_name', 'first_name'), 25)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'users': page_obj,
        'total_count': paginator.count,
        'search': search,
        'role': role
    }
    
    return render(request, 'components/users/user_list.html', context)


@login_required
@require_http_methods(["POST"])
def user_update_htmx(request, user_id):
    """Update user information."""
    user = get_object_or_404(User, id=user_id)
    
    # Check permissions
    if user != request.user and not request.user.is_admin:
        return HttpResponse("Unauthorized", status=403)
    
    # Update user fields
    user.first_name = request.POST.get('first_name', user.first_name)
    user.last_name = request.POST.get('last_name', user.last_name)
    user.email = request.POST.get('email', user.email)
    user.phone = request.POST.get('phone', user.phone)
    user.unit_preference = request.POST.get('unit_preference', user.unit_preference)
    
    # Admin-only fields
    if request.user.is_admin:
        user.role = request.POST.get('role', user.role)
        user.is_active = request.POST.get('is_active') == 'true'
    
    user.save()
    
    messages.success(request, f"User {user.username} updated successfully.")
    
    return render(request, 'components/users/user_profile.html', {
        'user': user,
        'is_own_profile': user == request.user,
        'updated': True
    })


# ========== UTILITY DATA MANAGEMENT ==========

@login_required
def utility_list_htmx(request, project_id=None):
    """List utilities with optional project filter."""
    queryset = Utility.objects.select_related('project')
    
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(project=project)
        context_project = project
    else:
        context_project = None
        # Filter by user's accessible projects
        user_projects = Project.objects.filter(
            Q(egis_project_manager=request.user.username) |
            Q(members__user=request.user)
        ).distinct()
        queryset = queryset.filter(project__in=user_projects)
    
    # Type filter
    utility_type = request.GET.get('type')
    if utility_type:
        queryset = queryset.filter(type=utility_type)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(owner__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Status filter
    status = request.GET.get('status')
    if status:
        queryset = queryset.filter(status=status)
    
    # Ordering
    queryset = queryset.order_by('type', 'name')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 50)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'utilities': page_obj,
        'project': context_project,
        'total_count': paginator.count,
        'search': search,
        'utility_type': utility_type,
        'status': status,
        'utility_types': Utility.objects.values_list('type', flat=True).distinct()
    }
    
    return render(request, 'components/utilities/utility_list.html', context)


@login_required
def utility_detail_htmx(request, utility_id):
    """Get utility details as HTML fragment."""
    utility = get_object_or_404(Utility.objects.select_related('project'), id=utility_id)
    
    # Check access permissions
    if not (request.user.is_admin or 
            utility.project.egis_project_manager == request.user.username or
            utility.project.members.filter(user=request.user).exists()):
        return HttpResponse("Unauthorized", status=403)
    
    # Get related conflicts
    conflicts = Conflict.objects.filter(
        Q(utility=utility) | Q(utility2=utility)
    ).select_related('project', 'utility', 'utility2')
    
    context = {
        'utility': utility,
        'conflicts': conflicts,
        'can_edit': request.user.is_admin or utility.project.egis_project_manager == request.user.username
    }
    
    return render(request, 'components/utilities/utility_detail.html', context)


@login_required
@require_http_methods(["POST"])
def utility_create_htmx(request, project_id):
    """Create a new utility."""
    project = get_object_or_404(Project, id=project_id)
    
    # Check permissions
    if not (request.user.is_admin or project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    # Create utility
    utility = Utility.objects.create(
        project=project,
        name=request.POST.get('name'),
        type=request.POST.get('type'),
        owner=request.POST.get('owner', ''),
        description=request.POST.get('description', ''),
        status=request.POST.get('status', 'active'),
        geometry=None  # TODO: Handle geometry from map drawing
    )
    
    messages.success(request, f"Utility '{utility.name}' created successfully.")
    
    # Return the new utility item
    return render(request, 'components/utilities/utility_item.html', {
        'utility': utility,
        'can_edit': True
    })


@login_required
@require_http_methods(["POST"])
def utility_update_htmx(request, utility_id):
    """Update utility information."""
    utility = get_object_or_404(Utility, id=utility_id)
    
    # Check permissions
    if not (request.user.is_admin or utility.project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    # Update fields
    utility.name = request.POST.get('name', utility.name)
    utility.type = request.POST.get('type', utility.type)
    utility.owner = request.POST.get('owner', utility.owner)
    utility.description = request.POST.get('description', utility.description)
    utility.status = request.POST.get('status', utility.status)
    
    utility.save()
    
    messages.success(request, f"Utility '{utility.name}' updated successfully.")
    
    return render(request, 'components/utilities/utility_detail.html', {
        'utility': utility,
        'can_edit': True,
        'updated': True
    })


@login_required
@require_http_methods(["DELETE"])
def utility_delete_htmx(request, utility_id):
    """Delete a utility."""
    utility = get_object_or_404(Utility, id=utility_id)
    
    # Check permissions
    if not (request.user.is_admin or utility.project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    utility_name = utility.name
    utility.delete()
    
    messages.success(request, f"Utility '{utility_name}' deleted successfully.")
    
    # Return empty response to remove the item from the list
    return HttpResponse("")


# ========== CONFLICT MANAGEMENT ==========

@login_required
def conflict_list_htmx(request, project_id=None):
    """List conflicts with filtering and pagination."""
    queryset = Conflict.objects.select_related('project', 'utility', 'utility2')
    
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(project=project)
        context_project = project
    else:
        context_project = None
        # Filter by user's accessible projects
        user_projects = Project.objects.filter(
            Q(egis_project_manager=request.user.username) |
            Q(members__user=request.user)
        ).distinct()
        queryset = queryset.filter(project__in=user_projects)
    
    # Status filter
    status = request.GET.get('status')
    if status:
        queryset = queryset.filter(status=status)
    
    # Severity filter
    severity = request.GET.get('severity')
    if severity:
        queryset = queryset.filter(severity=severity)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(description__icontains=search) |
            Q(utility__name__icontains=search) |
            Q(utility2__name__icontains=search)
        )
    
    # Ordering
    queryset = queryset.order_by('-created_at')
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 25)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'conflicts': page_obj,
        'project': context_project,
        'total_count': paginator.count,
        'search': search,
        'status': status,
        'severity': severity
    }
    
    return render(request, 'components/conflicts/conflict_list.html', context)


@login_required
def conflict_detail_htmx(request, conflict_id):
    """Get conflict details as HTML fragment."""
    conflict = get_object_or_404(
        Conflict.objects.select_related('project', 'utility', 'utility2', 'resolved_by'),
        id=conflict_id
    )
    
    # Check access permissions
    if not (request.user.is_admin or 
            conflict.project.egis_project_manager == request.user.username or
            conflict.project.members.filter(user=request.user).exists()):
        return HttpResponse("Unauthorized", status=403)
    
    context = {
        'conflict': conflict,
        'can_edit': request.user.is_admin or conflict.project.egis_project_manager == request.user.username
    }
    
    return render(request, 'components/conflicts/conflict_detail.html', context)


@login_required
@require_http_methods(["POST"])
def conflict_resolve_htmx(request, conflict_id):
    """Mark a conflict as resolved."""
    conflict = get_object_or_404(Conflict, id=conflict_id)
    
    # Check permissions
    if not (request.user.is_admin or conflict.project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    conflict.status = 'resolved'
    conflict.resolved_by = request.user
    conflict.resolved_at = timezone.now()
    conflict.resolution_notes = request.POST.get('resolution_notes', '')
    conflict.save()
    
    messages.success(request, "Conflict resolved successfully.")
    
    return render(request, 'components/conflicts/conflict_detail.html', {
        'conflict': conflict,
        'can_edit': True,
        'resolved': True
    })


@login_required
@require_http_methods(["POST"])
def conflict_detection_htmx(request, project_id):
    """Run automated conflict detection for a project."""
    project = get_object_or_404(Project, id=project_id)
    
    # Check permissions
    if not (request.user.is_admin or project.egis_project_manager == request.user.username):
        return HttpResponse("Unauthorized", status=403)
    
    # TODO: Implement actual spatial conflict detection algorithm
    # This is a placeholder implementation
    
    messages.info(request, "Conflict detection analysis started. This may take a few moments...")
    
    return render(request, 'components/conflicts/detection_status.html', {
        'project': project,
        'status': 'running'
    })


# ========== DOCUMENT MANAGEMENT ==========

@login_required
def document_list_htmx(request, project_id=None):
    """List documents with filtering and pagination."""
    queryset = Document.objects.filter(
        Q(uploaded_by=request.user) |
        Q(is_public=True) |
        Q(shares__shared_with=request.user)
    ).distinct().select_related('uploaded_by', 'project')
    
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(project=project)
        context_project = project
    else:
        context_project = None
    
    # Type filter
    doc_type = request.GET.get('type')
    if doc_type:
        queryset = queryset.filter(file_type=doc_type)
    
    # Search filter
    search = request.GET.get('search')
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Ordering
    order_by = request.GET.get('order_by', '-created_at')
    queryset = queryset.order_by(order_by)
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(queryset, 24)  # Grid layout
    page_obj = paginator.get_page(page_number)
    
    context = {
        'documents': page_obj,
        'project': context_project,
        'total_count': paginator.count,
        'search': search,
        'doc_type': doc_type,
        'order_by': order_by
    }
    
    return render(request, 'components/documents/document_grid.html', context)


@login_required
@require_http_methods(["POST"])
def document_upload_htmx(request):
    """Handle document upload via HTMX."""
    project_id = request.POST.get('project_id')
    file = request.FILES.get('file')
    
    if not file:
        return HttpResponse("No file provided", status=400)
    
    project = None
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        # Check project access
        if not (request.user.is_admin or 
                project.egis_project_manager == request.user.username or
                project.members.filter(user=request.user).exists()):
            return HttpResponse("Unauthorized", status=403)
    
    # Create document
    document = Document.objects.create(
        name=file.name,
        file=file,
        file_type=file.content_type,
        file_size=file.size,
        uploaded_by=request.user,
        project=project,
        description=request.POST.get('description', '')
    )
    
    messages.success(request, f"Document '{document.name}' uploaded successfully.")
    
    return render(request, 'components/documents/document_item.html', {
        'document': document
    })


@login_required
@require_http_methods(["DELETE"])
def document_delete_htmx(request, document_id):
    """Delete a document."""
    document = get_object_or_404(Document, id=document_id)
    
    # Check permissions
    if not (request.user.is_admin or document.uploaded_by == request.user):
        return HttpResponse("Unauthorized", status=403)
    
    document_name = document.name
    document.delete()
    
    messages.success(request, f"Document '{document_name}' deleted successfully.")
    
    return HttpResponse("")


# ========== NOTIFICATION MANAGEMENT ==========

@login_required
def notification_list_htmx(request):
    """Get user notifications as HTML fragment."""
    notifications = request.user.notifications.order_by('-created_at')
    
    # Filter by read status
    show_all = request.GET.get('show_all', 'false') == 'true'
    if not show_all:
        notifications = notifications.filter(read=False)
    
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(notifications, 10)
    page_obj = paginator.get_page(page_number)
    
    context = {
        'notifications': page_obj,
        'unread_count': request.user.notifications.filter(read=False).count(),
        'show_all': show_all
    }
    
    return render(request, 'components/notifications/notification_list.html', context)


@login_required
@require_http_methods(["POST"])
def notification_mark_read_htmx(request, notification_id):
    """Mark a notification as read."""
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.read = True
    notification.save()
    
    # Return updated notification item
    return render(request, 'components/notifications/notification_item.html', {
        'notification': notification
    })


@login_required
@require_http_methods(["POST"])
def notification_mark_all_read_htmx(request):
    """Mark all notifications as read."""
    count = request.user.notifications.filter(read=False).update(read=True)
    
    messages.success(request, f"{count} notifications marked as read.")
    
    # Return updated notification list
    return notification_list_htmx(request)


# ========== CHAT/MESSAGING ==========

@login_required
def chat_messages_htmx(request):
    """Get chat messages for a channel."""
    channel = request.GET.get('channel', 'general')
    project_id = request.GET.get('project_id')
    
    queryset = ChatMessage.objects.filter(channel=channel)
    if project_id:
        project = get_object_or_404(Project, id=project_id)
        queryset = queryset.filter(project=project)
    
    messages_qs = queryset.select_related('user').order_by('-created_at')[:50]
    
    context = {
        'messages': reversed(messages_qs),
        'channel': channel,
        'project_id': project_id
    }
    
    return render(request, 'components/chat/message_list.html', context)


@login_required
@require_http_methods(["POST"])
def chat_send_message_htmx(request):
    """Send a chat message."""
    content = request.POST.get('content')
    channel = request.POST.get('channel', 'general')
    project_id = request.POST.get('project_id')
    
    if not content:
        return HttpResponse("Message content is required", status=400)
    
    project = None
    if project_id:
        project = get_object_or_404(Project, id=project_id)
    
    message = ChatMessage.objects.create(
        user=request.user,
        content=content,
        channel=channel,
        project=project
    )
    
    # Return just the new message
    return render(request, 'components/chat/message_item.html', {
        'message': message
    })


# ========== DASHBOARD DATA ==========

@login_required
def dashboard_stats_htmx(request):
    """Get dashboard statistics as HTML fragment."""
    user = request.user
    
    # Get user's projects
    projects = Project.objects.filter(
        Q(egis_project_manager=user.username) |
        Q(members__user=user)
    ).distinct()
    
    stats = {
        'total_projects': projects.count(),
        'active_projects': projects.filter(rag_status='Green').count(),
        'warning_projects': projects.filter(rag_status='Yellow').count(),
        'critical_projects': projects.filter(rag_status='Red').count(),
        'total_utilities': Utility.objects.filter(project__in=projects).count(),
        'open_conflicts': Conflict.objects.filter(
            project__in=projects, 
            status='open'
        ).count(),
        'pending_tasks': Task.objects.filter(
            project__in=projects,
            completed=False,
            assigned_to=user
        ).count(),
        'unread_notifications': user.notifications.filter(read=False).count()
    }
    
    return render(request, 'components/dashboard/stats_cards.html', {'stats': stats})


@login_required
def recent_activity_htmx(request):
    """Get recent activity for dashboard."""
    # Get activities from last 7 days
    since = timezone.now() - timedelta(days=7)
    
    # Get user's accessible projects
    user_projects = Project.objects.filter(
        Q(egis_project_manager=request.user.username) |
        Q(members__user=request.user)
    ).distinct()
    
    # Combine different activity types
    activities = []
    
    # Recent tasks
    recent_tasks = Task.objects.filter(
        project__in=user_projects,
        created_at__gte=since
    ).select_related('project', 'assigned_to')[:10]
    
    for task in recent_tasks:
        activities.append({
            'type': 'task',
            'title': f"New task: {task.title}",
            'description': f"In project {task.project.name}",
            'timestamp': task.created_at,
            'icon': 'check-square',
            'user': task.assigned_to
        })
    
    # Recent comments
    recent_comments = Comment.objects.filter(
        created_at__gte=since
    ).select_related('user')[:10]
    
    for comment in recent_comments:
        activities.append({
            'type': 'comment',
            'title': f"{comment.user.get_full_name()} commented",
            'description': comment.content[:100] + '...' if len(comment.content) > 100 else comment.content,
            'timestamp': comment.created_at,
            'icon': 'message-circle',
            'user': comment.user
        })
    
    # Sort activities by timestamp
    activities.sort(key=lambda x: x['timestamp'], reverse=True)
    
    context = {
        'activities': activities[:20]  # Limit to 20 most recent
    }
    
    return render(request, 'components/dashboard/recent_activities.html', context)