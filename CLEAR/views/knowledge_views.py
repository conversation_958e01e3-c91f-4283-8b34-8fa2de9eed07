"""
Knowledge Base Views

This module contains views related to knowledge base, articles, and documentation.
Includes both traditional views and HTMX endpoints for dynamic interactions.

"""

import logging
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)
import json
from datetime import timedelta
from ..models.knowledge import (
    Article,
    ArticleCategory,
    KnowledgeArticle,
)
from ..models.user_activity import FeatureRequest, FeatureVote
from .imports_base import *
from django.utils.text import slugify
from ..models import Conflict, Document, Project, Stakeholder, Task, User, Utility
from ..services.entity_chaining import EntityChainingService
from ..models import EntityRelationship, KnowledgeGraphNode

logger = logging.getLogger(__name__)

# ========================================
# CLASS-BASED VIEWS
# ========================================

class KnowledgeHomeView(LoginRequiredMixin, TemplateView):
    """Knowledge base home"""
    template_name = 'knowledge/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get popular articles
        context['popular_articles'] = KnowledgeArticle.objects.filter(
            is_published=True
        ).order_by('-views_count')[:5]

        # Get recent articles
        context['recent_articles'] = KnowledgeArticle.objects.filter(
            is_published=True
        ).order_by('-created_at')[:5]

        return context


class KnowledgeArticleView(LoginRequiredMixin, DetailView):
    """Knowledge article detail"""
    model = KnowledgeArticle
    template_name = 'knowledge/article.html'
    context_object_name = 'article'

    def get_object(self):
        obj = super().get_object()
        # Increment view count
        obj.views_count += 1
        obj.save(update_fields=['views_count'])
        return obj


class KnowledgeSetupDocsView(LoginRequiredMixin, TemplateView):
    """Developer setup documentation"""
    template_name = 'knowledge/setup-docs.html'


class KnowledgeBaseView(LoginRequiredMixin, TemplateView):
    template_name = 'knowledge/knowledge_base.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all knowledge articles
        articles = KnowledgeArticle.objects.filter(is_published=True).order_by('-created_at')
        
        # Get categories
        categories = KnowledgeArticle.objects.filter(
            is_published=True
        ).values('category').annotate(
            count=Count('id')
        ).order_by('category')
        
        # Get recent articles
        recent_articles = articles.order_by('-created_at')[:5]
        
        # Get popular articles
        popular_articles = articles.annotate(
            view_count=Count('articleview')
        ).order_by('-view_count')[:5]
        
        context.update({
            'articles': articles,
            'categories': categories,
            'recent_articles': recent_articles,
            'popular_articles': popular_articles,
            'article_count': articles.count()
        })
        
        return context


class KnowledgeArticleDetailView(LoginRequiredMixin, DetailView):
    model = KnowledgeArticle
    template_name = 'knowledge/article_detail.html'
    context_object_name = 'article'
    
    def get_object(self, queryset=None):
        article = super().get_object(queryset)
        
        # Check if article is published or user is staff
        if not article.is_published and not self.request.user.is_staff:
            raise PermissionDenied
        
        return article
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        article = self.get_object()
        
        # Record view
        ArticleView.objects.create(
            article=article,
            user=self.request.user,
            ip_address=self.request.META.get('REMOTE_ADDR', '')
        )
        
        # Get related articles
        related_articles = KnowledgeArticle.objects.filter(
            category=article.category,
            is_published=True
        ).exclude(id=article.id).order_by('-created_at')[:3]
        
        # Get article revisions
        revisions = ArticleRevision.objects.filter(
            article=article
        ).order_by('-created_at')
        
        # Get article attachments
        attachments = ArticleAttachment.objects.filter(article=article)
        
        # Get article votes
        upvotes = ArticleVote.objects.filter(article=article, vote_type='up').count()
        downvotes = ArticleVote.objects.filter(article=article, vote_type='down').count()
        
        # Check if user has voted
        user_vote = None
        if self.request.user.is_authenticated:
            try:
                user_vote = ArticleVote.objects.get(
                    article=article,
                    user=self.request.user
                ).vote_type
            except ArticleVote.DoesNotExist:
                pass
        
        context.update({
            'related_articles': related_articles,
            'revisions': revisions,
            'attachments': attachments,
            'upvotes': upvotes,
            'downvotes': downvotes,
            'user_vote': user_vote,
            'view_count': ArticleView.objects.filter(article=article).count()
        })
        
        return context


class KnowledgeArticleCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = KnowledgeArticle
    template_name = 'knowledge/article_form.html'
    fields = ['title', 'content', 'category', 'tags', 'is_published']
    
    def test_func(self):
        # Only staff or users with create_article permission can create articles
        return self.request.user.is_staff or self.request.user.has_perm('CLEAR.add_knowledgearticle')
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        form.instance.updated_by = self.request.user
        
        # Create initial revision
        response = super().form_valid(form)
        
        ArticleRevision.objects.create(
            article=self.object,
            content=form.cleaned_data['content'],
            revision_number=1,
            created_by=self.request.user,
            comment='Initial version'
        )
        
        # Handle attachments
        if 'attachments' in self.request.FILES:
            for attachment in self.request.FILES.getlist('attachments'):
                ArticleAttachment.objects.create(
                    article=self.object,
                    file=attachment,
                    filename=attachment.name,
                    uploaded_by=self.request.user
                )
        
        # Create activity record
        Activity.objects.create(
            user=self.request.user,
            action='created',
            target_model='KnowledgeArticle',
            target_id=self.object.id,
            target_name=self.object.title,
            description=f"Created knowledge article: {self.object.title}"
        )
        
        return response
    
    def get_success_url(self):
        return reverse_lazy('knowledge_article_detail', kwargs={'pk': self.object.pk})


class KnowledgeArticleUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = KnowledgeArticle
    template_name = 'knowledge/article_form.html'
    fields = ['title', 'content', 'category', 'tags', 'is_published']
    
    def test_func(self):
        article = self.get_object()
        # Only staff, the creator, or users with change_article permission can update articles
        return (self.request.user.is_staff or 
                article.created_by == self.request.user or 
                self.request.user.has_perm('CLEAR.change_knowledgearticle'))
    
    def form_valid(self, form):
        # Get the original content before update
        original_article = KnowledgeArticle.objects.get(pk=self.object.pk)
        original_content = original_article.content
        
        # Update the article
        form.instance.updated_by = self.request.user
        form.instance.updated_at = timezone.now()
        
        # Create new revision if content changed
        response = super().form_valid(form)
        
        if original_content != form.cleaned_data['content']:
            # Get latest revision number
            latest_revision = ArticleRevision.objects.filter(
                article=self.object
            ).order_by('-revision_number').first()
            
            new_revision_number = 1
            if latest_revision:
                new_revision_number = latest_revision.revision_number + 1
            
            # Create new revision
            ArticleRevision.objects.create(
                article=self.object,
                content=form.cleaned_data['content'],
                revision_number=new_revision_number,
                created_by=self.request.user,
                comment=self.request.POST.get('revision_comment', 'Updated content')
            )
        
        # Handle attachments
        if 'attachments' in self.request.FILES:
            for attachment in self.request.FILES.getlist('attachments'):
                ArticleAttachment.objects.create(
                    article=self.object,
                    file=attachment,
                    filename=attachment.name,
                    uploaded_by=self.request.user
                )
        
        # Create activity record
        Activity.objects.create(
            user=self.request.user,
            action='updated',
            target_model='KnowledgeArticle',
            target_id=self.object.id,
            target_name=self.object.title,
            description=f"Updated knowledge article: {self.object.title}"
        )
        
        return response
    
    def get_success_url(self):
        return reverse_lazy('knowledge_article_detail', kwargs={'pk': self.object.pk})


class KnowledgeArticleDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    model = KnowledgeArticle
    template_name = 'knowledge/article_confirm_delete.html'
    success_url = reverse_lazy('knowledge_base')
    
    def test_func(self):
        article = self.get_object()
        # Only staff, the creator, or users with delete_article permission can delete articles
        return (self.request.user.is_staff or 
                article.created_by == self.request.user or 
                self.request.user.has_perm('CLEAR.delete_knowledgearticle'))
    
    def delete(self, request, *args, **kwargs):
        article = self.get_object()
        
        # Create activity record before deletion
        Activity.objects.create(
            user=self.request.user,
            action='deleted',
            target_model='KnowledgeArticle',
            target_id=article.id,
            target_name=article.title,
            description=f"Deleted knowledge article: {article.title}"
        )
        
        return super().delete(request, *args, **kwargs)


# ========================================
# FUNCTION-BASED VIEWS
# ========================================

@login_required
def knowledge_base_list(request):
    """Knowledge Base main list view with categories and featured articles"""
    # Get featured articles
    featured_articles = Article.objects.filter(
        status='published',
        is_featured=True,
        is_archived=False
    ).select_related('author', 'category').order_by('-published_at')[:3]
    
    # Get recent articles
    recent_articles = Article.objects.filter(
        status='published',
        is_archived=False
    ).select_related('author', 'category').order_by('-published_at')[:10]
    
    # Get categories with article counts
    categories = ArticleCategory.objects.filter(
        is_active=True
    ).prefetch_related('articles').order_by('sort_order', 'name')
    
    # Add article counts to categories
    for category in categories:
        category.published_count = category.articles.filter(status='published', is_archived=False).count()
    
    # Get search query if provided
    search_query = request.GET.get('q', '')
    if search_query:
        recent_articles = recent_articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(tags__icontains=search_query)
        )
    
    # Get category filter if provided
    category_filter = request.GET.get('category')
    if category_filter:
        try:
            category_obj = ArticleCategory.objects.get(slug=category_filter)
            recent_articles = recent_articles.filter(category=category_obj)
        except ArticleCategory.DoesNotExist:
            pass
    
    context = {
        'featured_articles': featured_articles,
        'recent_articles': recent_articles,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_filter,
        'page_title': 'Knowledge Base'
    }
    
    return render(request, 'knowledge_base/article_list.html', context)


@login_required
def knowledge_base_article_detail(request, slug):
    """Article detail view with related articles and comments"""
    article = get_object_or_404(
        Article,
        slug=slug,
        status='published',
        is_archived=False
    )
    
    # Record article view
    if request.user.is_authenticated:
        ArticleView.objects.create(
            article=article,
            user=request.user,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
    
    # Increment view count
    article.view_count += 1
    article.save(update_fields=['view_count'])
    
    # Get related articles
    related_articles = Article.objects.filter(
        category=article.category,
        status='published',
        is_archived=False
    ).exclude(id=article.id).order_by('-published_at')[:5]
    
    # Get user's vote if exists
    user_vote = None
    if request.user.is_authenticated:
        try:
            user_vote = ArticleVote.objects.get(article=article, user=request.user)
        except ArticleVote.DoesNotExist:
            pass
    
    # Get article statistics
    upvotes = article.get_upvotes()
    downvotes = article.get_downvotes()
    average_rating = article.get_average_rating()
    
    context = {
        'article': article,
        'related_articles': related_articles,
        'user_vote': user_vote,
        'upvotes': upvotes,
        'downvotes': downvotes,
        'average_rating': average_rating,
        'page_title': article.title
    }
    
    return render(request, 'knowledge_base/article_detail.html', context)


@login_required
def knowledge_base_category_list(request, slug):
    """Category view showing articles in a specific category"""
    category = get_object_or_404(ArticleCategory, slug=slug, is_active=True)
    
    # Get articles in this category
    articles = Article.objects.filter(
        category=category,
        status='published',
        is_archived=False
    ).select_related('author').order_by('-published_at')
    
    # Get search query if provided
    search_query = request.GET.get('q', '')
    if search_query:
        articles = articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(tags__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(articles, 12)  # 12 articles per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get subcategories
    subcategories = ArticleCategory.objects.filter(
        parent=category,
        is_active=True
    ).order_by('sort_order', 'name')
    
    context = {
        'category': category,
        'articles': page_obj,
        'subcategories': subcategories,
        'search_query': search_query,
        'page_title': f'{category.name} - Knowledge Base'
    }
    
    return render(request, 'knowledge_base/category_list.html', context)


# ========================================
# KNOWLEDGE BASE HTMX ENDPOINTS
# ========================================

@login_required
@require_http_methods(["GET"])
def kb_articles_search_htmx(request):
    """HTMX endpoint for article search"""
    search_query = request.GET.get('q', '')
    category_slug = request.GET.get('category', '')
    
    articles = Article.objects.filter(
        status='published',
        is_archived=False
    ).select_related('author', 'category')
    
    if search_query:
        articles = articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(tags__icontains=search_query) |
            Q(excerpt__icontains=search_query)
        )
    
    if category_slug:
        try:
            category = ArticleCategory.objects.get(slug=category_slug)
            articles = articles.filter(category=category)
        except ArticleCategory.DoesNotExist:
            pass
    
    articles = articles.order_by('-published_at')[:20]  # Limit to 20 results
    
    return render(request, 'knowledge_base/partials/article_search_results.html', {
        'articles': articles,
        'search_query': search_query
    })


@login_required
@require_http_methods(["POST"])
def kb_article_vote_htmx(request, article_id):
    """HTMX endpoint for article voting"""
    article = get_object_or_404(Article, id=article_id)
    vote_type = request.POST.get('vote_type')  # 'upvote' or 'downvote'
    rating = request.POST.get('rating')  # 1-5 stars
    feedback = request.POST.get('feedback', '')
    
    if vote_type not in ['upvote', 'downvote']:
        return HttpResponse('Invalid vote type', status=400)
    
    # Get or create user's vote
    vote, created = ArticleVote.objects.get_or_create(
        article=article,
        user=request.user,
        defaults={
            'vote_type': vote_type,
            'rating': int(rating) if rating else None,
            'feedback': feedback
        }
    )
    
    if not created:
        # Update existing vote
        vote.vote_type = vote_type
        if rating:
            vote.rating = int(rating)
        vote.feedback = feedback
        vote.save()
    
    # Get updated vote counts
    upvotes = article.get_upvotes()
    downvotes = article.get_downvotes()
    average_rating = article.get_average_rating()
    
    return render(request, 'knowledge_base/partials/article_voting.html', {
        'article': article,
        'user_vote': vote,
        'upvotes': upvotes,
        'downvotes': downvotes,
        'average_rating': average_rating
    })


@login_required
@require_http_methods(["GET"])
def kb_article_preview_htmx(request, article_id):
    """HTMX endpoint for article preview modal"""
    article = get_object_or_404(Article, id=article_id)
    
    # Check if user can view (must be published or user is author/admin)
    if article.status != 'published' and article.author != request.user and not request.user.is_staff:
        return HttpResponse('Article not found', status=404)
    
    return render(request, 'knowledge_base/partials/article_preview_modal.html', {
        'article': article
    })


@login_required
@require_http_methods(["POST"])
def kb_article_create_htmx(request):
    """HTMX endpoint for creating new articles (staff only)"""
    if not request.user.is_staff and request.user.role not in ['admin', 'manager']:
        return HttpResponse('Unauthorized', status=403)
    
    title = request.POST.get('title', '').strip()
    content = request.POST.get('content', '').strip()
    excerpt = request.POST.get('excerpt', '').strip()
    category_id = request.POST.get('category_id')
    article_type = request.POST.get('article_type', 'guide')
    tags = request.POST.get('tags', '').strip()
    is_featured = request.POST.get('is_featured') == 'on'
    
    if not title or not content:
        return HttpResponse('Title and content are required', status=400)
    
    try:
        category = None
        if category_id:
            category = ArticleCategory.objects.get(id=category_id)
        
        # Generate slug from title
        slug = slugify(title)
        
        # Ensure slug is unique
        original_slug = slug
        counter = 1
        while Article.objects.filter(slug=slug).exists():
            slug = f"{original_slug}-{counter}"
            counter += 1
        
        article = Article.objects.create(
            title=title,
            slug=slug,
            content=content,
            excerpt=excerpt or content[:200] + '...',
            category=category,
            article_type=article_type,
            tags=tags,
            is_featured=is_featured,
            author=request.user,
            status='draft'  # Start as draft
        )
        
        # Return updated article list or success message
        return render(request, 'knowledge_base/partials/article_created.html', {
            'article': article,
            'success_message': f'Article "{title}" created successfully as draft.'
        })
        
    except Exception as e:
        logger.error(f'Error creating article: {str(e)}')
        return HttpResponse(f'Error creating article: {str(e)}', status=400)


@login_required
@require_http_methods(["POST"])
def kb_article_update_htmx(request, article_id):
    """HTMX endpoint for updating articles"""
    article = get_object_or_404(Article, id=article_id)
    
    # Check permissions (author, staff, or admin)
    if article.author != request.user and not request.user.is_staff and request.user.role not in ['admin', 'manager']:
        return HttpResponse('Unauthorized', status=403)
    
    title = request.POST.get('title', '').strip()
    content = request.POST.get('content', '').strip()
    excerpt = request.POST.get('excerpt', '').strip()
    category_id = request.POST.get('category_id')
    article_type = request.POST.get('article_type', article.article_type)
    tags = request.POST.get('tags', '').strip()
    is_featured = request.POST.get('is_featured') == 'on'
    status = request.POST.get('status', article.status)
    
    if not title or not content:
        return HttpResponse('Title and content are required', status=400)
    
    try:
        # Store original content for revision tracking
        original_content = article.content
        original_title = article.title
        
        # Update article
        article.title = title
        article.content = content
        article.excerpt = excerpt or content[:200] + '...'
        article.article_type = article_type
        article.tags = tags
        article.is_featured = is_featured
        
        # Update category if provided
        if category_id:
            try:
                category = ArticleCategory.objects.get(id=category_id)
                article.category = category
            except ArticleCategory.DoesNotExist:
                pass
        
        # Update status if user has permission
        if request.user.is_staff or request.user.role in ['admin', 'manager']:
            article.status = status
            if status == 'published' and not article.published_at:
                article.published_at = timezone.now()
        
        article.save()
        
        # Create revision record if content changed
        if original_content != content or original_title != title:
            ArticleRevision.objects.create(
                article=article,
                version_number=article.get_next_version_number(),
                change_type='content' if original_content != content else 'metadata',
                change_summary=f'Updated by {request.user.get_full_name() or request.user.username}',
                modified_by=request.user,
                content_diff={'old_title': original_title, 'new_title': title} if original_title != title else None
            )
        
        return render(request, 'knowledge_base/partials/article_updated.html', {
            'article': article,
            'success_message': f'Article "{title}" updated successfully.'
        })
        
    except Exception as e:
        logger.error(f'Error updating article: {str(e)}')
        return HttpResponse(f'Error updating article: {str(e)}', status=400)


@login_required
@require_http_methods(["GET"])
def kb_categories_list_htmx(request):
    """HTMX endpoint for loading categories list"""
    categories = ArticleCategory.objects.filter(
        is_active=True
    ).order_by('sort_order', 'name')
    
    # Add article counts
    for category in categories:
        category.published_count = category.articles.filter(
            status='published',
            is_archived=False
        ).count()
    
    return render(request, 'knowledge_base/partials/categories_list.html', {
        'categories': categories
    })


@login_required
@require_http_methods(["GET"])
def kb_recent_articles_htmx(request):
    """HTMX endpoint for loading recent articles"""
    limit = int(request.GET.get('limit', 10))
    offset = int(request.GET.get('offset', 0))
    
    articles = Article.objects.filter(
        status='published',
        is_archived=False
    ).select_related('author', 'category').order_by('-published_at')[offset:offset+limit]
    
    return render(request, 'knowledge_base/partials/recent_articles.html', {
        'articles': articles,
        'has_more': Article.objects.filter(status='published', is_archived=False).count() > offset + limit
    })


# ========================================
# ENTITY MANAGEMENT AND KNOWLEDGE GRAPH
# ========================================

@login_required
@require_http_methods(["GET"])
def entity_mention_autocomplete_htmx(request):
    """HTMX endpoint for entity mention autocomplete with cascading chain support"""
    
    query = request.GET.get('q', '').strip()
    entity_type = request.GET.get('type', '').strip()
    chain_mode = request.GET.get('chain_mode', 'false').lower() == 'true'
    parent_chain = request.GET.get('parent_chain', '').strip()
    
    if not query or len(query) < 1:
        return HttpResponse('')
    
    suggestions = []
    
    try:
        if chain_mode:
            # Use cascading suggestions for entity chains
            chaining_service = EntityChainingService()
            
            # Determine the partial chain to work with
            if parent_chain:
                # Building on an existing chain
                if query.startswith('/'):
                    # User typed "/" to add to chain
                    partial_chain = parent_chain + query
                else:
                    # User is completing the current entity
                    partial_chain = parent_chain + '/' + query
            else:
                # Starting a new chain
                partial_chain = query
            
            suggestions = chaining_service.get_cascading_suggestions(partial_chain)
            
        else:
            # Original autocomplete logic for simple entity mentions
            if entity_type == 'project' or not entity_type:
                projects = Project.objects.filter(
                    Q(name__icontains=query) | Q(description__icontains=query)
                )[:5]
                for project in projects:
                    suggestions.append({
                        'type': 'project',
                        'id': str(project.id),
                        'name': project.name,
                        'mention_text': f'@project-{project.id}',
                        'description': project.description[:100] if project.description else '',
                        'can_have_children': True
                    })
            
            if entity_type == 'task' or not entity_type:
                tasks = Task.objects.filter(
                    Q(title__icontains=query) | Q(description__icontains=query)
                )[:5]
                for task in tasks:
                    suggestions.append({
                        'type': 'task',
                        'id': str(task.id),
                        'name': task.title,
                        'mention_text': f'@task-{task.id}',
                        'description': task.description[:100] if task.description else '',
                        'can_have_children': True
                    })
            
            if entity_type == 'user' or not entity_type:
                users = User.objects.filter(
                    Q(first_name__icontains=query) | 
                    Q(last_name__icontains=query) |
                    Q(username__icontains=query)
                )[:5]
                for user in users:
                    suggestions.append({
                        'type': 'user',
                        'id': str(user.id),
                        'name': f'{user.first_name} {user.last_name}',
                        'mention_text': f'@user-{user.id}',
                        'description': user.job_title or user.role,
                        'can_have_children': True
                    })
            
            if entity_type == 'stakeholder' or not entity_type:
                stakeholders = Stakeholder.objects.filter(
                    Q(first_name__icontains=query) | 
                    Q(last_name__icontains=query) |
                    Q(contact_company__icontains=query)
                )[:5]
                for stakeholder in stakeholders:
                    suggestions.append({
                        'type': 'stakeholder',
                        'id': str(stakeholder.id),
                        'name': stakeholder.full_name or stakeholder.contact_company,
                        'mention_text': f'@stakeholder-{stakeholder.id}',
                        'description': stakeholder.contact_company or stakeholder.stakeholder_type,
                        'can_have_children': True
                    })
            
            # Add other entity types for completeness
            if entity_type == 'utility' or not entity_type:
                utilities = Utility.objects.filter(
                    Q(name__icontains=query) | Q(company__icontains=query)
                )[:3]
                for utility in utilities:
                    suggestions.append({
                        'type': 'utility',
                        'id': str(utility.id),
                        'name': utility.name,
                        'mention_text': f'@utility-{utility.id}',
                        'description': utility.company or utility.utility_type,
                        'can_have_children': True
                    })
            
            if entity_type == 'conflict' or not entity_type:
                conflicts = Conflict.objects.filter(
                    Q(description__icontains=query) | Q(conflict_type__icontains=query)
                )[:3]
                for conflict in conflicts:
                    suggestions.append({
                        'type': 'conflict',
                        'id': str(conflict.id),
                        'name': f"{conflict.conflict_type} Conflict",
                        'mention_text': f'@conflict-{conflict.id}',
                        'description': conflict.description[:100] if conflict.description else '',
                        'can_have_children': True
                    })
            
            if entity_type == 'document' or not entity_type:
                documents = Document.objects.filter(
                    Q(title__icontains=query) | Q(description__icontains=query)
                )[:3]
                for document in documents:
                    suggestions.append({
                        'type': 'document',
                        'id': str(document.id),
                        'name': document.title,
                        'mention_text': f'@document-{document.id}',
                        'description': document.description[:100] if document.description else '',
                        'can_have_children': True
                    })
        
        context = {
            'suggestions': suggestions,
            'query': query,
            'chain_mode': chain_mode,
            'parent_chain': parent_chain
        }
        
        return render(request, 'components/ai_communication/entity_autocomplete.html', context)
        
    except Exception as e:
        logger.error(f"Error in entity autocomplete: {str(e)}")
        return HttpResponse('<div class="text-red-600">Error loading suggestions</div>')


@login_required
@require_http_methods(["GET"])
def entity_chain_builder_htmx(request):
    """HTMX endpoint for entity chain builder component"""
    
    try:
        chain_text = request.GET.get('chain', '').strip()
        chain_mode = request.GET.get('chain_mode', 'true').lower() == 'true'
        
        current_chain_components = []
        
        if chain_text:
            # Parse existing chain
            EntityChainingService()
            
            # Remove @ symbol if present
            if chain_text.startswith('@'):
                chain_text = chain_text[1:]
            
            # Parse chain components
            components = chain_text.split('/')
            for component in components:
                if '-' in component:
                    entity_type, entity_id = component.rsplit('-', 1)
                    
                    # Get entity name (simplified - would use service method in production)
                    entity_name = f"{entity_type}-{entity_id}"
                    
                    current_chain_components.append({
                        'type': entity_type,
                        'id': entity_id,
                        'name': entity_name,
                        'mention_text': f"@{component}"
                    })
        
        context = {
            'current_chain_components': current_chain_components,
            'chain_mode': chain_mode,
            'chain_text': chain_text
        }
        
        return render(request, 'components/ai_communication/entity_chain_builder.html', context)
        
    except Exception as e:
        logger.error(f"Error in chain builder: {str(e)}")
        return HttpResponse('<div class="text-red-600">Error loading chain builder</div>')


@login_required
@require_http_methods(["GET"])
def knowledge_graph_visualization_htmx(request):
    """HTMX endpoint for knowledge graph visualization"""
    
    try:
        # Get top entities by importance
        nodes = KnowledgeGraphNode.objects.filter(
            importance_score__gt=0.1
        ).order_by('-importance_score')[:50]
        
        # Get relationships between these nodes
        node_keys = [f"{node.entity_type}:{node.entity_id}" for node in nodes]
        relationships = EntityRelationship.objects.filter(
            strength__gt=0.3
        )[:100]
        
        # Format data for visualization
        graph_data = {
            'nodes': [],
            'edges': []
        }
        
        for node in nodes:
            graph_data['nodes'].append({
                'id': f"{node.entity_type}:{node.entity_id}",
                'label': node.entity_name,
                'type': node.entity_type,
                'importance': node.importance_score,
                'centrality': node.centrality_score,
                'activity': node.activity_level,
                'mentions': node.total_mentions
            })
        
        for rel in relationships:
            source_id = f"{rel.source_entity_type}:{rel.source_entity_id}"
            target_id = f"{rel.target_entity_type}:{rel.target_entity_id}"
            
            if source_id in node_keys and target_id in node_keys:
                graph_data['edges'].append({
                    'source': source_id,
                    'target': target_id,
                    'type': rel.relationship_type,
                    'strength': rel.strength,
                    'confidence': rel.confidence
                })
        
        context = {
            'graph_data': graph_data,
            'node_count': len(nodes),
            'relationship_count': len(relationships)
        }
        
        return render(request, 'components/ai_communication/knowledge_graph.html', context)
        
    except Exception as e:
        logger.error(f"Error in knowledge graph visualization: {str(e)}")
        return HttpResponse('<div class="text-red-600">Error loading knowledge graph</div>')


# ========================================
# LEGACY VIEWS (Maintained for compatibility)
# ========================================

@login_required
def knowledge_base_view(request):
    """View the knowledge base."""
    # Get all knowledge articles
    articles = KnowledgeArticle.objects.filter(is_published=True).order_by('-created_at')
    
    # Apply filters if provided
    category_filter = request.GET.get('category')
    if category_filter:
        articles = articles.filter(category=category_filter)
    
    search_query = request.GET.get('search')
    if search_query:
        articles = articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(tags__icontains=search_query)
        )
    
    # Get categories
    categories = KnowledgeArticle.objects.filter(
        is_published=True
    ).values('category').annotate(
        count=Count('id')
    ).order_by('category')
    
    # Get recent articles
    recent_articles = KnowledgeArticle.objects.filter(
        is_published=True
    ).order_by('-created_at')[:5]
    
    # Get popular articles
    popular_articles = KnowledgeArticle.objects.filter(
        is_published=True
    ).annotate(
        view_count=Count('articleview')
    ).order_by('-view_count')[:5]
    
    # Paginate results
    paginator = Paginator(articles, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'recent_articles': recent_articles,
        'popular_articles': popular_articles,
        'category_filter': category_filter,
        'search_query': search_query,
        'article_count': articles.count()
    }
    
    return render(request, 'knowledge/knowledge_base.html', context)


@login_required
def knowledge_article_detail(request, article_id):
    """View a knowledge article."""
    article = get_object_or_404(KnowledgeArticle, pk=article_id)
    
    # Check if article is published or user is staff
    if not article.is_published and not request.user.is_staff:
        return render(request, '403.html', status=403)
    
    # Record view
    ArticleView.objects.create(
        article=article,
        user=request.user,
        ip_address=request.META.get('REMOTE_ADDR', '')
    )
    
    # Get related articles
    related_articles = KnowledgeArticle.objects.filter(
        category=article.category,
        is_published=True
    ).exclude(id=article.id).order_by('-created_at')[:3]
    
    # Get article revisions
    revisions = ArticleRevision.objects.filter(
        article=article
    ).order_by('-created_at')
    
    # Get article attachments
    attachments = ArticleAttachment.objects.filter(article=article)
    
    # Get article votes
    upvotes = ArticleVote.objects.filter(article=article, vote_type='up').count()
    downvotes = ArticleVote.objects.filter(article=article, vote_type='down').count()
    
    # Check if user has voted
    user_vote = None
    if request.user.is_authenticated:
        try:
            user_vote = ArticleVote.objects.get(
                article=article,
                user=request.user
            ).vote_type
        except ArticleVote.DoesNotExist:
            pass
    
    context = {
        'article': article,
        'related_articles': related_articles,
        'revisions': revisions,
        'attachments': attachments,
        'upvotes': upvotes,
        'downvotes': downvotes,
        'user_vote': user_vote,
        'view_count': ArticleView.objects.filter(article=article).count()
    }
    
    return render(request, 'knowledge/article_detail.html', context)


# ============================================================================
# MISSING KNOWLEDGE/NOTE VIEWS - ADDED TO UNBLOCK SERVER STARTUP  
# ============================================================================

class NotebookView(LoginRequiredMixin, TemplateView):
    """User notebook view"""
    template_name = 'CLEAR/notebook.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            user_notes = Note.objects.filter(user=self.request.user)[:20]
        except Exception:
            user_notes = []
        
        context.update({
            'notes': user_notes,
        })
        return context


class NotesListView(LoginRequiredMixin, ListView):
    """List all user notes"""
    template_name = 'CLEAR/notes.html'
    context_object_name = 'notes'
    paginate_by = 20
    
    def get_queryset(self):
        try:
            return Note.objects.filter(user=self.request.user).order_by('-created_at')
        except Exception:
            return []


class NoteCreateView(LoginRequiredMixin, CreateView):
    """Create a new note"""
    model = Note
    template_name = 'CLEAR/note_create.html'
    fields = ['title', 'content', 'tags']
    success_url = reverse_lazy('CLEAR:notes')
    
    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)


class NoteDetailView(LoginRequiredMixin, DetailView):
    """Note detail view"""
    model = Note
    template_name = 'CLEAR/note_detail.html'
    context_object_name = 'note'
    
    def get_queryset(self):
        return Note.objects.filter(user=self.request.user)


# ========== KNOWLEDGE/DOCUMENTATION VIEWS ==========

class NotebookView(LoginRequiredMixin, TemplateView):
    """Main notebook interface"""
    template_name = "CLEAR/knowledge/notebook.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user notes (simplified)
        context.update({
            "recent_notes": [],
            "note_count": 0,
            "categories": ["General", "Projects", "Technical", "Meetings"],
        })
        
        return context


class NotesListView(LoginRequiredMixin, ListView):
    """List all notes"""
    template_name = "CLEAR/knowledge/notes_list.html"
    
    def get_queryset(self):
        # Simplified implementation - no actual Note model yet
        return []
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["notes"] = self.get_queryset()
        return context


class NoteDetailView(LoginRequiredMixin, DetailView):
    """View note details"""
    template_name = "CLEAR/knowledge/note_detail.html"
    
    def get_object(self):
        # Simplified implementation
        return {
            "id": self.kwargs["pk"],
            "title": f"Note {self.kwargs['pk']}",
            "content": "Note content placeholder",
            "created_at": timezone.now(),
        }
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["note"] = self.get_object()
        return context


class NoteCreateView(LoginRequiredMixin, TemplateView):
    """Create new note"""
    template_name = "CLEAR/knowledge/note_create.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["categories"] = ["General", "Projects", "Technical", "Meetings"]
        return context
    
    def post(self, request, *args, **kwargs):
        # Simplified implementation
        request.POST.get("title")
        request.POST.get("content")
        
        # In real implementation, would save to Note model
        return redirect("CLEAR:notes_list")


# ========== FEATURE REQUEST VIEWS ==========

class FeatureRequestListView(LoginRequiredMixin, ListView):
    """List all feature requests with HTMX support"""
    model = FeatureRequest
    template_name = "feature_requests/feature_requests.html"
    context_object_name = 'feature_requests'
    paginate_by = 10
    
    def get_queryset(self):
        queryset = FeatureRequest.objects.select_related('user').prefetch_related('votes').order_by('-vote_count', '-created_at')
        
        # Apply search filter
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Apply status filter
        status_filter = self.request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Apply category filter
        category_filter = self.request.GET.get('category')
        if category_filter:
            queryset = queryset.filter(category=category_filter)
        
        # Apply priority filter
        priority_filter = self.request.GET.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter values to context for maintaining state
        context['search_query'] = self.request.GET.get('search', '')
        context['status_filter'] = self.request.GET.get('status', '')
        context['category_filter'] = self.request.GET.get('category', '')
        context['priority_filter'] = self.request.GET.get('priority', '')
        
        # Add statistics
        stats = self.get_feature_request_stats()
        context.update(stats)
        
        return context
    
    def get_feature_request_stats(self):
        """Calculate feature request statistics"""
        
        current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        
        total_requests = FeatureRequest.objects.count()
        pending_requests = FeatureRequest.objects.filter(status__in=['submitted', 'under_review']).count()
        in_development = FeatureRequest.objects.filter(status='in_development').count()
        completed_requests = FeatureRequest.objects.filter(status='completed').count()
        
        # Calculate changes this month
        total_this_month = FeatureRequest.objects.filter(created_at__gte=current_month).count()
        total_last_month = FeatureRequest.objects.filter(
            created_at__gte=last_month, 
            created_at__lt=current_month
        ).count()
        total_change = total_this_month - total_last_month
        
        return {
            'total_requests': total_requests,
            'pending_requests': pending_requests,
            'in_development': in_development,
            'completed_requests': completed_requests,
            'total_change': total_change,
        }
    
    def render_to_response(self, context, **response_kwargs):
        # If this is an HTMX request, return only the list component
        if self.request.headers.get('HX-Request'):
            template_name = 'components/feature_requests/request_list.html'
            return render(self.request, template_name, context, **response_kwargs)
        
        return super().render_to_response(context, **response_kwargs)


@login_required
@require_http_methods(["POST"])
def feature_request_filter(request):
    """HTMX endpoint for filtering feature requests"""
    view = FeatureRequestListView()
    view.request = request
    view.object_list = view.get_queryset()
    
    # Get page object for pagination
    paginator = Paginator(view.object_list, view.paginate_by)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    context = view.get_context_data(object_list=page_obj)
    context['page_obj'] = page_obj
    
    return render(request, 'components/feature_requests/request_list.html', context)


@login_required
@require_http_methods(["GET"])
def feature_request_stats(request):
    """HTMX endpoint for feature request statistics"""
    view = FeatureRequestListView()
    stats = view.get_feature_request_stats()
    
    # Calculate additional stats for header
    context = {
        'pending_requests': stats['pending_requests'],
        'in_development': stats['in_development'], 
        'completed_requests': stats['completed_requests'],
    }
    
    return render(request, 'components/feature_requests/stats_header.html', context)


@login_required
@require_http_methods(["GET"])
def feature_request_stats_cards(request):
    """HTMX endpoint for feature request statistics cards"""
    view = FeatureRequestListView()
    stats = view.get_feature_request_stats()
    
    # Calculate additional statistics
    under_review_count = FeatureRequest.objects.filter(status='under_review').count()
    
    # Calculate average review time
    
    avg_review_days = FeatureRequest.objects.filter(
        status__in=['approved', 'rejected', 'in_development', 'completed'],
        reviewed_at__isnull=False
    ).aggregate(
        avg_days=Avg(
            (timezone.now() - F('created_at')).total_seconds() / 86400
        )
    )['avg_days'] or 0
    
    # Get upcoming releases count
    upcoming_release = FeatureRequest.objects.filter(
        status='in_development',
        target_release__isnull=False
    ).count()
    
    # Get completed this quarter
    quarter_start = timezone.now().replace(month=((timezone.now().month - 1) // 3) * 3 + 1, day=1)
    completed_this_quarter = FeatureRequest.objects.filter(
        status='completed',
        updated_at__gte=quarter_start
    ).count()
    
    context = {
        'stats': {
            'total_requests': stats['total_requests'],
            'total_change': stats['total_change'],
            'under_review': under_review_count,
            'avg_review_days': int(avg_review_days),
            'in_development': stats['in_development'],
            'upcoming_release': upcoming_release,
            'completed': stats['completed_requests'],
            'completed_this_quarter': completed_this_quarter,
        }
    }
    
    return render(request, 'components/feature_requests/stats_cards.html', context)


@login_required
@require_http_methods(["GET"])
def feature_request_create_form(request):
    """HTMX endpoint to show feature request creation form"""
    return render(request, 'components/feature_requests/create_modal.html')


@login_required
@require_http_methods(["POST"])
def feature_request_create(request):
    """HTMX endpoint to create a new feature request"""
    try:
        # Validate form data
        title = request.POST.get('title', '').strip()
        category = request.POST.get('category', '').strip()
        priority = request.POST.get('priority', '').strip()
        description = request.POST.get('description', '').strip()
        use_case = request.POST.get('use_case', '').strip()
        
        errors = {}
        
        if not title:
            errors['title'] = 'Title is required'
        elif len(title) > 255:
            errors['title'] = 'Title must be less than 255 characters'
        
        if not category:
            errors['category'] = 'Category is required'
        elif category not in dict(FeatureRequest._meta.get_field('category').choices):
            errors['category'] = 'Invalid category selected'
        
        if not priority:
            errors['priority'] = 'Priority is required'
        elif priority not in dict(FeatureRequest._meta.get_field('priority').choices):
            errors['priority'] = 'Invalid priority selected'
        
        if not description:
            errors['description'] = 'Description is required'
        
        if errors:
            # Return form with errors
            context = {
                'errors': errors,
                'form_data': {
                    'title': title,
                    'category': category,
                    'priority': priority,
                    'description': description,
                    'use_case': use_case,
                }
            }
            return render(request, 'components/feature_requests/create_modal.html', context)
        
        # Create the feature request
        feature_request = FeatureRequest.objects.create(
            user=request.user,
            title=title,
            description=description,
            use_case=use_case,
            category=category,
            priority=priority,
            status='submitted'
        )
        
        # Close modal and show success message
        response = HttpResponse('')
        response['HX-Trigger'] = json.dumps({
            'featureRequestCreated': {
                'id': str(feature_request.id),
                'title': feature_request.title
            },
            'refreshFeatureRequestList': True,
            'showSuccessMessage': 'Feature request submitted successfully!'
        })
        
        return response
        
    except Exception as e:
        logger.error(f"Error creating feature request: {str(e)}")
        context = {
            'error': 'An error occurred while submitting your request. Please try again.',
            'form_data': {
                'title': request.POST.get('title', ''),
                'category': request.POST.get('category', ''),
                'priority': request.POST.get('priority', ''),
                'description': request.POST.get('description', ''),
                'use_case': request.POST.get('use_case', ''),
            }
        }
        return render(request, 'components/feature_requests/create_modal.html', context)


@login_required
@require_http_methods(["GET"])
def feature_request_close_modal(request):
    """HTMX endpoint to close the feature request modal"""
    return HttpResponse('')


@login_required
@require_http_methods(["POST"])
def feature_request_vote(request, request_id):
    """HTMX endpoint for voting on feature requests"""
    try:
        feature_request = get_object_or_404(FeatureRequest, id=request_id)
        vote_type = request.POST.get('vote_type', 'upvote')
        
        # Check if user has already voted
        existing_vote = FeatureVote.objects.filter(
            feature_request=feature_request,
            user=request.user
        ).first()
        
        if existing_vote:
            if existing_vote.vote_type == vote_type:
                # Remove vote if clicking same type
                existing_vote.delete()
                feature_request.vote_count = F('vote_count') - 1
            else:
                # Change vote type
                existing_vote.vote_type = vote_type
                existing_vote.save()
                # No change to vote_count since it's changing from one to another
        else:
            # Create new vote
            FeatureVote.objects.create(
                feature_request=feature_request,
                user=request.user,
                vote_type=vote_type
            )
            feature_request.vote_count = F('vote_count') + 1
        
        feature_request.save()
        feature_request.refresh_from_db()
        
        # Return updated vote count
        context = {'request': feature_request}
        return render(request, 'components/feature_requests/vote_count.html', context)
        
    except Exception as e:
        logger.error(f"Error voting on feature request: {str(e)}")
        return HttpResponse('<span class="text-red-500">Error voting</span>')


class FeatureRequestDetailView(LoginRequiredMixin, DetailView):
    """View feature request details with HTMX support"""
    model = FeatureRequest
    template_name = "feature_requests/feature_request_detail.html"
    context_object_name = 'feature_request'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user's vote if any
        user_vote = None
        if self.request.user.is_authenticated:
            try:
                vote = FeatureVote.objects.get(
                    feature_request=self.object,
                    user=self.request.user
                )
                user_vote = vote.vote_type
            except FeatureVote.DoesNotExist:
                pass
        
        context['user_vote'] = user_vote
        
        # Get related feature requests
        related_requests = FeatureRequest.objects.filter(
            category=self.object.category
        ).exclude(id=self.object.id).order_by('-vote_count')[:3]
        
        context['related_requests'] = related_requests
        
        return context


@login_required
@require_http_methods(["GET"])
def feature_request_share(request, request_id):
    """HTMX endpoint for sharing feature requests"""
    feature_request = get_object_or_404(FeatureRequest, id=request_id)
    
    share_url = request.build_absolute_uri(
        reverse('CLEAR:feature_request_detail', kwargs={'pk': request_id})
    )
    
    context = {
        'feature_request': feature_request,
        'share_url': share_url,
    }
    
    return render(request, 'components/feature_requests/share_modal.html', context)


# Update existing placeholder views
class FeatureRequestCreateView(LoginRequiredMixin, CreateView):
    """Create new feature request (fallback for non-HTMX)"""
    model = FeatureRequest
    template_name = "feature_requests/feature_request_create.html"
    fields = ['title', 'description', 'use_case', 'category', 'priority']
    success_url = reverse_lazy('CLEAR:feature_requests')
    
    def form_valid(self, form):
        form.instance.user = self.request.user
        form.instance.status = 'submitted'
        return super().form_valid(form)
