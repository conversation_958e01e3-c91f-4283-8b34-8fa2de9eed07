"""
Dashboard views for the CLEAR application.

This module contains views for the main dashboard and related functionality,
with enhanced error handling.
"""


import logging
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.db import models
from ..models import (
        from ..models import TimeEntry
    from ..forms import QuickTimeEntryForm, TimeEntryForm
    from django.shortcuts import redirect
    from ..forms import TimeEntryForm
    from ..forms import QuickTimeEntryForm
    import math
    from decimal import Decimal
    from django.shortcuts import get_object_or_404
    from django.shortcuts import get_object_or_404, redirect
    import csv
    from django.http import HttpResponse


logger = logging.getLogger(__name__)




    ChatMessage,
    Conflict,
    Project,
    ProjectActivity,
    Task,
    TimeEntry,
    Utility,
    WhisperMessage,
)

logger = logging.getLogger(__name__)

@login_required
def dashboard(request):
    """
    Main dashboard view - renders the updated React-style dashboard with error handling.

    This view has been fully migrated with 100% visual parity to the original React dashboard.
    The dashboard includes:
    - Timer Controls and Active Timer
    - Team Chat section
    - Timesheet Summary section
    - Tasks List
    - Meetings List
    - My Projects section

    All components use HTMX for dynamic content loading and real-time updates.
    try:
        # Get user's projects for timer components
        user_projects = Project.objects.filter(
            Q(manager_id=request.user.id) | 
            Q(coordinator_id=str(request.user.id)) | 
            Q(egis_project_manager=request.user.username)
        ).order_by('name')[:20]  # Limit to most recent 20 projects

        # Calculate initial stats for welcome hero
        today = timezone.now().date()
        tasks_today = Task.objects.filter(
            assigned_to=request.user,
            due_date=today,
            status__in=['pending', 'in_progress']
        ).count()
        
        # Get hours this week
        week_start = today - timedelta(days=today.weekday())
        week_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=today
        )
        
        total_minutes = week_entries.aggregate(Sum('duration_minutes'))['duration_minutes__sum'] or 0
        hours_this_week = round(total_minutes / 60.0, 1)
        
        # Calculate on track percentage
        expected_hours = (today.weekday() + 1) * 8  # 8 hours per day
        on_track_percentage = min(100, round((hours_this_week / expected_hours) * 100, 0)) if expected_hours > 0 else 0

        context = {
            'now': timezone.now(),
            'today': timezone.now().date(),
            'user_projects': user_projects,
            'active_projects_count': user_projects.count(),
            'tasks_today': tasks_today,
            'hours_this_week': hours_this_week,
            'on_track_percentage': int(on_track_percentage),
        }
        return render(request, 'CLEAR/dashboard.html', context)
    except Exception as e:
        logger.error(f"Error in dashboard view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': f"An error occurred while loading the dashboard: {str(e)}"
        }, status=500)

@login_required
def dashboard_stats(request):
    """Get dashboard statistics (HTMX endpoint) with error handling."""
    try:
        user = request.user

        # Recalculate stats
        user_projects = Project.objects.filter(
            Q(manager_id=user.id) | Q(coordinator_id=str(user.id))
        )

        stats = {
            'total_projects': user_projects.count(),
            'total_utilities': Utility.objects.filter(
                project__in=user_projects
            ).count(),
            'active_conflicts': Conflict.objects.filter(
                project__in=user_projects,
                status__in=['open', 'pending']
            ).count(),
            'pending_tasks': Task.objects.filter(
                assigned_to=user,
                status__in=['pending', 'in_progress']
            ).count(),
            'unread_notifications': getattr(user, 'notifications', None) and user.notifications.filter(is_read=False).count() or 0,
            'active_whispers': WhisperMessage.objects.filter(
                recipient=user,
                is_read=False
            ).count(),
            'recent_messages': ChatMessage.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).count(),
        }

        if request.headers.get('HX-Request'):
            return render(request, 'components/dashboard_stats.html', {'stats': stats})
        else:
            return JsonResponse(stats)
    except Exception as e:
        logger.error(f"Error in dashboard_stats view: {str(e)}")
        if request.headers.get('HX-Request'):
            return render(request, 'components/error_message.html', {
                'error_message': f"An error occurred while loading dashboard statistics: {str(e)}"
            }, status=500)
        else:
            return JsonResponse({
                'error': f"An error occurred while loading dashboard statistics: {str(e)}"
            }, status=500)

@login_required
def ai_communication_dashboard(request):
    """AI communication interface with error handling."""
    try:
        context = {
            "user": request.user,
            "ai_enabled": True,
            "recent_queries": [],
        }
        return render(request, "CLEAR/ai_dashboard.html", context)
    except Exception as e:
        logger.error(f"Error in ai_communication_dashboard view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': 'An error occurred while loading the AI dashboard: ' + str(e)
        }, status=500)

@login_required
def my_time_partial(request):
    """My Time component for dashboard - shows user's timesheet data."""
    try:

        
        # Get current week timesheet data for the user
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        
        # Get time entries for current week
        week_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=week_end
        )
        
        # Calculate totals
        totals = week_entries.aggregate(
            total_minutes=Sum('duration_minutes'),
            billable_minutes=Sum('duration_minutes', filter=Q(is_billable=True)),
            overhead_minutes=Sum('duration_minutes', filter=Q(is_billable=False))
        )
        
        total_minutes = totals['total_minutes'] or 0
        billable_minutes = totals['billable_minutes'] or 0
        overhead_minutes = totals['overhead_minutes'] or 0
        
        # Convert to hours
        total_hours = round(total_minutes / 60.0, 1)
        billable_hours = round(billable_minutes / 60.0, 1)
        overhead_hours = round(overhead_minutes / 60.0, 1)
        
        # Calculate week progress (assuming 40 hour work week)
        week_progress = min(round((total_hours / 40.0) * 100, 1), 100)
        
        # Get today's entries
        today_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date=today
        ).select_related('project').order_by('-start_time')[:3]
        
        context = {
            'total_hours': total_hours,
            'billable_hours': billable_hours,
            'overhead_hours': overhead_hours,
            'week_progress': week_progress,
            'today_entries': today_entries
        }
        
        return render(request, 'components/dashboard/my_time.html', context)
    except Exception as e:
        logger.error(f"Error in my_time_partial view: {str(e)}")
        return render(request, 'components/error_message.html', {
            'error_message': f"Error loading time data: {str(e)}"
        }, status=500)

@login_required
def my_tasks_partial(request):
    """My Tasks component for dashboard - shows user's assigned tasks."""
    try:
        # Get tasks assigned to the current user
        all_tasks = Task.objects.filter(
            assigned_to=request.user,
            status__in=['pending', 'in_progress']
        )
        
        # Get priority counts
        high_priority_count = all_tasks.filter(priority__in=['high', 'urgent']).count()
        medium_priority_count = all_tasks.filter(priority='medium').count()
        low_priority_count = all_tasks.filter(priority='low').count()
        
        # Get top 5 tasks ordered by priority and due date
        my_tasks = all_tasks.select_related('project').order_by(
            models.Case(
                models.When(priority='urgent', then=0),
                models.When(priority='high', then=1),
                models.When(priority='medium', then=2),
                models.When(priority='low', then=3),
                default=4,
                output_field=models.IntegerField()
            ),
            'due_date'
        )[:5]
        
        tasks_count = all_tasks.count()
        
        context = {
            'my_tasks': my_tasks,
            'tasks_count': tasks_count,
            'high_priority_count': high_priority_count,
            'medium_priority_count': medium_priority_count,
            'low_priority_count': low_priority_count
        }
        
        return render(request, 'components/dashboard/my_tasks.html', context)
    except Exception as e:
        logger.error(f"Error in my_tasks_partial view: {str(e)}")
        return render(request, 'components/error_message.html', {
            'error_message': f"Error loading tasks: {str(e)}"
        }, status=500)

@login_required
def my_meetings_partial(request):
    """My Meetings component for dashboard - shows user's upcoming meetings."""
    try:
        # TODO: Replace with actual Meeting model query when available
        # For now, using sample data
        context = {
            'my_meetings': [],  # Will be populated from Meeting model
            'meetings_count': 0,
            'next_meeting': None
        }
        
        return render(request, 'components/dashboard/my_meetings.html', context)
    except Exception as e:
        logger.error(f"Error in my_meetings_partial view: {str(e)}")
        return render(request, 'components/error_message.html', {
            'error_message': f"Error loading meetings: {str(e)}"
        }, status=500)

@login_required
def my_projects_partial(request):
    """My Projects component for dashboard - shows user's assigned projects."""
    try:
        # Get all projects where user is manager, coordinator, or team member
        all_projects = Project.objects.filter(
            Q(manager_id=request.user.id) | 
            Q(coordinator_id=str(request.user.id)) | 
            Q(egis_project_manager=request.user.username)
        )
        
        # Count projects by status
        at_risk_count = all_projects.filter(
            Q(rag_status='Red') | Q(project_health_rag='Red')
        ).count()
        
        # Calculate over budget count (projects where current cost > contract amount)
        over_budget_count = all_projects.filter(
            current_cost__gt=models.F('contract_amount')
        ).exclude(
            contract_amount__isnull=True
        ).count()
        
        on_track_count = all_projects.filter(
            Q(rag_status='Green') | Q(project_health_rag='Green')
        ).count()
        
        # Get top 4 projects for display
        my_projects = all_projects.order_by('-updated_at')[:4]
        
        projects_count = all_projects.count()
        
        # Add computed fields for each project
        for project in my_projects:
            project.utility_count = project.utilities.count() if hasattr(project, 'utilities') else 0
            project.conflict_count = project.conflicts.filter(status__in=['open', 'pending']).count() if hasattr(project, 'conflicts') else 0
            project.task_count = project.tasks.filter(status__in=['pending', 'in_progress']).count() if hasattr(project, 'tasks') else 0
            
            # Calculate progress based on tasks
            total_tasks = project.tasks.count() if hasattr(project, 'tasks') else 0
            completed_tasks = project.tasks.filter(status='completed').count() if hasattr(project, 'tasks') else 0
            project.progress = int((completed_tasks / total_tasks * 100)) if total_tasks > 0 else 0
        
        context = {
            'my_projects': my_projects,
            'projects_count': projects_count,
            'at_risk_count': at_risk_count,
            'over_budget_count': over_budget_count,
            'on_track_count': on_track_count,
            'recent_project_activity': None  # TODO: Add recent activity
        }
        
        return render(request, 'components/dashboard/my_projects.html', context)
    except Exception as e:
        logger.error(f"Error in my_projects_partial view: {str(e)}")
        return render(request, 'components/error_message.html', {
            'error_message': f"Error loading projects: {str(e)}"
        }, status=500)

@login_required
def team_chat_partial(request):
    """Team Chat component for dashboard - shows team communication."""
    try:
        # Get recent team messages
        messages = ChatMessage.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).select_related('user').order_by('-created_at')[:50]
        
        # Get online users (simplified - in production would use Django Channels)
        online_users = []
        online_users_count = len(online_users)
        
        # Prepare messages with additional fields
        for message in messages:
            message.is_own = (message.user == request.user)
            message.user_initials = message.user.get_initials() if hasattr(message.user, 'get_initials') else message.user.username[:2].upper()
            message.time_ago = timezone.now() - message.created_at
            
            # Format time_ago
            if message.time_ago.total_seconds() < 60:
                message.time_ago = "just now"
            elif message.time_ago.total_seconds() < 3600:
                minutes = int(message.time_ago.total_seconds() / 60)
                message.time_ago = f"{minutes}m ago"
            elif message.time_ago.total_seconds() < 86400:
                hours = int(message.time_ago.total_seconds() / 3600)
                message.time_ago = f"{hours}h ago"
            else:
                days = int(message.time_ago.total_seconds() / 86400)
                message.time_ago = f"{days}d ago"
        
        context = {
            'messages': messages,
            'online_users': online_users,
            'online_users_count': online_users_count
        }
        
        return render(request, 'components/dashboard/team_chat.html', context)
    except Exception as e:
        logger.error(f"Error in team_chat_partial view: {str(e)}")
        return render(request, 'components/error_message.html', {
            'error_message': f"Error loading team chat: {str(e)}"
        }, status=500)

@login_required
def task_complete(request, task_id):
    """Toggle task completion status (HTMX endpoint)."""
    if request.method == 'POST':
        try:
            task = Task.objects.get(id=task_id, assigned_to=request.user)
            
            # Toggle task status
            if task.status == 'completed':
                task.status = 'pending'
                task.completion_date = None
            else:
                task.status = 'completed'
                task.completion_date = timezone.now()
            
            task.save()
            
            # Log the activity
            ProjectActivity.objects.create(
                project=task.project,
                user=request.user,
                action_type='task_completed' if task.status == 'completed' else 'task_updated',
                description=f"{'Completed' if task.status == 'completed' else 'Reopened'} task: {task.title}",
                target_type='task',
                target_id=str(task.id)
            )
            
            # Return refreshed my_tasks component
            return my_tasks_partial(request)
        except Task.DoesNotExist:
            return HttpResponse("Task not found", status=404)
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse("Method not allowed", status=405)


# ========== TIMESHEET MANAGEMENT VIEWS ==========

@login_required
def timesheet_view(request):
    """Main timesheet view with HTMX support"""
    
    try:
        # Get current week dates
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        
        # Get time entries for current week
        week_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=week_end
        ).select_related('project', 'work_type').order_by('-start_time')
        
        # Calculate week totals
        week_totals = week_entries.aggregate(
            total_minutes=Sum('duration_minutes'),
            billable_minutes=Sum('duration_minutes', filter=Q(is_billable=True))
        )
        
        total_hours = (week_totals['total_minutes'] or 0) / 60.0
        billable_hours = (week_totals['billable_minutes'] or 0) / 60.0
        
        # Get user's projects for forms
        user_projects = Project.objects.filter(
            Q(manager_id=request.user.id) | 
            Q(coordinator_id=str(request.user.id)) | 
            Q(egis_project_manager=request.user.username)
        ).order_by('name')
        
        # Initialize forms
        time_entry_form = TimeEntryForm(user=request.user)
        quick_entry_form = QuickTimeEntryForm(user=request.user)
        
        context = {
            'week_entries': week_entries,
            'week_start': week_start,
            'week_end': week_end,
            'total_hours': round(total_hours, 1),
            'billable_hours': round(billable_hours, 1),
            'overhead_hours': round(total_hours - billable_hours, 1),
            'week_progress': min(100, round((total_hours / 40) * 100, 1)),
            'time_entry_form': time_entry_form,
            'quick_entry_form': quick_entry_form,
            'user_projects': user_projects,
            'today': today,
        }
        
        return render(request, 'CLEAR/timesheet.html', context)
        
    except Exception as e:
        logger.error(f"Error in timesheet_view: {str(e)}")
        return render(request, 'error/500.html', {
            'error_message': f"Error loading timesheet: {str(e)}"
        }, status=500)


@login_required
def save_timesheet(request):
    """Save a new time entry via HTMX"""

    
    if request.method == 'POST':
        try:
            form = TimeEntryForm(user=request.user, data=request.POST)
            
            if form.is_valid():
                time_entry = form.save()
                
                # Return the new entry as HTML
                if request.htmx:
                    response = render(request, 'components/timesheet/time_entry_row.html', {
                        'entry': time_entry
                    })
                    response['HX-Trigger'] = 'timesheetUpdated'
                    return response
                else:
                    return redirect('CLEAR:timesheet')
            else:
                # Return form with errors
                if request.htmx:
                    return render(request, 'components/timesheet/time_entry_form.html', {
                        'form': form
                    }, status=400)
                else:
                    # For non-HTMX requests, redirect back with errors
                    return render(request, 'CLEAR/timesheet.html', {
                        'time_entry_form': form,
                        'form_errors': True
                    })
        
        except Exception as e:
            logger.error(f"Error saving timesheet entry: {str(e)}")
            if request.htmx:
                return HttpResponse(
                    f'<div class="alert alert-danger">Error: {str(e)}</div>',
                    status=500
                )
            else:
                return redirect('CLEAR:timesheet')
    
    return HttpResponse('Method not allowed', status=405)


@login_required
def quick_time_entry_save(request):
    """Save a quick time entry via HTMX"""
    
    if request.method == 'POST':
        try:
            form = QuickTimeEntryForm(user=request.user, data=request.POST)
            
            if form.is_valid():
                # Create TimeEntry from quick form data
                TimeEntry.objects.create(
                    user=request.user,
                    project=form.cleaned_data['project'],
                    description=form.cleaned_data['description'],
                    duration_hours=form.cleaned_data['hours'],
                    start_time=timezone.datetime.combine(
                        form.cleaned_data['date'],
                        timezone.now().time()
                    ),
                    is_billable=True  # Default to billable
                )
                
                if request.htmx:
                    # Return success message and refresh timesheet
                    response = HttpResponse(
                        '<div class="alert alert-success">Time entry saved successfully!</div>'
                    )
                    response['HX-Trigger'] = 'timesheetUpdated'
                    return response
                else:
                    return redirect('CLEAR:timesheet')
            else:
                if request.htmx:
                    return render(request, 'components/timesheet/quick_entry_form.html', {
                        'form': form
                    }, status=400)
        
        except Exception as e:
            logger.error(f"Error saving quick time entry: {str(e)}")
            if request.htmx:
                return HttpResponse(
                    f'<div class="alert alert-danger">Error: {str(e)}</div>',
                    status=500
                )
    
    return HttpResponse('Method not allowed', status=405)


@login_required
def update_time_entry(request):
    """Update an existing time entry via HTMX"""

    
    if request.method == 'POST':
        try:
            entry_id = request.POST.get('entry_id')
            field = request.POST.get('field')
            value = request.POST.get('value')
            
            if not all([entry_id, field, value]):
                return HttpResponse('Missing required fields', status=400)
            
            # Get the time entry
            time_entry = get_object_or_404(
                TimeEntry, 
                id=entry_id, 
                user=request.user
            )
            
            # Update the specific field
            if field == 'duration_hours':
                # Apply decimal hours formatting
                hours_float = float(value)
                if hours_float == int(hours_float):
                    formatted_hours = Decimal(f"{int(hours_float)}.0")
                else:
                    decimal_places = len(str(hours_float).split('.')[-1]) if '.' in str(hours_float) else 0
                    if decimal_places > 1:
                        rounded_up = math.ceil(hours_float * 10) / 10
                        formatted_hours = Decimal(f"{rounded_up:.1f}")
                    else:
                        formatted_hours = Decimal(f"{hours_float:.1f}")
                
                time_entry.duration_hours = formatted_hours
                
            elif field == 'description':
                time_entry.description = value
            elif field == 'is_billable':
                time_entry.is_billable = value.lower() == 'true'
            elif field == 'notes':
                time_entry.notes = value
            
            time_entry.save()
            
            # Return updated row
            return render(request, 'components/timesheet/time_entry_row.html', {
                'entry': time_entry
            })
        
        except Exception as e:
            logger.error(f"Error updating time entry: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse('Method not allowed', status=405)


@login_required
def delete_time_entry(request, entry_id):
    """Delete a time entry via HTMX"""
    
    if request.method == 'POST':
        try:
            time_entry = get_object_or_404(
                TimeEntry,
                id=entry_id,
                user=request.user
            )
            
            time_entry.delete()
            
            if request.htmx:
                # Return empty response to remove the row
                response = HttpResponse('')
                response['HX-Trigger'] = 'timesheetUpdated'
                return response
            else:
                return redirect('CLEAR:timesheet')
        
        except Exception as e:
            logger.error(f"Error deleting time entry: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse('Method not allowed', status=405)


@login_required
def timesheet_entries_partial(request):
    """Return timesheet entries partial for HTMX updates"""
    
    try:
        # Get current week dates
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=6)
        
        # Get time entries for current week
        week_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=week_start,
            start_time__date__lte=week_end
        ).select_related('project', 'work_type').order_by('-start_time')
        
        return render(request, 'components/timesheet/entries_list.html', {
            'week_entries': week_entries
        })
        
    except Exception as e:
        logger.error(f"Error loading timesheet entries: {str(e)}")
        return HttpResponse(f"Error: {str(e)}", status=500)


@login_required
def edit_time_entry_field(request, entry_id):
    """Return editable field for inline editing"""
    
    try:
        field = request.GET.get('field')
        time_entry = get_object_or_404(
            TimeEntry,
            id=entry_id,
            user=request.user
        )
        
        return render(request, 'components/timesheet/editable_field.html', {
            'entry': time_entry,
            'field': field
        })
        
    except Exception as e:
        logger.error(f"Error loading editable field: {str(e)}")
        return HttpResponse(f"Error: {str(e)}", status=500)


@login_required
def edit_time_entry_modal(request, entry_id):
    """Return edit modal content for time entry"""
    
    try:
        time_entry = get_object_or_404(
            TimeEntry,
            id=entry_id,
            user=request.user
        )
        
        form = TimeEntryForm(user=request.user, instance=time_entry)
        
        return render(request, 'components/timesheet/edit_modal_content.html', {
            'form': form,
            'entry': time_entry
        })
        
    except Exception as e:
        logger.error(f"Error loading edit modal: {str(e)}")
        return HttpResponse(f"Error: {str(e)}", status=500)


@login_required
def export_timesheet(request):
    """Export timesheet data as CSV"""

    
    try:
        # Get date range from request
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        if not start_date or not end_date:
            # Default to current week
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
        
        # Get time entries
        time_entries = TimeEntry.objects.filter(
            user=request.user,
            start_time__date__gte=start_date,
            start_time__date__lte=end_date
        ).select_related('project', 'work_type').order_by('start_time')
        
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="timesheet_{start_date}_{end_date}.csv"'
        
        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Project', 'Work Type', 'Description', 
            'Duration (Hours)', 'Billable', 'Notes'
        ])
        
        for entry in time_entries:
            writer.writerow([
                entry.start_time.date(),
                entry.project.name if entry.project else 'N/A',
                entry.work_type.name if entry.work_type else 'N/A',
                entry.description,
                entry.duration_hours,
                'Yes' if entry.is_billable else 'No',
                entry.notes or ''
            ])
        
        return response
        
    except Exception as e:
        logger.error(f"Error exporting timesheet: {str(e)}")
        return HttpResponse(f"Error: {str(e)}", status=500)


@login_required 
def submit_timesheet(request):
    """Submit timesheet for approval"""
    # TODO: Implement timesheet submission workflow
    if request.method == 'POST':
        try:
            # This would typically create a TimesheetEntry record
            # and change the status of time entries to 'submitted'
            return HttpResponse(
                '<div class="alert alert-success">Timesheet submitted for approval!</div>'
            )
        except Exception as e:
            logger.error(f"Error submitting timesheet: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse('Method not allowed', status=405)

@login_required
def send_team_message(request):
    """Send a team chat message (HTMX endpoint)."""
    if request.method == 'POST':
        try:
            content = request.POST.get('content', '').strip()
            if not content:
                return HttpResponse("Message content is required", status=400)
            
            # Create the message
            message = ChatMessage.objects.create(
                user=request.user,
                content=content[:500]  # Limit to 500 characters
            )
            
            # Prepare message for display
            message.is_own = True
            message.user_initials = request.user.get_initials() if hasattr(request.user, 'get_initials') else request.user.username[:2].upper()
            message.time_ago = "just now"
            
            # Return just the new message HTML
            return render(request, 'components/dashboard/message_item.html', {
                'message': message
            })
        except Exception as e:
            logger.error(f"Error sending team message: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse("Method not allowed", status=405)

@login_required
def task_quick_add(request):
    """Quick add a task (HTMX endpoint)."""
    if request.method == 'POST':
        try:
            title = request.POST.get('title', '').strip()
            priority = request.POST.get('priority', 'medium').lower()
            
            if not title:
                return HttpResponse("Task title is required", status=400)
            
            # Get the user's most recent project to assign the task to
            recent_project = Project.objects.filter(
                Q(manager_id=request.user.id) | 
                Q(coordinator_id=str(request.user.id)) | 
                Q(egis_project_manager=request.user.username)
            ).order_by('-updated_at').first()
            
            # Create the task
            task = Task.objects.create(
                title=title,
                project=recent_project,  # Assign to most recent project or None
                assigned_to=request.user,
                created_by=request.user,
                status='pending',
                priority=priority,
                due_date=timezone.now().date() + timedelta(days=1)  # Default to tomorrow
            )
            
            # Log the activity if project exists
            if recent_project:
                ProjectActivity.objects.create(
                    project=recent_project,
                    user=request.user,
                    action_type='task_created',
                    description=f"Created task: {task.title}",
                    target_type='task',
                    target_id=str(task.id)
                )
            
            # Return refreshed my_tasks component
            return my_tasks_partial(request)
        except Exception as e:
            logger.error(f"Error creating quick task: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse("Method not allowed", status=405)

@login_required
def task_delete(request, task_id):
    """Delete a task (HTMX endpoint)."""
    if request.method == 'DELETE':
        try:
            task = Task.objects.get(id=task_id, assigned_to=request.user)
            task_title = task.title
            task_project = task.project
            
            # Delete the task
            task.delete()
            
            # Log the activity if project exists
            if task_project:
                ProjectActivity.objects.create(
                    project=task_project,
                    user=request.user,
                    action_type='task_updated',
                    description=f"Deleted task: {task_title}",
                    target_type='task',
                    target_id=str(task_id)
                )
            
            # Return refreshed my_tasks component
            return my_tasks_partial(request)
        except Task.DoesNotExist:
            return HttpResponse("Task not found", status=404)
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {str(e)}")
            return HttpResponse(f"Error: {str(e)}", status=500)
    
    return HttpResponse("Method not allowed", status=405)