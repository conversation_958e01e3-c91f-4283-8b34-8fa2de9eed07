"""
Base imports for all view modules.
This file aggregates common imports to reduce redundancy across view files.
"""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
import json

# Django imports
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout, get_user_model
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.paginator import Paginator
from django.db import transaction, connection
from django.db.models import Q, F, Count, Sum, Avg, Max, Min
from django.http import HttpResponse, JsonResponse, Http404
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView, <PERSON>View, DetailView, <PERSON><PERSON><PERSON>iew, UpdateView, DeleteView

# Import all models
from ..models import *

# Import services
try:
    from ..services.analytics_engine import AnalyticsEngine
except ImportError:
    AnalyticsEngine = None

try:
    from ..services.entity_chaining import EntityChainingService
except ImportError:
    EntityChainingService = None

logger = logging.getLogger(__name__)
