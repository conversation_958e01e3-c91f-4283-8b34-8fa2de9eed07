"""
Business Analytics Views

This module contains views related to business analytics, reporting, and data visualization.
"""


import logging
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils import timezone
from django.views.generic import TemplateView
from ..services.analytics_engine import AnalyticsEngine
from .imports_base import *


logger = logging.getLogger(__name__)



class BusinessAnalyticsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'analytics/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize analytics engine
        analytics = AnalyticsEngine(self.request.user)
        
        # Get time period from request or default to last 30 days
        period = self.request.GET.get('period', '30d')
        
        # Set date range based on period
        if period == '7d':
            start_date = timezone.now() - timedelta(days=7)
            context['period_name'] = 'Last 7 Days'
        elif period == '30d':
            start_date = timezone.now() - timedelta(days=30)
            context['period_name'] = 'Last 30 Days'
        elif period == '90d':
            start_date = timezone.now() - timedelta(days=90)
            context['period_name'] = 'Last 90 Days'
        elif period == 'ytd':
            start_date = timezone.datetime(timezone.now().year, 1, 1)
            context['period_name'] = 'Year to Date'
        else:
            start_date = timezone.now() - timedelta(days=30)
            context['period_name'] = 'Last 30 Days'
            
        # Get analytics data
        metrics = analytics.get_business_metrics(start_date)
        
        # Format metrics for display
        context['metrics'] = metrics
        context['current_period'] = period
        
        # Get projects for filtering
        context['projects'] = Project.objects.filter(
            Q(members=self.request.user) | 
            Q(created_by=self.request.user)
        ).distinct()
        
        # Get organizations for filtering
        if self.request.user.is_staff:
            context['organizations'] = Organization.objects.all()
        else:
            context['organizations'] = Organization.objects.filter(
                members=self.request.user
            ).distinct()
            
        # Add chart data
        context['project_performance_data'] = analytics.get_project_performance_data(start_date)
        context['resource_utilization_data'] = analytics.get_resource_utilization_data(start_date)
        context['financial_metrics_data'] = analytics.get_financial_metrics_data(start_date)
        
        return context

@login_required
def business_metrics_partial(request):
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        
    # Get analytics data
    metrics = analytics.get_business_metrics(start_date)
    
    # Apply filters if provided
    project_id = request.GET.get('project')
    if project_id:
        metrics = analytics.filter_metrics_by_project(metrics, project_id)
        
    organization_id = request.GET.get('organization')
    if organization_id:
        metrics = analytics.filter_metrics_by_organization(metrics, organization_id)
    
    context = {
        'metrics': metrics,
        'period_name': period_name,
        'current_period': period
    }
    
    return render(request, 'analytics/partials/metrics_cards.html', context)

@login_required
def analytics_chart_data(request, chart_type=None):
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
    else:
        start_date = timezone.now() - timedelta(days=30)
    
    # Get chart type from request
    chart_type = chart_type or request.GET.get('chart', 'project_performance')
    
    # Get chart data based on type
    if chart_type == 'project_performance':
        data = analytics.get_project_performance_data(start_date)
    elif chart_type == 'resource_utilization':
        data = analytics.get_resource_utilization_data(start_date)
    elif chart_type == 'financial_metrics':
        data = analytics.get_financial_metrics_data(start_date)
    elif chart_type == 'task_completion':
        data = analytics.get_task_completion_data(start_date)
    elif chart_type == 'conflict_resolution':
        data = analytics.get_conflict_resolution_data(start_date)
    else:
        data = {}
    
    # Apply filters if provided
    project_id = request.GET.get('project')
    if project_id:
        data = analytics.filter_chart_data_by_project(data, project_id, chart_type)
        
    organization_id = request.GET.get('organization')
    if organization_id:
        data = analytics.filter_chart_data_by_organization(data, organization_id, chart_type)
    
    return JsonResponse(data)

@login_required
def analytics_export(request):
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    
    # Get export format
    export_format = request.GET.get('format', 'csv')
    
    # Get metrics data
    metrics = analytics.get_business_metrics(start_date)
    
    # Apply filters if provided
    project_id = request.GET.get('project')
    if project_id:
        metrics = analytics.filter_metrics_by_project(metrics, project_id)
        
    organization_id = request.GET.get('organization')
    if organization_id:
        metrics = analytics.filter_metrics_by_organization(metrics, organization_id)
    
    # Generate filename
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    filename = f"analytics_export_{period}_{timestamp}"
    
    if export_format == 'csv':
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{filename}.csv"'
        
        writer = csv.writer(response)
        
        # Write header
        writer.writerow(['Metric', 'Value', 'Change', 'Period'])
        
        # Write data
        for category, category_metrics in metrics.items():
            writer.writerow([category.upper(), '', '', ''])
            for metric in category_metrics:
                writer.writerow([
                    metric['name'],
                    metric['value'],
                    f"{metric['change']}%",
                    period_name
                ])
        
        return response
    
    elif export_format == 'json':
        # Create JSON response
        response = JsonResponse({
            'period': period_name,
            'generated_at': timezone.now().isoformat(),
            'metrics': metrics
        })
        response['Content-Disposition'] = f'attachment; filename="{filename}.json"'
        return response
    
    else:
        return HttpResponse("Unsupported export format", status=400)

@login_required
def executive_analytics_dashboard(request):
    # Check permissions
    if not request.user.is_staff and not request.user.role == 'executive':
        return render(request, '403.html', status=403)
    
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
        comparison_start = start_date - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
        comparison_start = start_date - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
        days_passed = (timezone.now() - start_date).days
        comparison_start = start_date - timedelta(days=days_passed)
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    
    # Get executive KPIs
    executive_kpi = analytics.get_executive_kpis(start_date, comparison_start)
    
    # Generate insights
    predictive_insights = generate_predictive_insights(executive_kpi)
    strategic_opportunities = generate_strategic_opportunities(executive_kpi)
    critical_alerts = generate_critical_alerts(executive_kpi)
    
    # Generate forecast data
    forecast_data = generate_forecast_data(analytics, 90)  # 90-day forecast
    
    # Generate performance matrix
    performance_matrix = generate_performance_matrix_data(analytics)
    
    # Generate cost analysis
    cost_analysis = generate_cost_analysis_data(analytics)
    
    # Generate risk assessment
    risk_assessment = generate_risk_assessment_data(analytics)
    
    # Generate productivity trends
    productivity_trends = generate_productivity_trends_data(analytics)
    
    context = {
        'period_name': period_name,
        'current_period': period,
        'executive_kpi': executive_kpi,
        'predictive_insights': predictive_insights,
        'strategic_opportunities': strategic_opportunities,
        'critical_alerts': critical_alerts,
        'forecast_data': forecast_data,
        'performance_matrix': performance_matrix,
        'cost_analysis': cost_analysis,
        'risk_assessment': risk_assessment,
        'productivity_trends': productivity_trends
    }
    
    return render(request, 'analytics/executive_dashboard.html', context)

@login_required
def executive_analytics_refresh(request):
    # Check permissions
    if not request.user.is_staff and not request.user.role == 'executive':
        return HttpResponse("Permission denied", status=403)
    
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        comparison_start = start_date - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        comparison_start = start_date - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        comparison_start = start_date - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        days_passed = (timezone.now() - start_date).days
        comparison_start = start_date - timedelta(days=days_passed)
    else:
        start_date = timezone.now() - timedelta(days=30)
        comparison_start = start_date - timedelta(days=30)
    
    # Get executive KPIs
    executive_kpi = analytics.get_executive_kpis(start_date, comparison_start)
    
    return render(request, 'analytics/partials/executive_kpi_cards.html', {
        'executive_kpi': executive_kpi,
        'current_period': period
    })

@login_required
def executive_analytics_period(request):
    # Check permissions
    if not request.user.is_staff and not request.user.role == 'executive':
        return HttpResponse("Permission denied", status=403)
    
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
        comparison_start = start_date - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
        comparison_start = start_date - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
        days_passed = (timezone.now() - start_date).days
        comparison_start = start_date - timedelta(days=days_passed)
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    
    # Get executive KPIs
    executive_kpi = analytics.get_executive_kpis(start_date, comparison_start)
    
    return render(request, 'analytics/partials/executive_dashboard_content.html', {
        'period_name': period_name,
        'current_period': period,
        'executive_kpi': executive_kpi
    })

@login_required
def executive_analytics_export(request):
    # Check permissions
    if not request.user.is_staff and not request.user.role == 'executive':
        return HttpResponse("Permission denied", status=403)
    
    # Initialize analytics engine
    analytics = AnalyticsEngine(request.user)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
        comparison_start = start_date - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
        comparison_start = start_date - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
        days_passed = (timezone.now() - start_date).days
        comparison_start = start_date - timedelta(days=days_passed)
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    
    # Get executive KPIs
    executive_kpi = analytics.get_executive_kpis(start_date, comparison_start)
    
    # Get export format
    export_format = request.GET.get('format', 'csv')
    
    # Generate filename
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    filename = f"executive_analytics_{period}_{timestamp}"
    
    if export_format == 'csv':
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{filename}.csv"'
        
        writer = csv.writer(response)
        
        # Write header
        writer.writerow(['Category', 'Metric', 'Value', 'Change', 'Period'])
        
        # Write data
        for category, metrics in executive_kpi.items():
            for metric_name, metric_data in metrics.items():
                writer.writerow([
                    category,
                    metric_name,
                    metric_data['value'],
                    f"{metric_data['change']}%",
                    period_name
                ])
        
        return response
    
    elif export_format == 'json':
        # Create JSON response
        response = JsonResponse({
            'period': period_name,
            'generated_at': timezone.now().isoformat(),
            'executive_kpi': executive_kpi
        })
        response['Content-Disposition'] = f'attachment; filename="{filename}.json"'
        return response
    
    else:
        return HttpResponse("Unsupported export format", status=400)

# Helper functions for executive analytics
def generate_predictive_insights(executive_kpi):
    """Generate predictive insights based on executive KPIs."""
    insights = []
    
    # Project completion rate insight
    if 'project_metrics' in executive_kpi and 'completion_rate' in executive_kpi['project_metrics']:
        completion_rate = executive_kpi['project_metrics']['completion_rate']['value']
        executive_kpi['project_metrics']['completion_rate']['change']
        
        if completion_rate < 80:
            insights.append({
                'title': 'Project Completion Risk',
                'description': f'Current completion rate of {completion_rate}% suggests potential delays in project delivery.',
                'recommendation': 'Consider resource reallocation or scope adjustment to improve completion rates.',
                'impact': 'high' if completion_rate < 70 else 'medium',
                'category': 'project'
            })
    
    # Financial performance insight
    if 'financial_metrics' in executive_kpi and 'profit_margin' in executive_kpi['financial_metrics']:
        profit_margin = executive_kpi['financial_metrics']['profit_margin']['value']
        profit_change = executive_kpi['financial_metrics']['profit_margin']['change']
        
        if profit_margin < 15:
            insights.append({
                'title': 'Profit Margin Alert',
                'description': f'Profit margin of {profit_margin}% is below target threshold of 15%.',
                'recommendation': 'Review cost structures and pricing strategies for ongoing projects.',
                'impact': 'high' if profit_margin < 10 else 'medium',
                'category': 'financial'
            })
        
        if profit_change < -5:
            insights.append({
                'title': 'Declining Profitability',
                'description': f'Profit margin has decreased by {abs(profit_change)}% compared to previous period.',
                'recommendation': 'Analyze cost increases and implement targeted efficiency measures.',
                'impact': 'high' if profit_change < -10 else 'medium',
                'category': 'financial'
            })
    
    # Add more insights based on other metrics
    
    return insights

def generate_strategic_opportunities(executive_kpi):
    """Generate strategic opportunities based on executive KPIs."""
    opportunities = []
    
    # Resource utilization opportunity
    if 'resource_metrics' in executive_kpi and 'utilization_rate' in executive_kpi['resource_metrics']:
        utilization = executive_kpi['resource_metrics']['utilization_rate']['value']
        
        if utilization > 90:
            opportunities.append({
                'title': 'Resource Expansion',
                'description': f'High resource utilization of {utilization}% indicates potential capacity constraints.',
                'recommendation': 'Consider strategic hiring or contractor engagement to support growth.',
                'potential_impact': 'Increased capacity could support 15-20% more project volume.',
                'category': 'resource'
            })
        elif utilization < 70:
            opportunities.append({
                'title': 'Business Development Focus',
                'description': f'Resource utilization of {utilization}% indicates available capacity.',
                'recommendation': 'Intensify business development efforts to fill resource capacity.',
                'potential_impact': 'Could increase revenue by utilizing existing resources more effectively.',
                'category': 'business'
            })
    
    # Client satisfaction opportunity
    if 'client_metrics' in executive_kpi and 'satisfaction_score' in executive_kpi['client_metrics']:
        satisfaction = executive_kpi['client_metrics']['satisfaction_score']['value']
        
        if satisfaction > 85:
            opportunities.append({
                'title': 'Referral Program',
                'description': f'High client satisfaction score of {satisfaction}% creates referral potential.',
                'recommendation': 'Implement structured referral program to leverage client goodwill.',
                'potential_impact': 'Could generate 10-15% new business through referrals.',
                'category': 'client'
            })
    
    # Add more opportunities based on other metrics
    
    return opportunities

def generate_critical_alerts(executive_kpi):
    """Generate critical alerts based on executive KPIs."""
    alerts = []
    
    # Cash flow alert
    if 'financial_metrics' in executive_kpi and 'cash_flow' in executive_kpi['financial_metrics']:
        cash_flow = executive_kpi['financial_metrics']['cash_flow']['value']
        cash_flow_change = executive_kpi['financial_metrics']['cash_flow']['change']
        
        if cash_flow < 0:
            alerts.append({
                'title': 'Negative Cash Flow',
                'description': f'Current cash flow is negative at ${cash_flow}.',
                'recommendation': 'Review accounts receivable and accelerate collections.',
                'urgency': 'critical',
                'category': 'financial'
            })
        elif cash_flow_change < -20:
            alerts.append({
                'title': 'Declining Cash Flow',
                'description': f'Cash flow has decreased by {abs(cash_flow_change)}% compared to previous period.',
                'recommendation': 'Analyze cash flow trends and implement mitigation strategies.',
                'urgency': 'high',
                'category': 'financial'
            })
    
    # Project delay alert
    if 'project_metrics' in executive_kpi and 'on_time_delivery' in executive_kpi['project_metrics']:
        on_time = executive_kpi['project_metrics']['on_time_delivery']['value']
        
        if on_time < 75:
            alerts.append({
                'title': 'Project Delivery Delays',
                'description': f'Only {on_time}% of projects are being delivered on time.',
                'recommendation': 'Conduct project delivery review and implement corrective actions.',
                'urgency': 'high' if on_time < 60 else 'medium',
                'category': 'project'
            })
    
    # Add more alerts based on other metrics
    
    return alerts

def generate_forecast_data(analytics, days):
    """Generate forecast data for the specified number of days."""
    # Get historical data for the past 90 days
    start_date = timezone.now() - timedelta(days=90)
    
    # Get revenue data
    revenue_data = analytics.get_revenue_trend(start_date)
    
    # Get project completion data
    completion_data = analytics.get_project_completion_trend(start_date)
    
    # Get resource utilization data
    utilization_data = analytics.get_resource_utilization_trend(start_date)
    
    # Generate forecast using simple linear regression or more complex models
    # This is a simplified example
    revenue_forecast = analytics.forecast_metric(revenue_data, days)
    completion_forecast = analytics.forecast_metric(completion_data, days)
    utilization_forecast = analytics.forecast_metric(utilization_data, days)
    
    # Combine into forecast data
    forecast_data = {
        'revenue': revenue_forecast,
        'project_completion': completion_forecast,
        'resource_utilization': utilization_forecast,
        'forecast_period': f"{days} days",
        'generated_at': timezone.now().isoformat()
    }
    
    return forecast_data

def generate_performance_matrix_data(analytics):
    """Generate performance matrix data for visualization."""
    # Get all active projects
    projects = Project.objects.filter(status='active')
    
    # Calculate performance metrics for each project
    matrix_data = []
    
    for project in projects:
        # Calculate financial performance (0-100)
        financial_score = analytics.calculate_project_financial_score(project)
        
        # Calculate schedule performance (0-100)
        schedule_score = analytics.calculate_project_schedule_score(project)
        
        # Calculate size/value (for bubble size)
        project_value = project.budget or 0
        
        # Add to matrix data
        matrix_data.append({
            'id': project.id,
            'name': project.name,
            'financial_score': financial_score,
            'schedule_score': schedule_score,
            'value': project_value,
            'manager': project.manager.get_full_name() if project.manager else 'Unassigned'
        })
    
    return matrix_data

def generate_cost_analysis_data(analytics):
    """Generate cost analysis data for visualization."""
    # This would typically involve complex cost analysis logic
    # Simplified example for demonstration
    return analytics.get_cost_analysis_data()

def generate_risk_assessment_data(analytics):
    """Generate risk assessment data for visualization."""
    # This would typically involve risk scoring and categorization
    # Simplified example for demonstration
    return analytics.get_risk_assessment_data()

def generate_productivity_trends_data(analytics):
    """Generate productivity trends data for visualization."""
    # Get productivity data for the past 12 months
    start_date = timezone.now().replace(day=1) - timedelta(days=365)
    
    # Get monthly productivity data
    productivity_data = analytics.get_productivity_trend(start_date)
    
    return productivity_data

def calculate_change(current, previous):
    """Calculate percentage change between two values."""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)

class ReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'analytics/reports.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize analytics engine
        analytics = AnalyticsEngine(self.request.user)
        
        # Get available report types
        context['report_types'] = [
            {
                'id': 'project_performance',
                'name': 'Project Performance Report',
                'description': 'Comprehensive analysis of project performance metrics including schedule, budget, and resource utilization.',
                'icon': 'chart-line'
            },
            {
                'id': 'financial_summary',
                'name': 'Financial Summary Report',
                'description': 'Financial overview including revenue, expenses, profit margins, and cash flow analysis.',
                'icon': 'dollar-sign'
            },
            {
                'id': 'resource_utilization',
                'name': 'Resource Utilization Report',
                'description': 'Analysis of resource allocation, utilization rates, and capacity planning.',
                'icon': 'users'
            },
            {
                'id': 'client_satisfaction',
                'name': 'Client Satisfaction Report',
                'description': 'Client feedback metrics, satisfaction scores, and relationship health indicators.',
                'icon': 'smile'
            },
            {
                'id': 'operational_efficiency',
                'name': 'Operational Efficiency Report',
                'description': 'Metrics on process efficiency, workflow optimization, and operational improvements.',
                'icon': 'cogs'
            }
        ]
        
        # Get user's recent reports
        context['recent_reports'] = analytics.get_recent_reports(self.request.user, limit=5)
        
        # Get scheduled reports
        context['scheduled_reports'] = analytics.get_scheduled_reports(self.request.user)
        
        # Get projects for filtering
        context['projects'] = Project.objects.filter(
            Q(members=self.request.user) | 
            Q(created_by=self.request.user)
        ).distinct()
        
        # Get organizations for filtering
        if self.request.user.is_staff:
            context['organizations'] = Organization.objects.all()
        else:
            context['organizations'] = Organization.objects.filter(
                members=self.request.user
            ).distinct()
        
        # Get available export formats
        context['export_formats'] = [
            {'id': 'pdf', 'name': 'PDF Document'},
            {'id': 'excel', 'name': 'Excel Spreadsheet'},
            {'id': 'csv', 'name': 'CSV File'},
            {'id': 'json', 'name': 'JSON Data'}
        ]
        
        # Get available time periods
        context['time_periods'] = [
            {'id': '7d', 'name': 'Last 7 Days'},
            {'id': '30d', 'name': 'Last 30 Days'},
            {'id': '90d', 'name': 'Last 90 Days'},
            {'id': 'ytd', 'name': 'Year to Date'},
            {'id': 'custom', 'name': 'Custom Range'}
        ]
        
        return context