"""
Stakeholder Management Views

This module contains views related to stakeholder management, communication, and engagement.
"""


import logging
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.generic import DetailView, ListView
from .imports_base import *


logger = logging.getLogger(__name__)



class StakeholderListView(LoginRequiredMixin, ListView):
    model = Stakeholder
    template_name = 'stakeholders/stakeholder_list.html'
    context_object_name = 'stakeholders'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Stakeholder.objects.all()
        
        # Filter by organization if user is not staff
        if not self.request.user.is_staff:
            user_orgs = Organization.objects.filter(
                Q(members=self.request.user) | 
                Q(created_by=self.request.user)
            )
            queryset = queryset.filter(organization__in=user_orgs)
        
        # Apply search if provided
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(organization__name__icontains=search_query) |
                Q(role__icontains=search_query)
            )
        
        # Apply filters if provided
        organization_filter = self.request.GET.get('organization')
        if organization_filter:
            queryset = queryset.filter(organization_id=organization_filter)
            
        role_filter = self.request.GET.get('role')
        if role_filter:
            queryset = queryset.filter(role=role_filter)
            
        influence_filter = self.request.GET.get('influence')
        if influence_filter:
            queryset = queryset.filter(influence_level=influence_filter)
        
        return queryset

class StakeholderDetailView(LoginRequiredMixin, DetailView):
    model = Stakeholder
    template_name = 'stakeholders/stakeholder_detail.html'
    context_object_name = 'stakeholder'

@login_required
def stakeholder_search_htmx(request):
    search_query = request.GET.get('query', '')
    
    if not search_query or len(search_query) < 2:
        return render(request, 'stakeholders/partials/stakeholder_search_results.html', {
            'stakeholders': [],
            'query': search_query
        })
    
    # Base queryset
    queryset = Stakeholder.objects.all()
    
    # Filter by organization if user is not staff
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        queryset = queryset.filter(organization__in=user_orgs)
    
    # Apply search
    stakeholders = queryset.filter(
        Q(name__icontains=search_query) |
        Q(email__icontains=search_query) |
        Q(organization__name__icontains=search_query) |
        Q(role__icontains=search_query)
    )[:20]
    
    return render(request, 'stakeholders/partials/stakeholder_search_results.html', {
        'stakeholders': stakeholders,
        'query': search_query
    })

@login_required
def stakeholder_filter_htmx(request):
    # Base queryset
    queryset = Stakeholder.objects.all()
    
    # Filter by organization if user is not staff
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        queryset = queryset.filter(organization__in=user_orgs)
    
    # Apply filters
    organization_filter = request.GET.get('organization')
    if organization_filter:
        queryset = queryset.filter(organization_id=organization_filter)
        
    role_filter = request.GET.get('role')
    if role_filter:
        queryset = queryset.filter(role=role_filter)
        
    influence_filter = request.GET.get('influence')
    if influence_filter:
        queryset = queryset.filter(influence_level=influence_filter)
    
    # Apply search if provided
    search_query = request.GET.get('search')
    if search_query:
        queryset = queryset.filter(
            Q(name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(organization__name__icontains=search_query) |
            Q(role__icontains=search_query)
        )
    
    # Get available filter options for display
    organizations = Organization.objects.all()
    if not request.user.is_staff:
        organizations = organizations.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
    
    roles = Stakeholder.objects.values_list('role', flat=True).distinct()
    influence_levels = [choice[0] for choice in Stakeholder.INFLUENCE_CHOICES]
    
    context = {
        'stakeholders': queryset,
        'organizations': organizations,
        'roles': roles,
        'influence_levels': influence_levels,
        'current_filters': {
            'organization': organization_filter,
            'role': role_filter,
            'influence': influence_filter,
            'search': search_query
        }
    }
    
    return render(request, 'stakeholders/partials/stakeholder_filtered_list.html', context)

@login_required
def stakeholder_sort_htmx(request):
    # Base queryset
    queryset = Stakeholder.objects.all()
    
    # Filter by organization if user is not staff
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        queryset = queryset.filter(organization__in=user_orgs)
    
    # Apply sort
    sort_by = request.GET.get('sort_by', 'name')
    sort_dir = request.GET.get('sort_dir', 'asc')
    
    if sort_dir == 'desc':
        sort_by = f'-{sort_by}'
    
    stakeholders = queryset.order_by(sort_by)
    
    return render(request, 'stakeholders/partials/stakeholder_sorted_list.html', {
        'stakeholders': stakeholders,
        'sort_by': request.GET.get('sort_by', 'name'),
        'sort_dir': request.GET.get('sort_dir', 'asc')
    })

@login_required
def stakeholder_update_htmx(request, stakeholder_id):
    stakeholder = get_object_or_404(Stakeholder, pk=stakeholder_id)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if stakeholder.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Update stakeholder fields
        stakeholder.name = request.POST.get('name', stakeholder.name)
        stakeholder.email = request.POST.get('email', stakeholder.email)
        stakeholder.phone = request.POST.get('phone', stakeholder.phone)
        stakeholder.role = request.POST.get('role', stakeholder.role)
        stakeholder.influence_level = request.POST.get('influence_level', stakeholder.influence_level)
        stakeholder.notes = request.POST.get('notes', stakeholder.notes)
        
        # Update organization if provided
        organization_id = request.POST.get('organization')
        if organization_id:
            try:
                organization = Organization.objects.get(pk=organization_id)
                stakeholder.organization = organization
            except Organization.DoesNotExist:
                pass
        
        stakeholder.save()
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='updated',
            target_model='Stakeholder',
            target_id=stakeholder.id,
            target_name=stakeholder.name
        )
        
        messages.success(request, f'Stakeholder "{stakeholder.name}" updated successfully.')
        
        return render(request, 'stakeholders/partials/stakeholder_detail_content.html', {
            'stakeholder': stakeholder
        })
    
    # GET request - show form
    organizations = Organization.objects.all()
    if not request.user.is_staff:
        organizations = organizations.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
    
    context = {
        'stakeholder': stakeholder,
        'organizations': organizations,
        'influence_levels': Stakeholder.INFLUENCE_CHOICES
    }
    
    return render(request, 'stakeholders/partials/stakeholder_edit_form.html', context)

@login_required
def stakeholder_projects_htmx(request, stakeholder_id):
    stakeholder = get_object_or_404(Stakeholder, pk=stakeholder_id)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if stakeholder.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    # Get stakeholder's projects
    projects = Project.objects.filter(stakeholders=stakeholder)
    
    # Get project stats
    project_stats = []
    
    for project in projects:
        # Get communication count
        communication_count = Activity.objects.filter(
            target_model='Communication',
            project=project,
            description__icontains=stakeholder.name
        ).count()
        
        # Get last communication
        last_communication = Activity.objects.filter(
            target_model='Communication',
            project=project,
            description__icontains=stakeholder.name
        ).order_by('-timestamp').first()
        
        # Get stakeholder's role in project
        stakeholder_role = "Stakeholder"  # Default role
        
        # Check if stakeholder is a project member
        if project.members.filter(email=stakeholder.email).exists():
            stakeholder_role = "Team Member"
        
        # Check if stakeholder is project owner
        if project.created_by and project.created_by.email == stakeholder.email:
            stakeholder_role = "Project Owner"
        
        # Add to stats
        project_stats.append({
            'project': project,
            'communication_count': communication_count,
            'last_communication': last_communication,
            'stakeholder_role': stakeholder_role
        })
    
    context = {
        'stakeholder': stakeholder,
        'project_stats': project_stats
    }
    
    return render(request, 'stakeholders/partials/stakeholder_projects.html', context)

@login_required
def communication_history_htmx(request, entity_type, entity_id):
    if entity_type == 'stakeholder':
        entity = get_object_or_404(Stakeholder, pk=entity_id)
        title = f"Communication History for {entity.name}"
    elif entity_type == 'organization':
        entity = get_object_or_404(Organization, pk=entity_id)
        title = f"Communication History for {entity.name}"
    else:
        return HttpResponse("Invalid entity type", status=400)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if hasattr(entity, 'organization') and entity.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
        elif entity_type == 'organization' and entity not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    # Get communication history
    if entity_type == 'stakeholder':
        communications = Activity.objects.filter(
            target_model='Communication',
            description__icontains=entity.name
        ).order_by('-timestamp')
    else:  # organization
        # Get all stakeholders in the organization
        stakeholders = Stakeholder.objects.filter(organization=entity)
        stakeholder_names = [s.name for s in stakeholders]
        
        # Get communications for any of these stakeholders
        communications = Activity.objects.filter(
            target_model='Communication'
        ).order_by('-timestamp')
        
        filtered_communications = []
        for comm in communications:
            for name in stakeholder_names:
                if name in comm.description:
                    filtered_communications.append(comm)
                    break
        
        communications = filtered_communications
    
    # Apply filters if provided
    project_filter = request.GET.get('project')
    if project_filter:
        communications = [c for c in communications if c.project and str(c.project.id) == project_filter]
    
    date_filter = request.GET.get('date')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            communications = [c for c in communications if c.timestamp.date() == filter_date]
        except ValueError:
            pass
    
    # Get projects for filtering
    projects = Project.objects.filter(
        Q(members=request.user) | 
        Q(created_by=request.user)
    ).distinct()
    
    context = {
        'entity': entity,
        'entity_type': entity_type,
        'communications': communications,
        'title': title,
        'projects': projects,
        'current_filters': {
            'project': project_filter,
            'date': date_filter
        }
    }
    
    return render(request, 'stakeholders/partials/communication_history.html', context)

@login_required
def log_communication_htmx(request, entity_type, entity_id):
    if entity_type == 'stakeholder':
        entity = get_object_or_404(Stakeholder, pk=entity_id)
    elif entity_type == 'organization':
        entity = get_object_or_404(Organization, pk=entity_id)
    else:
        return HttpResponse("Invalid entity type", status=400)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if hasattr(entity, 'organization') and entity.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
        elif entity_type == 'organization' and entity not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        communication_type = request.POST.get('communication_type')
        notes = request.POST.get('notes')
        project_id = request.POST.get('project')
        
        if not communication_type or not notes:
            return HttpResponse("Communication type and notes are required", status=400)
        
        # Get project if specified
        project = None
        if project_id:
            try:
                project = Project.objects.get(pk=project_id)
            except Project.DoesNotExist:
                pass
        
        # Create activity record
        entity_name = entity.name
        
        activity = Activity.objects.create(
            user=request.user,
            action='communicated',
            target_model='Communication',
            target_id=0,  # No specific ID for communications
            target_name=f"{communication_type} with {entity_name}",
            description=notes,
            project=project
        )
        
        messages.success(request, f'Communication with {entity_name} logged successfully.')
        
        # Return the new communication record
        return render(request, 'stakeholders/partials/communication_item.html', {
            'communication': activity
        })
    
    # GET request - show form
    projects = Project.objects.filter(
        Q(members=request.user) | 
        Q(created_by=request.user)
    ).distinct()
    
    communication_types = [
        'Email', 'Phone Call', 'Meeting', 'Video Conference', 
        'Site Visit', 'Letter', 'Text Message', 'Social Media', 'Other'
    ]
    
    context = {
        'entity': entity,
        'entity_type': entity_type,
        'projects': projects,
        'communication_types': communication_types
    }
    
    return render(request, 'stakeholders/partials/log_communication_form.html', context)

@login_required
def stakeholder_assign_project_htmx(request, stakeholder_id):
    stakeholder = get_object_or_404(Stakeholder, pk=stakeholder_id)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if stakeholder.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        project_id = request.POST.get('project_id')
        role = request.POST.get('role')
        
        if not project_id:
            return HttpResponse("Project is required", status=400)
        
        try:
            project = Project.objects.get(pk=project_id)
            
            # Check if user has access to this project
            if not (request.user.is_staff or 
                    project.created_by == request.user or 
                    project.members.filter(id=request.user.id).exists()):
                return HttpResponse("You don't have permission to modify this project", status=403)
            
            # Check if stakeholder is already assigned to this project
            if project.stakeholders.filter(id=stakeholder.id).exists():
                return HttpResponse(f"{stakeholder.name} is already assigned to this project", status=400)
            
            # Assign stakeholder to project
            project.stakeholders.add(stakeholder)
            
            # Create activity record
            Activity.objects.create(
                user=request.user,
                action='assigned',
                target_model='Stakeholder',
                target_id=stakeholder.id,
                target_name=stakeholder.name,
                description=f"Assigned to project: {project.name}" + (f" as {role}" if role else ""),
                project=project
            )
            
            messages.success(request, f'{stakeholder.name} assigned to {project.name} successfully.')
            
            # Get updated project stats
            communication_count = Activity.objects.filter(
                target_model='Communication',
                project=project,
                description__icontains=stakeholder.name
            ).count()
            
            last_communication = Activity.objects.filter(
                target_model='Communication',
                project=project,
                description__icontains=stakeholder.name
            ).order_by('-timestamp').first()
            
            # Determine stakeholder's role in project
            stakeholder_role = role or "Stakeholder"  # Use provided role or default
            
            # Check if stakeholder is a project member
            if project.members.filter(email=stakeholder.email).exists():
                stakeholder_role = "Team Member"
            
            # Check if stakeholder is project owner
            if project.created_by and project.created_by.email == stakeholder.email:
                stakeholder_role = "Project Owner"
            
            # Return the new project assignment
            return render(request, 'stakeholders/partials/project_assignment_item.html', {
                'project': project,
                'stakeholder': stakeholder,
                'communication_count': communication_count,
                'last_communication': last_communication,
                'stakeholder_role': stakeholder_role
            })
            
        except Project.DoesNotExist:
            return HttpResponse("Project not found", status=404)
    
    # GET request - show form
    # Get projects that the user has access to and stakeholder is not already assigned to
    available_projects = Project.objects.filter(
        Q(members=request.user) | 
        Q(created_by=request.user)
    ).exclude(
        stakeholders=stakeholder
    ).distinct()
    
    stakeholder_roles = [
        'Key Decision Maker', 'Influencer', 'Technical Advisor', 
        'Community Representative', 'Regulatory Contact', 'End User',
        'Financial Stakeholder', 'Vendor/Supplier', 'Other'
    ]
    
    context = {
        'stakeholder': stakeholder,
        'available_projects': available_projects,
        'stakeholder_roles': stakeholder_roles
    }
    
    return render(request, 'stakeholders/partials/assign_project_form.html', context)

@login_required
def project_search_htmx(request):
    search_query = request.GET.get('query', '')
    exclude_stakeholder_id = request.GET.get('exclude_stakeholder')
    
    if not search_query or len(search_query) < 2:
        return render(request, 'stakeholders/partials/project_search_results.html', {
            'projects': [],
            'query': search_query
        })
    
    # Get projects that the user has access to
    projects = Project.objects.filter(
        Q(members=request.user) | 
        Q(created_by=request.user),
        Q(name__icontains=search_query) |
        Q(description__icontains=search_query)
    ).distinct()
    
    # Exclude projects that the stakeholder is already assigned to
    if exclude_stakeholder_id:
        try:
            stakeholder = Stakeholder.objects.get(pk=exclude_stakeholder_id)
            projects = projects.exclude(stakeholders=stakeholder)
        except Stakeholder.DoesNotExist:
            pass
    
    return render(request, 'stakeholders/partials/project_search_results.html', {
        'projects': projects[:10],
        'query': search_query
    })

@login_required
def stakeholder_remove_project_htmx(request, stakeholder_id):
    if request.method != 'POST':
        return HttpResponse("Method not allowed", status=405)
    
    stakeholder = get_object_or_404(Stakeholder, pk=stakeholder_id)
    project_id = request.POST.get('project_id')
    
    if not project_id:
        return HttpResponse("Project ID is required", status=400)
    
    try:
        project = Project.objects.get(pk=project_id)
        
        # Check permissions
        if not (request.user.is_staff or 
                project.created_by == request.user or 
                project.members.filter(id=request.user.id).exists()):
            return HttpResponse("Permission denied", status=403)
        
        # Check if stakeholder is assigned to this project
        if not project.stakeholders.filter(id=stakeholder.id).exists():
            return HttpResponse(f"{stakeholder.name} is not assigned to this project", status=400)
        
        # Remove stakeholder from project
        project.stakeholders.remove(stakeholder)
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='removed',
            target_model='Stakeholder',
            target_id=stakeholder.id,
            target_name=stakeholder.name,
            description=f"Removed from project: {project.name}",
            project=project
        )
        
        messages.success(request, f'{stakeholder.name} removed from {project.name} successfully.')
        
        # Get updated project assignments
        projects = Project.objects.filter(stakeholders=stakeholder)
        
        # Get project stats
        project_stats = []
        
        for p in projects:
            # Get communication count
            communication_count = Activity.objects.filter(
                target_model='Communication',
                project=p,
                description__icontains=stakeholder.name
            ).count()
            
            # Get last communication
            last_communication = Activity.objects.filter(
                target_model='Communication',
                project=p,
                description__icontains=stakeholder.name
            ).order_by('-timestamp').first()
            
            # Get stakeholder's role in project
            stakeholder_role = "Stakeholder"  # Default role
            
            # Check if stakeholder is a project member
            if p.members.filter(email=stakeholder.email).exists():
                stakeholder_role = "Team Member"
            
            # Check if stakeholder is project owner
            if p.created_by and p.created_by.email == stakeholder.email:
                stakeholder_role = "Project Owner"
            
            # Add to stats
            project_stats.append({
                'project': p,
                'communication_count': communication_count,
                'last_communication': last_communication,
                'stakeholder_role': stakeholder_role
            })
        
        return render(request, 'stakeholders/partials/stakeholder_projects_content.html', {
            'stakeholder': stakeholder,
            'project_stats': project_stats
        })
        
    except Project.DoesNotExist:
        return HttpResponse("Project not found", status=404)

@login_required
def stakeholder_analytics_htmx(request):
    # Check permissions
    if not request.user.is_staff and request.user.role not in ['manager', 'executive']:
        return HttpResponse("Permission denied", status=403)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
        comparison_start = start_date - timedelta(days=7)
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
        comparison_start = start_date - timedelta(days=90)
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
        days_passed = (timezone.now() - start_date).days
        comparison_start = start_date - timedelta(days=days_passed)
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
        comparison_start = start_date - timedelta(days=30)
    
    # Get stakeholder analytics
    
    # 1. Total stakeholders
    total_stakeholders = Stakeholder.objects.count()
    
    # For comparison period
    total_stakeholders_previous = Stakeholder.objects.filter(
        created_at__lt=start_date
    ).count()
    
    stakeholders_change = calculate_change(total_stakeholders, total_stakeholders_previous)
    
    # 2. New stakeholders in period
    new_stakeholders = Stakeholder.objects.filter(
        created_at__gte=start_date
    ).count()
    
    # For comparison period
    new_stakeholders_previous = Stakeholder.objects.filter(
        created_at__gte=comparison_start,
        created_at__lt=start_date
    ).count()
    
    new_stakeholders_change = calculate_change(new_stakeholders, new_stakeholders_previous)
    
    # 3. Communications in period
    communications = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=start_date
    ).count()
    
    # For comparison period
    communications_previous = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=comparison_start,
        timestamp__lt=start_date
    ).count()
    
    communications_change = calculate_change(communications, communications_previous)
    
    # 4. Active stakeholders (those with communications in period)
    active_stakeholders = set()
    
    communication_activities = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=start_date
    )
    
    for activity in communication_activities:
        # Extract stakeholder name from description
        for stakeholder in Stakeholder.objects.all():
            if stakeholder.name in activity.description:
                active_stakeholders.add(stakeholder.id)
                break
    
    active_stakeholders_count = len(active_stakeholders)
    
    # For comparison period
    active_stakeholders_previous = set()
    
    communication_activities_previous = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=comparison_start,
        timestamp__lt=start_date
    )
    
    for activity in communication_activities_previous:
        for stakeholder in Stakeholder.objects.all():
            if stakeholder.name in activity.description:
                active_stakeholders_previous.add(stakeholder.id)
                break
    
    active_stakeholders_previous_count = len(active_stakeholders_previous)
    
    active_stakeholders_change = calculate_change(
        active_stakeholders_count, active_stakeholders_previous_count
    )
    
    # 5. Engagement rate (active stakeholders / total stakeholders)
    engagement_rate = round((active_stakeholders_count / total_stakeholders * 100) 
                           if total_stakeholders > 0 else 0, 1)
    
    engagement_rate_previous = round((active_stakeholders_previous_count / total_stakeholders_previous * 100)
                                    if total_stakeholders_previous > 0 else 0, 1)
    
    engagement_rate_change = calculate_change(engagement_rate, engagement_rate_previous)
    
    # 6. Communications per stakeholder
    comms_per_stakeholder = round(communications / active_stakeholders_count, 1) if active_stakeholders_count > 0 else 0
    
    comms_per_stakeholder_previous = round(communications_previous / active_stakeholders_previous_count, 1) if active_stakeholders_previous_count > 0 else 0
    
    comms_per_stakeholder_change = calculate_change(comms_per_stakeholder, comms_per_stakeholder_previous)
    
    # 7. Top communication methods
    communication_methods = {}
    
    for activity in communication_activities:
        # Extract communication method from target_name
        method = activity.target_name.split(' with ')[0] if ' with ' in activity.target_name else 'Other'
        communication_methods[method] = communication_methods.get(method, 0) + 1
    
    # Sort by count
    top_methods = sorted(communication_methods.items(), key=lambda x: x[1], reverse=True)[:5]
    
    # 8. Stakeholders by influence level
    influence_distribution = {}
    
    for level, _ in Stakeholder.INFLUENCE_CHOICES:
        count = Stakeholder.objects.filter(influence_level=level).count()
        influence_distribution[level] = count
    
    # 9. Stakeholders by organization
    org_distribution = {}
    
    organizations = Organization.objects.all()
    for org in organizations:
        count = Stakeholder.objects.filter(organization=org).count()
        if count > 0:
            org_distribution[org.name] = count
    
    # Sort by count
    top_organizations = sorted(org_distribution.items(), key=lambda x: x[1], reverse=True)[:5]
    
    # 10. Communication trend over time
    trend_data = []
    
    if period in ['7d', '30d']:
        # Daily data for shorter periods
        for i in range((timezone.now() - start_date).days + 1):
            day = start_date + timedelta(days=i)
            day_end = day.replace(hour=23, minute=59, second=59)
            
            count = Activity.objects.filter(
                target_model='Communication',
                timestamp__gte=day,
                timestamp__lte=day_end
            ).count()
            
            trend_data.append({
                'date': day.strftime('%Y-%m-%d'),
                'count': count
            })
    else:
        # Weekly data for longer periods
        current_date = start_date
        while current_date <= timezone.now():
            week_end = current_date + timedelta(days=6)
            
            count = Activity.objects.filter(
                target_model='Communication',
                timestamp__gte=current_date,
                timestamp__lte=week_end
            ).count()
            
            trend_data.append({
                'date': f"{current_date.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}",
                'count': count
            })
            
            current_date += timedelta(days=7)
    
    # Prepare context
    context = {
        'period_name': period_name,
        'current_period': period,
        'metrics': {
            'total_stakeholders': {
                'value': total_stakeholders,
                'change': stakeholders_change
            },
            'new_stakeholders': {
                'value': new_stakeholders,
                'change': new_stakeholders_change
            },
            'communications': {
                'value': communications,
                'change': communications_change
            },
            'active_stakeholders': {
                'value': active_stakeholders_count,
                'change': active_stakeholders_change
            },
            'engagement_rate': {
                'value': f"{engagement_rate}%",
                'change': engagement_rate_change
            },
            'comms_per_stakeholder': {
                'value': comms_per_stakeholder,
                'change': comms_per_stakeholder_change
            }
        },
        'top_methods': top_methods,
        'influence_distribution': influence_distribution,
        'top_organizations': top_organizations,
        'trend_data': trend_data
    }
    
    return render(request, 'stakeholders/partials/stakeholder_analytics.html', context)

def calculate_change(current, previous):
    """Calculate percentage change between two values."""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)

@login_required
def stakeholder_analytics_export_csv(request):
    # Check permissions
    if not request.user.is_staff and request.user.role not in ['manager', 'executive']:
        return HttpResponse("Permission denied", status=403)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="stakeholder_analytics_{period}_{timezone.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    
    # Write header
    writer.writerow(['Stakeholder Analytics Report', period_name, f'Generated on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'])
    writer.writerow([])
    
    # Write summary metrics
    writer.writerow(['Summary Metrics'])
    
    # 1. Total stakeholders
    total_stakeholders = Stakeholder.objects.count()
    writer.writerow(['Total Stakeholders', total_stakeholders])
    
    # 2. New stakeholders in period
    new_stakeholders = Stakeholder.objects.filter(
        created_at__gte=start_date
    ).count()
    writer.writerow(['New Stakeholders in Period', new_stakeholders])
    
    # 3. Communications in period
    communications = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=start_date
    ).count()
    writer.writerow(['Communications in Period', communications])
    
    # 4. Active stakeholders (those with communications in period)
    active_stakeholders = set()
    
    communication_activities = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=start_date
    )
    
    for activity in communication_activities:
        # Extract stakeholder name from description
        for stakeholder in Stakeholder.objects.all():
            if stakeholder.name in activity.description:
                active_stakeholders.add(stakeholder.id)
                break
    
    active_stakeholders_count = len(active_stakeholders)
    writer.writerow(['Active Stakeholders', active_stakeholders_count])
    
    # 5. Engagement rate (active stakeholders / total stakeholders)
    engagement_rate = round((active_stakeholders_count / total_stakeholders * 100) 
                           if total_stakeholders > 0 else 0, 1)
    writer.writerow(['Engagement Rate', f"{engagement_rate}%"])
    
    # 6. Communications per stakeholder
    comms_per_stakeholder = round(communications / active_stakeholders_count, 1) if active_stakeholders_count > 0 else 0
    writer.writerow(['Communications per Stakeholder', comms_per_stakeholder])
    
    writer.writerow([])
    
    # Write communication methods breakdown
    writer.writerow(['Communication Methods'])
    
    communication_methods = {}
    
    for activity in communication_activities:
        # Extract communication method from target_name
        method = activity.target_name.split(' with ')[0] if ' with ' in activity.target_name else 'Other'
        communication_methods[method] = communication_methods.get(method, 0) + 1
    
    # Sort by count
    methods = sorted(communication_methods.items(), key=lambda x: x[1], reverse=True)
    
    for method, count in methods:
        writer.writerow([method, count])
    
    writer.writerow([])
    
    # Write stakeholders by influence level
    writer.writerow(['Stakeholders by Influence Level'])
    
    for level, name in Stakeholder.INFLUENCE_CHOICES:
        count = Stakeholder.objects.filter(influence_level=level).count()
        writer.writerow([name, count])
    
    writer.writerow([])
    
    # Write stakeholders by organization
    writer.writerow(['Stakeholders by Organization'])
    
    organizations = Organization.objects.all()
    org_counts = []
    
    for org in organizations:
        count = Stakeholder.objects.filter(organization=org).count()
        if count > 0:
            org_counts.append((org.name, count))
    
    # Sort by count
    org_counts.sort(key=lambda x: x[1], reverse=True)
    
    for org_name, count in org_counts:
        writer.writerow([org_name, count])
    
    writer.writerow([])
    
    # Write communication trend
    writer.writerow(['Communication Trend'])
    writer.writerow(['Date', 'Count'])
    
    if period in ['7d', '30d']:
        # Daily data for shorter periods
        for i in range((timezone.now() - start_date).days + 1):
            day = start_date + timedelta(days=i)
            day_end = day.replace(hour=23, minute=59, second=59)
            
            count = Activity.objects.filter(
                target_model='Communication',
                timestamp__gte=day,
                timestamp__lte=day_end
            ).count()
            
            writer.writerow([day.strftime('%Y-%m-%d'), count])
    else:
        # Weekly data for longer periods
        current_date = start_date
        while current_date <= timezone.now():
            week_end = current_date + timedelta(days=6)
            
            count = Activity.objects.filter(
                target_model='Communication',
                timestamp__gte=current_date,
                timestamp__lte=week_end
            ).count()
            
            writer.writerow([f"{current_date.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}", count])
            
            current_date += timedelta(days=7)
    
    writer.writerow([])
    
    # Write most active stakeholders
    writer.writerow(['Most Active Stakeholders'])
    writer.writerow(['Stakeholder', 'Organization', 'Communications'])
    
    stakeholder_activity = {}
    
    for stakeholder in Stakeholder.objects.all():
        count = 0
        for activity in communication_activities:
            if stakeholder.name in activity.description:
                count += 1
        
        if count > 0:
            stakeholder_activity[stakeholder] = count
    
    # Sort by count
    most_active = sorted(stakeholder_activity.items(), key=lambda x: x[1], reverse=True)[:20]
    
    for stakeholder, count in most_active:
        writer.writerow([
            stakeholder.name, 
            stakeholder.organization.name if stakeholder.organization else 'N/A',
            count
        ])
    
    return response

@login_required
def mobile_stakeholder_projects_htmx(request, stakeholder_id):
    stakeholder = get_object_or_404(Stakeholder, pk=stakeholder_id)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if stakeholder.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    # Get stakeholder's projects
    projects = Project.objects.filter(stakeholders=stakeholder)
    
    # Get project stats
    project_stats = []
    
    for project in projects:
        # Get communication count
        communication_count = Activity.objects.filter(
            target_model='Communication',
            project=project,
            description__icontains=stakeholder.name
        ).count()
        
        # Get last communication
        last_communication = Activity.objects.filter(
            target_model='Communication',
            project=project,
            description__icontains=stakeholder.name
        ).order_by('-timestamp').first()
        
        # Add to stats
        project_stats.append({
            'project': project,
            'communication_count': communication_count,
            'last_communication': last_communication
        })
    
    context = {
        'stakeholder': stakeholder,
        'project_stats': project_stats
    }
    
    return render(request, 'stakeholders/partials/mobile_stakeholder_projects.html', context)

@login_required
def mobile_communication_history_htmx(request, entity_type, entity_id):
    if entity_type == 'stakeholder':
        entity = get_object_or_404(Stakeholder, pk=entity_id)
        title = f"Communication History for {entity.name}"
    elif entity_type == 'organization':
        entity = get_object_or_404(Organization, pk=entity_id)
        title = f"Communication History for {entity.name}"
    else:
        return HttpResponse("Invalid entity type", status=400)
    
    # Check permissions
    if not request.user.is_staff:
        user_orgs = Organization.objects.filter(
            Q(members=request.user) | 
            Q(created_by=request.user)
        )
        if hasattr(entity, 'organization') and entity.organization not in user_orgs:
            return HttpResponse("Permission denied", status=403)
        elif entity_type == 'organization' and entity not in user_orgs:
            return HttpResponse("Permission denied", status=403)
    
    # Get communication history
    if entity_type == 'stakeholder':
        communications = Activity.objects.filter(
            target_model='Communication',
            description__icontains=entity.name
        ).order_by('-timestamp')
    else:  # organization
        # Get all stakeholders in the organization
        stakeholders = Stakeholder.objects.filter(organization=entity)
        stakeholder_names = [s.name for s in stakeholders]
        
        # Get communications for any of these stakeholders
        communications = Activity.objects.filter(
            target_model='Communication'
        ).order_by('-timestamp')
        
        filtered_communications = []
        for comm in communications:
            for name in stakeholder_names:
                if name in comm.description:
                    filtered_communications.append(comm)
                    break
        
        communications = filtered_communications
    
    context = {
        'entity': entity,
        'entity_type': entity_type,
        'communications': communications[:10],  # Limit to 10 for mobile view
        'title': title
    }
    
    return render(request, 'stakeholders/partials/mobile_communication_history.html', context)

@login_required
def stakeholder_export_csv(request):
    # Check permissions
    if not request.user.is_staff and request.user.role not in ['manager', 'executive']:
        return HttpResponse("Permission denied", status=403)
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="stakeholders_{timezone.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    
    # Write header
    writer.writerow([
        'ID', 'Name', 'Email', 'Phone', 'Organization', 'Role', 
        'Influence Level', 'Notes', 'Created At', 'Updated At',
        'Project Count', 'Last Communication'
    ])
    
    # Get stakeholders
    stakeholders = Stakeholder.objects.all()
    
    # Apply filters if provided
    organization_filter = request.GET.get('organization')
    if organization_filter:
        stakeholders = stakeholders.filter(organization_id=organization_filter)
        
    role_filter = request.GET.get('role')
    if role_filter:
        stakeholders = stakeholders.filter(role=role_filter)
        
    influence_filter = request.GET.get('influence')
    if influence_filter:
        stakeholders = stakeholders.filter(influence_level=influence_filter)
    
    # Write data
    for stakeholder in stakeholders:
        # Get project count
        project_count = Project.objects.filter(stakeholders=stakeholder).count()
        
        # Get last communication
        last_communication = Activity.objects.filter(
            target_model='Communication',
            description__icontains=stakeholder.name
        ).order_by('-timestamp').first()
        
        last_comm_date = last_communication.timestamp.strftime('%Y-%m-%d') if last_communication else 'N/A'
        
        writer.writerow([
            stakeholder.id,
            stakeholder.name,
            stakeholder.email,
            stakeholder.phone,
            stakeholder.organization.name if stakeholder.organization else 'N/A',
            stakeholder.role,
            dict(Stakeholder.INFLUENCE_CHOICES).get(stakeholder.influence_level, 'Unknown'),
            stakeholder.notes.replace('\n', ' ').replace('\r', '') if stakeholder.notes else '',
            stakeholder.created_at.strftime('%Y-%m-%d'),
            stakeholder.updated_at.strftime('%Y-%m-%d'),
            project_count,
            last_comm_date
        ])
    
    return response

@login_required
def stakeholder_communications_report_csv(request):
    # Check permissions
    if not request.user.is_staff and request.user.role not in ['manager', 'executive']:
        return HttpResponse("Permission denied", status=403)
    
    # Get time period from request or default to last 30 days
    period = request.GET.get('period', '30d')
    
    # Set date range based on period
    if period == '7d':
        start_date = timezone.now() - timedelta(days=7)
        period_name = 'Last 7 Days'
    elif period == '30d':
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    elif period == '90d':
        start_date = timezone.now() - timedelta(days=90)
        period_name = 'Last 90 Days'
    elif period == 'ytd':
        start_date = timezone.datetime(timezone.now().year, 1, 1)
        period_name = 'Year to Date'
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = 'Last 30 Days'
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="stakeholder_communications_{period}_{timezone.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    
    # Write header
    writer.writerow([
        'Stakeholder Communications Report', period_name, 
        f'Generated on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
    ])
    writer.writerow([])
    
    writer.writerow([
        'Date', 'Stakeholder', 'Organization', 'Communication Type', 
        'Project', 'User', 'Notes'
    ])
    
    # Get communications
    communications = Activity.objects.filter(
        target_model='Communication',
        timestamp__gte=start_date
    ).order_by('-timestamp')
    
    # Apply filters if provided
    project_filter = request.GET.get('project')
    if project_filter:
        communications = communications.filter(project_id=project_filter)
    
    organization_filter = request.GET.get('organization')
    if organization_filter:
        # Get stakeholders in this organization
        org_stakeholders = Stakeholder.objects.filter(organization_id=organization_filter)
        stakeholder_names = [s.name for s in org_stakeholders]
        
        # Filter communications for these stakeholders
        filtered_communications = []
        for comm in communications:
            for name in stakeholder_names:
                if name in comm.description:
                    filtered_communications.append(comm)
                    break
        
        communications = filtered_communications
    
    # Write data
    for comm in communications:
        # Extract stakeholder name and communication type
        stakeholder_name = 'Unknown'
        org_name = 'Unknown'
        
        # Try to extract stakeholder name from target_name
        if ' with ' in comm.target_name:
            comm_type, stakeholder_part = comm.target_name.split(' with ', 1)
            
            # Try to match with a stakeholder
            for stakeholder in Stakeholder.objects.all():
                if stakeholder.name in stakeholder_part:
                    stakeholder_name = stakeholder.name
                    org_name = stakeholder.organization.name if stakeholder.organization else 'N/A'
                    break
        else:
            comm_type = comm.target_name
            
            # Try to extract stakeholder from description
            for stakeholder in Stakeholder.objects.all():
                if stakeholder.name in comm.description:
                    stakeholder_name = stakeholder.name
                    org_name = stakeholder.organization.name if stakeholder.organization else 'N/A'
                    break
        
        writer.writerow([
            comm.timestamp.strftime('%Y-%m-%d %H:%M'),
            stakeholder_name,
            org_name,
            comm_type,
            comm.project.name if comm.project else 'N/A',
            comm.user.get_full_name() if comm.user else 'System',
            comm.description.replace('\n', ' ').replace('\r', '') if comm.description else ''
        ])
    
    return response

@login_required
def stakeholder_project_assignments_report_csv(request):
    # Check permissions
    if not request.user.is_staff and request.user.role not in ['manager', 'executive']:
        return HttpResponse("Permission denied", status=403)
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="stakeholder_project_assignments_{timezone.now().strftime("%Y%m%d")}.csv"'
    
    writer = csv.writer(response)
    
    # Write header
    writer.writerow([
        'Stakeholder Project Assignments Report',
        f'Generated on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
    ])
    writer.writerow([])
    
    writer.writerow([
        'Project', 'Status', 'Stakeholder', 'Organization', 'Role',
        'Influence Level', 'Communication Count', 'Last Communication'
    ])
    
    # Get all projects
    projects = Project.objects.all()
    
    # Apply filters if provided
    project_filter = request.GET.get('project')
    if project_filter:
        projects = projects.filter(id=project_filter)
    
    organization_filter = request.GET.get('organization')
    if organization_filter:
        # Filter projects that have stakeholders from this organization
        org_stakeholders = Stakeholder.objects.filter(organization_id=organization_filter)
        projects = projects.filter(stakeholders__in=org_stakeholders).distinct()
    
    # Write data
    for project in projects:
        # Get stakeholders for this project
        stakeholders = project.stakeholders.all()
        
        for stakeholder in stakeholders:
            # Get communication count
            communication_count = Activity.objects.filter(
                target_model='Communication',
                project=project,
                description__icontains=stakeholder.name
            ).count()
            
            # Get last communication
            last_communication = Activity.objects.filter(
                target_model='Communication',
                project=project,
                description__icontains=stakeholder.name
            ).order_by('-timestamp').first()
            
            last_comm_date = last_communication.timestamp.strftime('%Y-%m-%d') if last_communication else 'N/A'
            
            writer.writerow([
                project.name,
                project.status,
                stakeholder.name,
                stakeholder.organization.name if stakeholder.organization else 'N/A',
                stakeholder.role,
                dict(Stakeholder.INFLUENCE_CHOICES).get(stakeholder.influence_level, 'Unknown'),
                communication_count,
                last_comm_date
            ])
    
    return response