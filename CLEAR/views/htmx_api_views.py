"""
HTMX API Views for the CLEAR application.

This module provides HTMX-powered views that replace the REST API endpoints
with hypermedia-driven alternatives that return HTML fragments instead of JSON.
These views follow the Hypermedia-Driven Application (HDA) architecture principles.
"""


import logging
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.views.decorators.http import require_http_methods
from django.contrib.auth import authenticate, login, logout
from ..models import (
    Activity,
    ChatMessage,
    Conflict,
    Notification,
    Project,
    Task,
    User,
    Utility,
    WhisperMessage,
)

logger = logging.getLogger(__name__)


# Authentication endpoints

@require_http_methods(["GET", "POST"])
def login_htmx(request):
    """HTMX-powered login view that replaces LoginAPIView"""
    if request.method == "POST":
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        if username and password:
            user = authenticate(request, username=username, password=password)
            if user:
                login(request, user)
                # Return a redirect response for successful login
                return HttpResponse(
                    headers={
                        'HX-Redirect': '/dashboard/'
                    }
                )
            else:
                # Return login form with error message
                return render(request, 'auth/login_form.html', {
                    'error': 'Invalid credentials',
                    'username': username
                })
        
        # Return login form with error message
        return render(request, 'auth/login_form.html', {
            'error': 'Username and password required'
        })
    
    # GET request - return the login form
    return render(request, 'auth/login_form.html')


@login_required
@require_http_methods(["POST"])
def logout_htmx(request):
    """HTMX-powered logout view that replaces LogoutAPIView"""
    logout(request)
    return HttpResponse(
        headers={
            'HX-Redirect': '/login/'
        }
    )


@login_required
def current_user_htmx(request):
    """HTMX-powered current user view that replaces CurrentUserAPIView"""
    return render(request, 'auth/current_user.html', {
        'user': request.user
    })


# Dashboard data endpoints

@login_required
def dashboard_stats_htmx(request):
    """HTMX-powered dashboard stats view that replaces DashboardStatsAPIView"""
    user = request.user
    
    # Get user's projects
    projects = Project.objects.filter(
        egis_project_manager=user.username
    )
    
    stats = {
        'total_projects': projects.count(),
        'active_projects': projects.filter(rag_status='Green').count(),
        'warning_projects': projects.filter(rag_status='Yellow').count(),
        'critical_projects': projects.filter(rag_status='Red').count(),
        'total_utilities': Utility.objects.filter(project__in=projects).count(),
        'open_conflicts': Conflict.objects.filter(
            project__in=projects, 
            status='open'
        ).count(),
        'pending_tasks': Task.objects.filter(
            project__in=projects,
            completed=False
        ).count(),
    }
    
    return render(request, 'components/dashboard_stats.html', stats)


@login_required
def recent_activity_htmx(request):
    """HTMX-powered recent activity view that replaces RecentActivityAPIView"""
    # Get recent activities
    activities = Activity.objects.filter(
        Q(user=request.user) | Q(project__in=request.user.projects.all())
    ).select_related('user').order_by('-created_at')[:10]
    
    return render(request, 'components/recent_activity.html', {
        'activities': activities
    })


# Project endpoints

@login_required
def projects_list_htmx(request):
    """HTMX-powered projects list view that replaces ProjectViewSet.list"""
    # Get query parameters
    search = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 25))
    
    # Filter projects
    queryset = Project.objects.all()
    
    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) | Q(client__icontains=search)
        )
    
    if status_filter:
        queryset = queryset.filter(rag_status=status_filter)
    
    # Order by created_at
    queryset = queryset.order_by('-created_at')
    
    # Simple pagination
    start = (page - 1) * page_size
    end = start + page_size
    
    projects = queryset[start:end]
    total = queryset.count()
    
    # Calculate total pages
    total_pages = (total + page_size - 1) // page_size
    
    return render(request, 'projects/projects_list.html', {
        'projects': projects,
        'total': total,
        'page': page,
        'page_size': page_size,
        'total_pages': total_pages,
        'search': search,
        'status_filter': status_filter
    })


@login_required
def project_detail_htmx(request, project_id):
    """HTMX-powered project detail view that replaces ProjectViewSet.retrieve"""
    project = get_object_or_404(Project, pk=project_id)
    
    return render(request, 'projects/project_detail.html', {
        'project': project
    })


# Utility endpoints

@login_required
def utilities_list_htmx(request):
    """HTMX-powered utilities list view that replaces UtilityViewSet.list"""
    # Get query parameters
    project_id = request.GET.get('project_id', '')
    
    # Filter utilities
    queryset = Utility.objects.select_related('project')
    
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    
    # Order by type and name
    utilities = queryset.order_by('type', 'name')
    
    return render(request, 'utilities/utilities_list.html', {
        'utilities': utilities,
        'project_id': project_id
    })


# Conflict endpoints

@login_required
def conflicts_list_htmx(request):
    """HTMX-powered conflicts list view that replaces ConflictViewSet.list"""
    # Get query parameters
    project_id = request.GET.get('project_id', '')
    status_filter = request.GET.get('status', '')
    
    # Filter conflicts
    queryset = Conflict.objects.select_related('project', 'utility', 'utility2')
    
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    
    if status_filter:
        queryset = queryset.filter(status=status_filter)
    
    # Order by created_at
    conflicts = queryset.order_by('-created_at')
    
    return render(request, 'conflicts/conflicts_list.html', {
        'conflicts': conflicts,
        'project_id': project_id,
        'status_filter': status_filter
    })


# Task endpoints

@login_required
def tasks_list_htmx(request):
    """HTMX-powered tasks list view that replaces TaskViewSet.list"""
    # Get query parameters
    project_id = request.GET.get('project_id', '')
    my_tasks = request.GET.get('my_tasks', '') == 'true'
    
    # Filter tasks
    queryset = Task.objects.select_related('project', 'assigned_to')
    
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    
    if my_tasks:
        queryset = queryset.filter(assigned_to=request.user)
    
    # Order by due_date
    tasks = queryset.order_by('due_date')
    
    return render(request, 'tasks/tasks_list.html', {
        'tasks': tasks,
        'project_id': project_id,
        'my_tasks': my_tasks
    })


# Notification endpoints

@login_required
def notifications_list_htmx(request):
    """HTMX-powered notifications list view that replaces NotificationViewSet.list"""
    # Users can only see their own notifications
    notifications = request.user.notifications.order_by('-created_at')
    
    return render(request, 'notifications/notifications_list.html', {
        'notifications': notifications
    })


@login_required
@require_http_methods(["POST"])
def mark_notification_read_htmx(request, notification_id):
    """HTMX-powered mark notification read view that replaces NotificationViewSet.mark_read"""
    notification = get_object_or_404(Notification, pk=notification_id, user=request.user)
    notification.read = True
    notification.save()
    
    return HttpResponse(
        headers={
            'HX-Trigger': 'notificationMarkedRead'
        }
    )


@login_required
@require_http_methods(["POST"])
def mark_all_notifications_read_htmx(request):
    """HTMX-powered mark all notifications read view that replaces NotificationViewSet.mark_all_read"""
    request.user.notifications.filter(read=False).update(read=True)
    
    return HttpResponse(
        headers={
            'HX-Trigger': 'allNotificationsMarkedRead'
        }
    )


# Chat endpoints

@login_required
def chat_messages_htmx(request):
    """HTMX-powered chat messages view that replaces ChatMessageAPIView.get"""
    # Get query parameters
    channel = request.GET.get('channel', 'general')
    project_id = request.GET.get('project_id', '')
    
    # Filter messages
    queryset = ChatMessage.objects.filter(channel=channel)
    if project_id:
        queryset = queryset.filter(project_id=project_id)
    
    messages = queryset.select_related('user').order_by('-created_at')[:50]
    
    return render(request, 'messaging/chat_messages.html', {
        'messages': reversed(messages),
        'channel': channel,
        'project_id': project_id
    })


@login_required
@require_http_methods(["POST"])
def send_chat_message_htmx(request):
    """HTMX-powered send chat message view that replaces ChatMessageAPIView.post"""
    content = request.POST.get('content', '')
    channel = request.POST.get('channel', 'general')
    project_id = request.POST.get('project_id', '')
    
    if not content:
        return HttpResponse(
            '<div class="text-red-500">Message content is required</div>',
            status=400
        )
    
    message = ChatMessage.objects.create(
        user=request.user,
        content=content,
        channel=channel,
        project_id=project_id if project_id else None
    )
    
    # Return the new message HTML
    return render(request, 'messaging/chat_message.html', {
        'message': message
    })


# Whisper endpoints

@login_required
def whisper_messages_htmx(request):
    """HTMX-powered whisper messages view that replaces WhisperMessageAPIView.get"""
    messages = WhisperMessage.objects.filter(
        recipient=request.user
    ).select_related('sender').order_by('-created_at')
    
    return render(request, 'messaging/whisper_messages.html', {
        'messages': messages
    })


@login_required
@require_http_methods(["POST"])
def send_whisper_message_htmx(request):
    """HTMX-powered send whisper message view that replaces WhisperMessageAPIView.post"""
    message_text = request.POST.get('message', '')
    recipient_id = request.POST.get('recipient_id', '') or request.POST.get('to_user_id', '')
    
    if not message_text or not recipient_id:
        return HttpResponse(
            '<div class="text-red-500">Message and recipient are required</div>',
            status=400
        )
    
    try:
        recipient = User.objects.get(id=recipient_id)
    except User.DoesNotExist:
        return HttpResponse(
            '<div class="text-red-500">User not found</div>',
            status=404
        )
    
    WhisperMessage.objects.create(
        sender=request.user,
        recipient=recipient,
        message=message_text
    )
    
    # Return success message
    return HttpResponse(
        '<div class="text-green-500">Message sent successfully</div>'
    )