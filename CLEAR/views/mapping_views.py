"""
Mapping and GIS Views

This module contains views related to mapping, GIS functionality, and spatial analysis.
"""


import logging
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.generic import TemplateView
from .imports_base import *
from CLEAR.feature_flags import FeatureFlags


logger = logging.getLogger(__name__)



class ProjectMapView(LoginRequiredMixin, TemplateView):
    template_name = 'projects/project_map.html'
    
    def get_template_names(self):
        """Return OpenLayers template if feature flag is enabled."""
        
        if FeatureFlags.is_enabled('USE_OPENLAYERS'):
            return ['CLEAR/projects/project_map_openlayers.html']
        return [self.template_name]
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs.get('project_id')
        project = get_object_or_404(Project, pk=project_id)
        
        # Check if user has access to this project
        if not (self.request.user.is_staff or 
                project.created_by == self.request.user or 
                project.members.filter(id=self.request.user.id).exists()):
            raise PermissionDenied
        
        # Get coordinate systems for spatial toolbar
        coordinate_systems = CoordinateSystem.objects.all()
        
        # Get utilities and conflicts for the project
        utilities = UtilityLineData.objects.filter(project=project)
        conflicts = Conflict.objects.filter(project=project)
        
        # Add feature flag information
        use_openlayers = FeatureFlags.is_enabled('USE_OPENLAYERS')
        
        context.update({
            'project': project,
            'coordinate_systems': coordinate_systems,
            'utilities': utilities,
            'conflicts': conflicts,
            'use_openlayers': use_openlayers,
            'map_library': 'openlayers' if use_openlayers else 'leaflet'
        })
        
        return context

class ProjectThreeDView(LoginRequiredMixin, TemplateView):
    template_name = 'projects/project_3d.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs.get('project_id')
        project = get_object_or_404(Project, pk=project_id)
        
        # Check if user has access to this project
        if not (self.request.user.is_staff or 
                project.created_by == self.request.user or 
                project.members.filter(id=self.request.user.id).exists()):
            raise PermissionDenied
        
        # Get project utilities
        utilities = UtilityLineData.objects.filter(project=project)
        
        # Get conflicts
        conflicts = Conflict.objects.filter(project=project)
        
        # Get GIS layers
        layers = GISLayer.objects.filter(project=project)
        
        context.update({
            'project': project,
            'utilities': utilities,
            'conflicts': conflicts,
            'layers': layers,
            'utility_count': utilities.count(),
            'conflict_count': conflicts.count()
        })
        
        return context

class GISProfessionalView(LoginRequiredMixin, TemplateView):
    template_name = 'gis/professional.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get user's projects
        projects = Project.objects.filter(
            Q(members=self.request.user) | 
            Q(created_by=self.request.user)
        ).distinct()
        
        # Get available coordinate systems
        coordinate_systems = CoordinateSystem.objects.all()
        
        # Get available GIS layers
        if self.request.user.is_staff:
            layers = GISLayer.objects.all()
        else:
            layers = GISLayer.objects.filter(
                Q(project__members=self.request.user) |
                Q(project__created_by=self.request.user) |
                Q(is_public=True)
            ).distinct()
        
        # Get utility line styles
        line_styles = LineStyle.objects.all()
        
        context.update({
            'projects': projects,
            'coordinate_systems': coordinate_systems,
            'layers': layers,
            'line_styles': line_styles,
            'utility_types': UtilityLineData.UTILITY_TYPES
        })
        
        return context

class GISTestView(LoginRequiredMixin, TemplateView):
    template_name = 'gis/test.html'

@login_required
def project_map_view(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return render(request, '403.html', status=403)
    
    # Get project utilities
    utilities = UtilityLineData.objects.filter(project=project)
    
    # Get conflicts
    conflicts = Conflict.objects.filter(project=project)
    
    # Get GIS layers
    layers = GISLayer.objects.filter(project=project)
    
    # Get available coordinate systems
    coordinate_systems = CoordinateSystem.objects.all()
    
    context = {
        'project': project,
        'utilities': utilities,
        'conflicts': conflicts,
        'layers': layers,
        'coordinate_systems': coordinate_systems,
        'utility_count': utilities.count(),
        'conflict_count': conflicts.count()
    }
    
    return render(request, 'projects/project_map.html', context)

@login_required
def map_layers_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get GIS layers for this project
    project_layers = GISLayer.objects.filter(project=project)
    
    # Get public layers
    public_layers = GISLayer.objects.filter(is_public=True)
    
    # Get organization layers if user is in an organization
    org_layers = []
    if request.user.organization:
        org_layers = GISLayer.objects.filter(
            organization=request.user.organization
        ).exclude(id__in=project_layers.values_list('id', flat=True))
    
    # Get layer visibility settings from request
    visible_layers = request.GET.getlist('visible_layers')
    
    context = {
        'project': project,
        'project_layers': project_layers,
        'public_layers': public_layers,
        'org_layers': org_layers,
        'visible_layers': visible_layers
    }
    
    return render(request, 'projects/partials/map_layers.html', context)

@login_required
def utility_conflicts_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get conflicts for this project
    conflicts = Conflict.objects.filter(project=project)
    
    # Apply filters if provided
    status_filter = request.GET.get('status')
    if status_filter:
        conflicts = conflicts.filter(status=status_filter)
    
    severity_filter = request.GET.get('severity')
    if severity_filter:
        conflicts = conflicts.filter(severity=severity_filter)
    
    utility_type_filter = request.GET.get('utility_type')
    if utility_type_filter:
        conflicts = conflicts.filter(
            Q(utility1__utility_type=utility_type_filter) |
            Q(utility2__utility_type=utility_type_filter)
        )
    
    # Get conflict statistics
    total_conflicts = conflicts.count()
    open_conflicts = conflicts.filter(status='open').count()
    resolved_conflicts = conflicts.filter(status='resolved').count()
    critical_conflicts = conflicts.filter(severity='critical').count()
    
    context = {
        'project': project,
        'conflicts': conflicts,
        'total_conflicts': total_conflicts,
        'open_conflicts': open_conflicts,
        'resolved_conflicts': resolved_conflicts,
        'critical_conflicts': critical_conflicts,
        'current_filters': {
            'status': status_filter,
            'severity': severity_filter,
            'utility_type': utility_type_filter
        }
    }
    
    return render(request, 'projects/partials/utility_conflicts.html', context)

@login_required
def spatial_search_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get search parameters
    search_type = request.GET.get('search_type', 'point')
    x_coord = request.GET.get('x_coord')
    y_coord = request.GET.get('y_coord')
    radius = request.GET.get('radius', 100)  # Default 100m radius
    buffer_distance = request.GET.get('buffer_distance', 10)  # Default 10m buffer
    utility_type = request.GET.get('utility_type')
    
    results = []
    
    if search_type == 'point' and x_coord and y_coord:
        try:
            # Convert coordinates to float
            x = float(x_coord)
            y = float(y_coord)
            radius = float(radius)
            
            # Create point
            point = Point(x, y, srid=4326)  # Assuming WGS84
            
            # Transform to project's coordinate system if needed
            if project.coordinate_system and project.coordinate_system.srid != 4326:
                point.transform(project.coordinate_system.srid)
            
            # Query utilities within radius
            utilities = UtilityLineData.objects.filter(
                project=project,
                geometry_2d__dwithin=(point, D(m=radius))
            )
            
            # Apply utility type filter if provided
            if utility_type:
                utilities = utilities.filter(utility_type=utility_type)
            
            # Calculate distance for each utility
            for utility in utilities:
                distance = utility.geometry_2d.distance(point)
                results.append({
                    'utility': utility,
                    'distance': round(distance, 2),
                    'type': 'utility'
                })
            
            # Sort by distance
            results.sort(key=lambda x: x['distance'])
            
        except (ValueError, TypeError):
            return HttpResponse("Invalid coordinates", status=400)
    
    elif search_type == 'line' and request.GET.get('line_coords'):
        try:
            # Parse line coordinates
            line_coords = json.loads(request.GET.get('line_coords'))
            buffer_distance = float(buffer_distance)
            
            # Create line
            line = GEOSGeometry(json.dumps({
                'type': 'LineString',
                'coordinates': line_coords
            }), srid=4326)
            
            # Transform to project's coordinate system if needed
            if project.coordinate_system and project.coordinate_system.srid != 4326:
                line.transform(project.coordinate_system.srid)
            
            # Create buffer
            buffer = line.buffer(buffer_distance)
            
            # Query utilities that intersect with buffer
            utilities = UtilityLineData.objects.filter(
                project=project,
                geometry_2d__intersects=buffer
            )
            
            # Apply utility type filter if provided
            if utility_type:
                utilities = utilities.filter(utility_type=utility_type)
            
            # Add to results
            for utility in utilities:
                results.append({
                    'utility': utility,
                    'type': 'utility'
                })
            
        except (ValueError, TypeError, json.JSONDecodeError):
            return HttpResponse("Invalid line coordinates", status=400)
    
    elif search_type == 'polygon' and request.GET.get('polygon_coords'):
        try:
            # Parse polygon coordinates
            polygon_coords = json.loads(request.GET.get('polygon_coords'))
            
            # Create polygon
            polygon = GEOSGeometry(json.dumps({
                'type': 'Polygon',
                'coordinates': [polygon_coords]  # Outer ring
            }), srid=4326)
            
            # Transform to project's coordinate system if needed
            if project.coordinate_system and project.coordinate_system.srid != 4326:
                polygon.transform(project.coordinate_system.srid)
            
            # Query utilities that intersect with polygon
            utilities = UtilityLineData.objects.filter(
                project=project,
                geometry_2d__intersects=polygon
            )
            
            # Apply utility type filter if provided
            if utility_type:
                utilities = utilities.filter(utility_type=utility_type)
            
            # Add to results
            for utility in utilities:
                results.append({
                    'utility': utility,
                    'type': 'utility'
                })
            
        except (ValueError, TypeError, json.JSONDecodeError):
            return HttpResponse("Invalid polygon coordinates", status=400)
    
    context = {
        'project': project,
        'results': results,
        'search_type': search_type,
        'utility_types': UtilityLineData.UTILITY_TYPES
    }
    
    return render(request, 'projects/partials/spatial_search_results.html', context)

@login_required
def map_geojson_data(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Get data type from request
    data_type = request.GET.get('type', 'utilities')
    
    # Get layer IDs if specified
    layer_ids = request.GET.getlist('layers')
    
    # Prepare response data
    response_data = {
        'type': 'FeatureCollection',
        'features': []
    }
    
    if data_type == 'utilities':
        # Get utilities for this project
        utilities = UtilityLineData.objects.filter(project=project)
        
        # Filter by utility type if specified
        utility_type = request.GET.get('utility_type')
        if utility_type:
            utilities = utilities.filter(utility_type=utility_type)
        
        # Convert to GeoJSON
        for utility in utilities:
            # Skip if geometry is invalid
            if not utility.geometry_2d:
                continue
            
            # Get style information
            style = {}
            if utility.line_style:
                style = {
                    'color': utility.line_style.color,
                    'weight': utility.line_style.width,
                    'opacity': utility.line_style.opacity,
                    'dashArray': utility.line_style.dash_array
                }
            else:
                # Default style based on utility type
                if utility.utility_type == 'water':
                    style = {'color': '#0000FF', 'weight': 3}
                elif utility.utility_type == 'gas':
                    style = {'color': '#FFFF00', 'weight': 3}
                elif utility.utility_type == 'electric':
                    style = {'color': '#FF0000', 'weight': 3}
                elif utility.utility_type == 'telecom':
                    style = {'color': '#FFA500', 'weight': 3}
                elif utility.utility_type == 'sewer':
                    style = {'color': '#A52A2A', 'weight': 3}
                elif utility.utility_type == 'storm':
                    style = {'color': '#00FF00', 'weight': 3}
                else:
                    style = {'color': '#808080', 'weight': 3}
            
            # Create feature
            feature = {
                'type': 'Feature',
                'geometry': json.loads(utility.geometry_2d.json),
                'properties': {
                    'id': utility.id,
                    'name': utility.name,
                    'utility_type': utility.utility_type,
                    'depth': utility.depth,
                    'diameter': utility.diameter,
                    'material': utility.material,
                    'owner': utility.owner,
                    'style': style
                }
            }
            
            response_data['features'].append(feature)
    
    elif data_type == 'conflicts':
        # Get conflicts for this project
        conflicts = Conflict.objects.filter(project=project)
        
        # Filter by status if specified
        status = request.GET.get('status')
        if status:
            conflicts = conflicts.filter(status=status)
        
        # Filter by severity if specified
        severity = request.GET.get('severity')
        if severity:
            conflicts = conflicts.filter(severity=severity)
        
        # Convert to GeoJSON
        for conflict in conflicts:
            # Skip if location is invalid
            if not conflict.location:
                continue
            
            # Get style information based on severity
            style = {}
            if conflict.severity == 'critical':
                style = {'color': '#FF0000', 'fillColor': '#FF0000', 'radius': 8}
            elif conflict.severity == 'major':
                style = {'color': '#FFA500', 'fillColor': '#FFA500', 'radius': 7}
            elif conflict.severity == 'moderate':
                style = {'color': '#FFFF00', 'fillColor': '#FFFF00', 'radius': 6}
            else:  # minor
                style = {'color': '#00FF00', 'fillColor': '#00FF00', 'radius': 5}
            
            # Create feature
            feature = {
                'type': 'Feature',
                'geometry': json.loads(conflict.location.json),
                'properties': {
                    'id': conflict.id,
                    'description': conflict.description,
                    'status': conflict.status,
                    'severity': conflict.severity,
                    'utility1_id': conflict.utility1.id if conflict.utility1 else None,
                    'utility2_id': conflict.utility2.id if conflict.utility2 else None,
                    'style': style
                }
            }
            
            response_data['features'].append(feature)
    
    elif data_type == 'layers':
        # Get GIS layers
        layers = GISLayer.objects.filter(id__in=layer_ids) if layer_ids else GISLayer.objects.filter(
            Q(project=project) | Q(is_public=True)
        )
        
        # Convert to GeoJSON
        for layer in layers:
            # Skip if geometry is invalid
            if not layer.geometry:
                continue
            
            # Create feature
            feature = {
                'type': 'Feature',
                'geometry': json.loads(layer.geometry.json),
                'properties': {
                    'id': layer.id,
                    'name': layer.name,
                    'description': layer.description,
                    'layer_type': layer.layer_type,
                    'style': {
                        'color': layer.color,
                        'fillColor': layer.fill_color,
                        'weight': layer.line_width,
                        'opacity': layer.opacity,
                        'fillOpacity': layer.fill_opacity
                    }
                }
            }
            
            response_data['features'].append(feature)
    
    return JsonResponse(response_data)

@login_required
def mapping_utility_data_geojson(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Get utilities for this project
    utilities = UtilityLineData.objects.filter(project=project)
    
    # Filter by utility type if specified
    utility_type = request.GET.get('utility_type')
    if utility_type:
        utilities = utilities.filter(utility_type=utility_type)
    
    # Prepare GeoJSON response
    features = []
    
    for utility in utilities:
        # Skip if geometry is invalid
        if not utility.geometry_2d:
            continue
        
        # Get style information
        style = {}
        if utility.line_style:
            style = {
                'color': utility.line_style.color,
                'weight': utility.line_style.width,
                'opacity': utility.line_style.opacity,
                'dashArray': utility.line_style.dash_array
            }
        else:
            # Default style based on utility type
            if utility.utility_type == 'water':
                style = {'color': '#0000FF', 'weight': 3}
            elif utility.utility_type == 'gas':
                style = {'color': '#FFFF00', 'weight': 3}
            elif utility.utility_type == 'electric':
                style = {'color': '#FF0000', 'weight': 3}
            elif utility.utility_type == 'telecom':
                style = {'color': '#FFA500', 'weight': 3}
            elif utility.utility_type == 'sewer':
                style = {'color': '#A52A2A', 'weight': 3}
            elif utility.utility_type == 'storm':
                style = {'color': '#00FF00', 'weight': 3}
            else:
                style = {'color': '#808080', 'weight': 3}
        
        # Create feature
        feature = {
            'type': 'Feature',
            'geometry': json.loads(utility.geometry_2d.json),
            'properties': {
                'id': utility.id,
                'name': utility.name,
                'utility_type': utility.utility_type,
                'depth': utility.depth,
                'diameter': utility.diameter,
                'material': utility.material,
                'owner': utility.owner,
                'style': style
            }
        }
        
        features.append(feature)
    
    geojson = {
        'type': 'FeatureCollection',
        'features': features
    }
    
    return JsonResponse(geojson)

@login_required
def project_utilities_geojson(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Get utilities for this project
    utilities = UtilityLineData.objects.filter(project=project)
    
    # Filter by utility type if specified
    utility_type = request.GET.get('utility_type')
    if utility_type:
        utilities = utilities.filter(utility_type=utility_type)
    
    # Prepare GeoJSON response
    features = []
    
    for utility in utilities:
        # Skip if geometry is invalid
        if not utility.geometry_2d:
            continue
        
        # Create feature
        feature = {
            'type': 'Feature',
            'geometry': json.loads(utility.geometry_2d.json),
            'properties': {
                'id': utility.id,
                'name': utility.name,
                'utility_type': utility.utility_type,
                'depth': utility.depth,
                'diameter': utility.diameter,
                'material': utility.material,
                'owner': utility.owner
            }
        }
        
        features.append(feature)
    
    geojson = {
        'type': 'FeatureCollection',
        'features': features
    }
    
    return JsonResponse(geojson)

@login_required
def project_boundary_geojson(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Check if project has a boundary
    if not project.boundary:
        return JsonResponse({
            'type': 'FeatureCollection',
            'features': []
        })
    
    # Create GeoJSON feature
    feature = {
        'type': 'Feature',
        'geometry': json.loads(project.boundary.json),
        'properties': {
            'id': project.id,
            'name': project.name,
            'style': {
                'color': '#FF0000',
                'weight': 3,
                'opacity': 0.7,
                'fillColor': '#FF0000',
                'fillOpacity': 0.1
            }
        }
    }
    
    geojson = {
        'type': 'FeatureCollection',
        'features': [feature]
    }
    
    return JsonResponse(geojson)

@login_required
def project_coordinate_systems(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get all coordinate systems
    coordinate_systems = CoordinateSystem.objects.all()
    
    # Get current coordinate system
    current_system = project.coordinate_system
    
    if request.method == 'POST':
        # Update project coordinate system
        system_id = request.POST.get('coordinate_system')
        
        if system_id:
            try:
                system = CoordinateSystem.objects.get(pk=system_id)
                project.coordinate_system = system
                project.save()
                
                # Create activity record
                Activity.objects.create(
                    user=request.user,
                    action='updated',
                    target_model='Project',
                    target_id=project.id,
                    target_name=project.name,
                    description=f"Updated coordinate system to {system.name}",
                    project=project
                )
                
                messages.success(request, f'Coordinate system updated to {system.name}')
                
                # Return updated coordinate system info
                return render(request, 'projects/partials/coordinate_system_info.html', {
                    'project': project,
                    'coordinate_system': system
                })
                
            except CoordinateSystem.DoesNotExist:
                return HttpResponse("Invalid coordinate system", status=400)
        
        return HttpResponse("Coordinate system ID is required", status=400)
    
    # GET request - show form
    context = {
        'project': project,
        'coordinate_systems': coordinate_systems,
        'current_system': current_system
    }
    
    return render(request, 'projects/partials/coordinate_system_form.html', context)

@login_required
def htmx_project_conflict_detection(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Run conflict detection
        buffer_distance = request.POST.get('buffer_distance', 1.0)
        try:
            buffer_distance = float(buffer_distance)
        except ValueError:
            buffer_distance = 1.0  # Default to 1.0 meter if invalid
        
        # Get utilities for this project
        utilities = UtilityLineData.objects.filter(project=project)
        
        # Track new conflicts
        new_conflicts = 0
        
        # Check each pair of utilities for conflicts
        for i, utility1 in enumerate(utilities):
            for utility2 in utilities[i+1:]:
                # Skip if either utility has no geometry
                if not utility1.geometry_2d or not utility2.geometry_2d:
                    continue
                
                # Create buffers
                buffer1 = utility1.geometry_2d.buffer(buffer_distance)
                buffer2 = utility2.geometry_2d.buffer(buffer_distance)
                
                # Check for intersection
                if buffer1.intersects(buffer2):
                    # Get intersection point
                    intersection = buffer1.intersection(buffer2)
                    
                    # Check if conflict already exists
                    existing_conflict = Conflict.objects.filter(
                        project=project,
                        utility1=utility1,
                        utility2=utility2
                    ).exists()
                    
                    if not existing_conflict:
                        # Create new conflict
                        Conflict.objects.create(
                            project=project,
                            utility1=utility1,
                            utility2=utility2,
                            location=intersection.centroid,
                            description=f"Potential conflict between {utility1.name} and {utility2.name}",
                            status='open',
                            severity='moderate',  # Default severity
                            detected_by=request.user
                        )
                        
                        new_conflicts += 1
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='ran',
            target_model='ConflictDetection',
            target_id=0,
            target_name='Conflict Detection',
            description=f"Ran conflict detection with {buffer_distance}m buffer. Found {new_conflicts} new conflicts.",
            project=project
        )
        
        # Get updated conflicts
        conflicts = Conflict.objects.filter(project=project)
        
        # Get conflict statistics
        total_conflicts = conflicts.count()
        open_conflicts = conflicts.filter(status='open').count()
        resolved_conflicts = conflicts.filter(status='resolved').count()
        critical_conflicts = conflicts.filter(severity='critical').count()
        
        context = {
            'project': project,
            'conflicts': conflicts,
            'total_conflicts': total_conflicts,
            'open_conflicts': open_conflicts,
            'resolved_conflicts': resolved_conflicts,
            'critical_conflicts': critical_conflicts,
            'new_conflicts': new_conflicts
        }
        
        return render(request, 'projects/partials/conflict_detection_results.html', context)
    
    # GET request - show form
    return render(request, 'projects/partials/conflict_detection_form.html', {
        'project': project
    })

@login_required
def map_conflict_detection_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Run conflict detection
        buffer_distance = request.POST.get('buffer_distance', 1.0)
        try:
            buffer_distance = float(buffer_distance)
        except ValueError:
            buffer_distance = 1.0  # Default to 1.0 meter if invalid
        
        # Get utilities for this project
        utilities = UtilityLineData.objects.filter(project=project)
        
        # Track new conflicts
        new_conflicts = 0
        
        # Check each pair of utilities for conflicts
        for i, utility1 in enumerate(utilities):
            for utility2 in utilities[i+1:]:
                # Skip if either utility has no geometry
                if not utility1.geometry_2d or not utility2.geometry_2d:
                    continue
                
                # Create buffers
                buffer1 = utility1.geometry_2d.buffer(buffer_distance)
                buffer2 = utility2.geometry_2d.buffer(buffer_distance)
                
                # Check for intersection
                if buffer1.intersects(buffer2):
                    # Get intersection point
                    intersection = buffer1.intersection(buffer2)
                    
                    # Check if conflict already exists
                    existing_conflict = Conflict.objects.filter(
                        project=project,
                        utility1=utility1,
                        utility2=utility2
                    ).exists()
                    
                    if not existing_conflict:
                        # Create new conflict
                        Conflict.objects.create(
                            project=project,
                            utility1=utility1,
                            utility2=utility2,
                            location=intersection.centroid,
                            description=f"Potential conflict between {utility1.name} and {utility2.name}",
                            status='open',
                            severity='moderate',  # Default severity
                            detected_by=request.user
                        )
                        
                        new_conflicts += 1
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='ran',
            target_model='ConflictDetection',
            target_id=0,
            target_name='Conflict Detection',
            description=f"Ran conflict detection with {buffer_distance}m buffer. Found {new_conflicts} new conflicts.",
            project=project
        )
        
        # Get updated conflicts
        conflicts = Conflict.objects.filter(project=project)
        
        # Get conflict statistics
        total_conflicts = conflicts.count()
        open_conflicts = conflicts.filter(status='open').count()
        resolved_conflicts = conflicts.filter(status='resolved').count()
        critical_conflicts = conflicts.filter(severity='critical').count()
        
        context = {
            'project': project,
            'conflicts': conflicts,
            'total_conflicts': total_conflicts,
            'open_conflicts': open_conflicts,
            'resolved_conflicts': resolved_conflicts,
            'critical_conflicts': critical_conflicts,
            'new_conflicts': new_conflicts
        }
        
        return render(request, 'projects/partials/map_conflict_results.html', context)
    
    # GET request - show form
    return render(request, 'projects/partials/map_conflict_detection_form.html', {
        'project': project
    })

@login_required
def map_utility_info_htmx(request, project_id, utility_line_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get utility
    utility = get_object_or_404(UtilityLineData, pk=utility_line_id, project=project)
    
    return render(request, 'projects/partials/utility_info.html', {
        'utility': utility,
        'project': project
    })

@login_required
def spatial_analysis_buffer_zones(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        utility_type = request.POST.get('utility_type')
        buffer_distance = request.POST.get('buffer_distance', 5.0)
        
        try:
            buffer_distance = float(buffer_distance)
        except ValueError:
            buffer_distance = 5.0  # Default to 5.0 meters if invalid
        
        # Get utilities
        utilities = UtilityLineData.objects.filter(project=project)
        
        if utility_type and utility_type != 'all':
            utilities = utilities.filter(utility_type=utility_type)
        
        # Create buffer zones
        buffer_features = []
        
        for utility in utilities:
            # Skip if geometry is invalid
            if not utility.geometry_2d:
                continue
            
            # Create buffer
            buffer = utility.geometry_2d.buffer(buffer_distance)
            
            # Create feature
            feature = {
                'type': 'Feature',
                'geometry': json.loads(buffer.json),
                'properties': {
                    'id': utility.id,
                    'name': utility.name,
                    'utility_type': utility.utility_type,
                    'buffer_distance': buffer_distance
                }
            }
            
            buffer_features.append(feature)
        
        # Create GeoJSON
        geojson = {
            'type': 'FeatureCollection',
            'features': buffer_features
        }
        
        return JsonResponse(geojson)
    
    # GET request - show form
    return render(request, 'projects/partials/buffer_zone_form.html', {
        'project': project,
        'utility_types': UtilityLineData.UTILITY_TYPES
    })

@login_required
def spatial_analysis_clearance_violations(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        clearance_distance = request.POST.get('clearance_distance', 1.0)
        utility_type1 = request.POST.get('utility_type1')
        utility_type2 = request.POST.get('utility_type2')
        
        try:
            clearance_distance = float(clearance_distance)
        except ValueError:
            clearance_distance = 1.0  # Default to 1.0 meter if invalid
        
        # Get utilities of type 1
        utilities1 = UtilityLineData.objects.filter(project=project)
        if utility_type1 and utility_type1 != 'all':
            utilities1 = utilities1.filter(utility_type=utility_type1)
        
        # Get utilities of type 2
        utilities2 = UtilityLineData.objects.filter(project=project)
        if utility_type2 and utility_type2 != 'all':
            utilities2 = utilities2.filter(utility_type=utility_type2)
        
        # Find clearance violations
        violations = []
        
        for utility1 in utilities1:
            for utility2 in utilities2:
                # Skip if comparing the same utility
                if utility1.id == utility2.id:
                    continue
                
                # Skip if either utility has no geometry
                if not utility1.geometry_2d or not utility2.geometry_2d:
                    continue
                
                # Calculate distance
                distance = utility1.geometry_2d.distance(utility2.geometry_2d)
                
                # Check if distance is less than clearance distance
                if distance < clearance_distance:
                    # Find closest points
                    point1 = utility1.geometry_2d.interpolate(
                        utility1.geometry_2d.project(utility2.geometry_2d)
                    )
                    point2 = utility2.geometry_2d.interpolate(
                        utility2.geometry_2d.project(utility1.geometry_2d)
                    )
                    
                    # Create violation record
                    violation = {
                        'utility1': utility1,
                        'utility2': utility2,
                        'distance': distance,
                        'point1': point1,
                        'point2': point2,
                        'clearance_required': clearance_distance
                    }
                    
                    violations.append(violation)
        
        return render(request, 'projects/partials/clearance_violations_results.html', {
            'project': project,
            'violations': violations,
            'clearance_distance': clearance_distance,
            'utility_type1': utility_type1,
            'utility_type2': utility_type2
        })
    
    # GET request - show form
    return render(request, 'projects/partials/clearance_violations_form.html', {
        'project': project,
        'utility_types': UtilityLineData.UTILITY_TYPES
    })

@login_required
def spatial_analysis_spatial_query(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        query_type = request.POST.get('query_type', 'within')
        geometry_wkt = request.POST.get('geometry_wkt')
        
        if not geometry_wkt:
            return HttpResponse("Geometry is required", status=400)
        
        try:
            # Parse WKT
            geometry = GEOSGeometry(geometry_wkt, srid=4326)
            
            # Transform to project's coordinate system if needed
            if project.coordinate_system and project.coordinate_system.srid != 4326:
                geometry.transform(project.coordinate_system.srid)
            
            # Get utilities based on query type
            utilities = UtilityLineData.objects.filter(project=project)
            
            if query_type == 'within':
                utilities = utilities.filter(geometry_2d__within=geometry)
            elif query_type == 'intersects':
                utilities = utilities.filter(geometry_2d__intersects=geometry)
            elif query_type == 'contains':
                utilities = utilities.filter(geometry_2d__contains=geometry)
            elif query_type == 'dwithin':
                distance = request.POST.get('distance', 10.0)
                try:
                    distance = float(distance)
                except ValueError:
                    distance = 10.0  # Default to 10.0 meters if invalid
                
                utilities = utilities.filter(geometry_2d__dwithin=(geometry, D(m=distance)))
            
            return render(request, 'projects/partials/spatial_query_results.html', {
                'project': project,
                'utilities': utilities,
                'query_type': query_type,
                'geometry_wkt': geometry_wkt
            })
            
        except (ValueError, TypeError):
            return HttpResponse("Invalid geometry WKT", status=400)
    
    # GET request - show form
    return render(request, 'projects/partials/spatial_query_form.html', {
        'project': project
    })

@login_required
def spatial_collaboration_init(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get collaboration settings
    settings, created = CollaborationSettings.objects.get_or_create(
        project=project,
        defaults={
            'enable_annotations': True,
            'enable_drawing': True,
            'enable_chat': True,
            'enable_realtime': False  # Realtime collaboration requires additional setup
        }
    )
    
    # Get active users
    active_users = Activity.objects.filter(
        project=project,
        timestamp__gte=timezone.now() - timedelta(minutes=15)
    ).values('user').distinct()
    
    active_user_objects = User.objects.filter(id__in=[a['user'] for a in active_users if a['user']])
    
    context = {
        'project': project,
        'settings': settings,
        'active_users': active_user_objects
    }
    
    return render(request, 'projects/partials/spatial_collaboration_panel.html', context)

@login_required
def spatial_annotation_create_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        text = request.POST.get('text')
        geometry_json = request.POST.get('geometry')
        
        if not text or not geometry_json:
            return HttpResponse("Text and geometry are required", status=400)
        
        try:
            # Parse geometry
            geometry_data = json.loads(geometry_json)
            geometry = GEOSGeometry(json.dumps(geometry_data), srid=4326)
            
            # Create annotation
            annotation = SpatialAnnotation.objects.create(
                project=project,
                user=request.user,
                text=text,
                geometry=geometry
            )
            
            # Create activity
            Activity.objects.create(
                user=request.user,
                action='created',
                target_model='SpatialAnnotation',
                target_id=annotation.id,
                target_name='Spatial Annotation',
                description=f"Added annotation: {text[:50]}{'...' if len(text) > 50 else ''}",
                project=project
            )
            
            return render(request, 'projects/partials/spatial_annotation_item.html', {
                'annotation': annotation
            })
            
        except (ValueError, TypeError, json.JSONDecodeError):
            return HttpResponse("Invalid geometry", status=400)
    
    # GET request - show form
    return render(request, 'projects/partials/spatial_annotation_form.html', {
        'project': project
    })

@login_required
def spatial_annotations_list_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get annotations
    annotations = SpatialAnnotation.objects.filter(project=project).order_by('-created_at')
    
    return render(request, 'projects/partials/spatial_annotations_list.html', {
        'project': project,
        'annotations': annotations
    })

@login_required
def spatial_annotation_update_htmx(request, project_id, annotation_id):
    project = get_object_or_404(Project, pk=project_id)
    annotation = get_object_or_404(SpatialAnnotation, pk=annotation_id, project=project)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            annotation.user == request.user):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        text = request.POST.get('text')
        
        if not text:
            return HttpResponse("Text is required", status=400)
        
        # Update annotation
        annotation.text = text
        annotation.updated_at = timezone.now()
        annotation.save()
        
        # Create activity
        Activity.objects.create(
            user=request.user,
            action='updated',
            target_model='SpatialAnnotation',
            target_id=annotation.id,
            target_name='Spatial Annotation',
            description=f"Updated annotation: {text[:50]}{'...' if len(text) > 50 else ''}",
            project=project
        )
        
        return render(request, 'projects/partials/spatial_annotation_item.html', {
            'annotation': annotation
        })
    
    # GET request - show form
    return render(request, 'projects/partials/spatial_annotation_edit_form.html', {
        'project': project,
        'annotation': annotation
    })

@login_required
def spatial_annotation_delete_htmx(request, project_id, annotation_id):
    project = get_object_or_404(Project, pk=project_id)
    annotation = get_object_or_404(SpatialAnnotation, pk=annotation_id, project=project)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            annotation.user == request.user):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Store annotation text for activity log
        annotation_text = annotation.text
        
        # Delete annotation
        annotation.delete()
        
        # Create activity
        Activity.objects.create(
            user=request.user,
            action='deleted',
            target_model='SpatialAnnotation',
            target_id=annotation_id,
            target_name='Spatial Annotation',
            description=f"Deleted annotation: {annotation_text[:50]}{'...' if len(annotation_text) > 50 else ''}",
            project=project
        )
        
        # Return success message
        return HttpResponse("Annotation deleted successfully")
    
    # GET request - show confirmation
    return render(request, 'projects/partials/spatial_annotation_delete_confirm.html', {
        'project': project,
        'annotation': annotation
    })

@login_required
def spatial_collaboration_status_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get active users
    active_users = Activity.objects.filter(
        project=project,
        timestamp__gte=timezone.now() - timedelta(minutes=15)
    ).values('user').distinct()
    
    active_user_objects = User.objects.filter(id__in=[a['user'] for a in active_users if a['user']])
    
    # Record user activity
    Activity.objects.create(
        user=request.user,
        action='viewed',
        target_model='Project',
        target_id=project.id,
        target_name=project.name,
        description="Viewed project map",
        project=project
    )
    
    return render(request, 'projects/partials/spatial_collaboration_status.html', {
        'project': project,
        'active_users': active_user_objects
    })

@login_required
def spatial_drawing_create_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Get parameters
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        geometry_json = request.POST.get('geometry')
        drawing_type = request.POST.get('drawing_type', 'sketch')
        
        if not name or not geometry_json:
            return HttpResponse("Name and geometry are required", status=400)
        
        try:
            # Parse geometry
            geometry_data = json.loads(geometry_json)
            geometry = GEOSGeometry(json.dumps(geometry_data), srid=4326)
            
            # Create drawing as a GIS layer
            layer = GISLayer.objects.create(
                project=project,
                name=name,
                description=description,
                geometry=geometry,
                layer_type='drawing',
                created_by=request.user,
                color='#FF0000',  # Default color
                fill_color='#FF0000',
                opacity=0.8,
                fill_opacity=0.2,
                line_width=2,
                is_public=False,
                metadata={'drawing_type': drawing_type}
            )
            
            # Create activity
            Activity.objects.create(
                user=request.user,
                action='created',
                target_model='Drawing',
                target_id=layer.id,
                target_name=name,
                description=f"Added drawing: {name}",
                project=project
            )
            
            return render(request, 'projects/partials/spatial_drawing_item.html', {
                'layer': layer
            })
            
        except (ValueError, TypeError, json.JSONDecodeError):
            return HttpResponse("Invalid geometry", status=400)
    
    # GET request - show form
    return render(request, 'projects/partials/spatial_drawing_form.html', {
        'project': project
    })

@login_required
def spatial_analysis_panel_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get analysis type
    analysis_type = request.GET.get('type', 'buffer')
    
    if analysis_type == 'buffer':
        return render(request, 'projects/partials/buffer_zone_form.html', {
            'project': project,
            'utility_types': UtilityLineData.UTILITY_TYPES
        })
    elif analysis_type == 'clearance':
        return render(request, 'projects/partials/clearance_violations_form.html', {
            'project': project,
            'utility_types': UtilityLineData.UTILITY_TYPES
        })
    elif analysis_type == 'query':
        return render(request, 'projects/partials/spatial_query_form.html', {
            'project': project
        })
    elif analysis_type == 'conflict':
        return render(request, 'projects/partials/map_conflict_detection_form.html', {
            'project': project
        })
    else:
        return HttpResponse("Invalid analysis type", status=400)

@login_required
def run_conflict_detection_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Run conflict detection
        buffer_distance = request.POST.get('buffer_distance', 1.0)
        try:
            buffer_distance = float(buffer_distance)
        except ValueError:
            buffer_distance = 1.0  # Default to 1.0 meter if invalid
        
        # Get utilities for this project
        utilities = UtilityLineData.objects.filter(project=project)
        
        # Track new conflicts
        new_conflicts = 0
        total_checks = 0
        
        # Check each pair of utilities for conflicts
        for i, utility1 in enumerate(utilities):
            for utility2 in utilities[i+1:]:
                total_checks += 1
                
                # Skip if either utility has no geometry
                if not utility1.geometry_2d or not utility2.geometry_2d:
                    continue
                
                # Create buffers
                buffer1 = utility1.geometry_2d.buffer(buffer_distance)
                buffer2 = utility2.geometry_2d.buffer(buffer_distance)
                
                # Check for intersection
                if buffer1.intersects(buffer2):
                    # Get intersection point
                    intersection = buffer1.intersection(buffer2)
                    
                    # Check if conflict already exists
                    existing_conflict = Conflict.objects.filter(
                        project=project,
                        utility1=utility1,
                        utility2=utility2
                    ).exists()
                    
                    if not existing_conflict:
                        # Create new conflict
                        Conflict.objects.create(
                            project=project,
                            utility1=utility1,
                            utility2=utility2,
                            location=intersection.centroid,
                            description=f"Potential conflict between {utility1.name} ({utility1.utility_type}) and {utility2.name} ({utility2.utility_type})",
                            status='open',
                            severity='moderate',  # Default severity
                            detected_by=request.user
                        )
                        
                        new_conflicts += 1
        
        # Create activity record
        Activity.objects.create(
            user=request.user,
            action='ran',
            target_model='ConflictDetection',
            target_id=0,
            target_name='Conflict Detection',
            description=f"Ran conflict detection with {buffer_distance}m buffer. Found {new_conflicts} new conflicts from {total_checks} utility pairs.",
            project=project
        )
        
        # Get updated conflicts
        conflicts = Conflict.objects.filter(project=project)
        
        # Get conflict statistics
        total_conflicts = conflicts.count()
        open_conflicts = conflicts.filter(status='open').count()
        resolved_conflicts = conflicts.filter(status='resolved').count()
        critical_conflicts = conflicts.filter(severity='critical').count()
        
        context = {
            'project': project,
            'conflicts': conflicts,
            'total_conflicts': total_conflicts,
            'open_conflicts': open_conflicts,
            'resolved_conflicts': resolved_conflicts,
            'critical_conflicts': critical_conflicts,
            'new_conflicts': new_conflicts,
            'total_checks': total_checks,
            'buffer_distance': buffer_distance
        }
        
        return render(request, 'projects/partials/conflict_detection_results.html', context)
    
    # GET request - show form
    return render(request, 'projects/partials/conflict_detection_form.html', {
        'project': project
    })

@login_required
def project_conflicts_3d_htmx(request, project_id):
    project = get_object_or_404(Project, pk=project_id)
    
    # Check if user has access to this project
    if not (request.user.is_staff or 
            project.created_by == request.user or 
            project.members.filter(id=request.user.id).exists()):
        return HttpResponse("Permission denied", status=403)
    
    # Get conflicts for this project
    conflicts = Conflict.objects.filter(project=project)
    
    # Apply filters if provided
    status_filter = request.GET.get('status')
    if status_filter:
        conflicts = conflicts.filter(status=status_filter)
    
    severity_filter = request.GET.get('severity')
    if severity_filter:
        conflicts = conflicts.filter(severity=severity_filter)
    
    # Prepare three-dimensional visualization data
    conflict_data = []
    
    for conflict in conflicts:
        # Skip if location is invalid
        if not conflict.location:
            continue
        
        # Get utilities involved
        utility1 = conflict.utility1
        utility2 = conflict.utility2
        
        # Skip if either utility is missing
        if not utility1 or not utility2:
            continue
        
        # Get depth information
        depth1 = utility1.depth or 0
        depth2 = utility2.depth or 0
        
        # Create conflict data
        conflict_data.append({
            'id': conflict.id,
            'description': conflict.description,
            'status': conflict.status,
            'severity': conflict.severity,
            'location': {
                'x': conflict.location.x,
                'y': conflict.location.y,
                'z': min(depth1, depth2) - 0.5  # Position slightly above the deeper utility
            },
            'utility1': {
                'id': utility1.id,
                'name': utility1.name,
                'type': utility1.utility_type,
                'depth': depth1,
                'diameter': utility1.diameter or 0.3  # Default diameter if not specified
            },
            'utility2': {
                'id': utility2.id,
                'name': utility2.name,
                'type': utility2.utility_type,
                'depth': depth2,
                'diameter': utility2.diameter or 0.3  # Default diameter if not specified
            }
        })
    
    context = {
        'project': project,
        'conflicts': conflicts,
        'conflict_data': json.dumps(conflict_data),
        'current_filters': {
            'status': status_filter,
            'severity': severity_filter
        }
    }
    
    return render(request, 'projects/partials/conflicts_3d.html', context)

@login_required
def gis_layers_list(request):
    # Get available GIS layers
    if request.user.is_staff:
        layers = GISLayer.objects.all()
    else:
        layers = GISLayer.objects.filter(
            Q(project__members=request.user) |
            Q(project__created_by=request.user) |
            Q(is_public=True)
        ).distinct()
    
    # Apply filters if provided
    layer_type = request.GET.get('layer_type')
    if layer_type:
        layers = layers.filter(layer_type=layer_type)
    
    project_id = request.GET.get('project')
    if project_id:
        layers = layers.filter(project_id=project_id)
    
    return render(request, 'gis/partials/layers_list.html', {
        'layers': layers
    })

@login_required
def toggle_gis_layer(request, layer_id):
    layer = get_object_or_404(GISLayer, pk=layer_id)
    
    # Check if user has access to this layer
    if not (request.user.is_staff or 
            (layer.project and layer.project.created_by == request.user) or 
            (layer.project and layer.project.members.filter(id=request.user.id).exists()) or
            layer.is_public):
        return HttpResponse("Permission denied", status=403)
    
    if request.method == 'POST':
        # Toggle layer visibility
        visible = request.POST.get('visible') == 'true'
        
        # This doesn't actually change the layer in the database
        # It's just for the UI state, but we'll return appropriate response
        
        return JsonResponse({
            'id': layer.id,
            'name': layer.name,
            'visible': visible
        })
    
    return HttpResponse("Method not allowed", status=405)