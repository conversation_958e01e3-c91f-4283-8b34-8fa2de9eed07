"""
Authentication and User Management Views for CLEAR Application.
Handles login, logout, MFA setup, and user authentication flows.
"""


from datetime import timedelta
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.views import LoginView as DjangoLoginView
from django.contrib.auth.views import LogoutView as DjangoLogoutView
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, FormView, TemplateView
from ..forms import MFABackupForm, MFADisableForm, MFASetupForm, MFAVerificationForm
from ..models import MFASession, User






def login_view(request):
    """Handle user login with full HTMX hypermedia systems compliance."""
    if request.user.is_authenticated:
        if request.htmx:
            response = HttpResponse('')
            response['HX-Redirect'] = '/dashboard/'
            return response
        return redirect('CLEAR:dashboard')

    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        password = request.POST.get('password', '').strip()
        remember_me = request.POST.get('remember_me')
        
        if username and password:
            # Authenticate user
            user = authenticate(request, username=username, password=password)
            
            if user is not None:
                # Check if user has MFA enabled
                if user.is_mfa_enabled:
                    # Store user ID in session for MFA verification
                    request.session['mfa_user_id'] = user.id
                    if remember_me:
                        request.session['remember_me'] = True
                    
                    if request.htmx:
                        response = HttpResponse('')
                        response['HX-Redirect'] = reverse_lazy('CLEAR:mfa_verify')
                        return response
                    else:
                        return redirect('CLEAR:mfa_verify')
                
                # Normal login flow for users without MFA
                login(request, user)
                
                # Handle Remember Me functionality
                if remember_me:
                    request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days
                else:
                    request.session.set_expiry(0)  # Browser close

                # Update user activity
                user.last_login_at = timezone.now()
                user.last_activity = timezone.now()
                user.save(update_fields=['last_login_at', 'last_activity'])

                success_message = f'Welcome back, {user.first_name or user.username}!'

                # Handle HTMX vs regular requests
                if request.htmx:
                    # For successful HTMX login, redirect to dashboard
                    response = HttpResponse('')
                    next_url = request.GET.get('next', '/dashboard/')
                    response['HX-Redirect'] = next_url
                    return response
                else:
                    # Traditional HTTP redirect
                    messages.success(request, success_message)
                    next_url = request.GET.get('next', '/dashboard/')
                    return redirect(next_url)
            else:
                # Authentication failed
                error_message = 'Invalid username/email or password. Please check your credentials and try again.'
                
                if request.htmx:
                    # Return HTML fragment for HTMX
                    return render(request, 'auth/partials/login_messages.html', {
                        'error_message': error_message
                    })
                else:
                    # Traditional form handling
                    messages.error(request, error_message)
        else:
            # Missing username or password
            error_message = 'Please enter both username/email and password.'
            
            if request.htmx:
                # Return HTML fragment for HTMX
                return render(request, 'auth/partials/login_messages.html', {
                    'error_message': error_message
                })
            else:
                # Traditional form handling
                messages.error(request, error_message)

    # GET request or form validation failed
    # Preserve form data for re-display
    form_data = {
        'username': request.POST.get('username', '') if request.method == 'POST' else '',
    }
    
    # For HTMX requests to just the form area, return partial
    if request.htmx and request.headers.get('HX-Target') == 'login-form':
        return render(request, 'auth/partials/login_form.html', {
            'form_data': form_data
        })
    
    # Return full page
    return render(request, 'auth/login.html', {
        'form_data': form_data
    })




@login_required
def password_reset_request(request):
    """Handle password reset requests"""
    if request.method == 'POST':
        # Handle password reset logic here
        email = request.POST.get('email')
        if email:
            # In a real app, you'd send a reset email here
            messages.success(request, 'Password reset email sent!')
            return redirect('CLEAR:login')
    
    return render(request, 'auth/reset-password.html')

def logout_view(request):
    """Handle user logout with HTMX support"""
    logout(request)
    
    if request.htmx:
        response = HttpResponse('')
        response['HX-Redirect'] = reverse_lazy('CLEAR:login')
        return response
    else:
        messages.info(request, 'You have been logged out.')
        return redirect('CLEAR:login')


class LoginView(DjangoLoginView):
    """Enhanced login view with Remember Me functionality and security features"""
    template_name = 'auth/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('dashboard')

    def form_valid(self, form):
        """Enhanced form validation with MFA and Remember Me functionality"""
        user = form.get_user()
        remember_me = self.request.POST.get('remember-me')

        # Check if user has MFA enabled
        if user.is_mfa_enabled:
            # Don't log the user in yet, redirect to MFA verification
            self.request.session['mfa_user_id'] = user.id
            if remember_me:
                self.request.session['remember_me'] = True
            return redirect('CLEAR:mfa_verify')

        # Normal login flow for users without MFA
        response = super().form_valid(form)

        # Handle Remember Me functionality
        if remember_me:
            # Set session to expire in 30 days instead of when browser closes
            self.request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days in seconds
        else:
            # Set session to expire when browser closes
            self.request.session.set_expiry(0)

        # Update user's last login activity
        user.last_login_at = timezone.now()
        user.last_activity = timezone.now()
        user.save(update_fields=['last_login_at', 'last_activity'])

        # Add success message
        messages.success(self.request, f'Welcome back, {user.first_name or user.username}!')

        return response

    def form_invalid(self, form):
        """Enhanced error handling for login failures"""
        # Add more specific error messages
        username = self.request.POST.get('username', '')

        # Check for common issues
        if User.objects.filter(email=username).exists() or User.objects.filter(username=username).exists():
            messages.error(self.request, 'Invalid password. Please check your password and try again.')
        else:
            messages.error(self.request, 'User not found. Please check your username or email.')

        return super().form_invalid(form)

    def get_context_data(self, **kwargs):
        """Add additional context for enhanced login features"""
        context = super().get_context_data(**kwargs)

        # Add any additional context for future enhancements
        context.update({
            'show_remember_me': True,
            'enable_password_strength': True,
            'allow_social_login': False,  # Will be enabled in Phase 3
        })

        return context


class LogoutView(DjangoLogoutView):
    """Custom logout view"""
    next_page = reverse_lazy('CLEAR:login')


class SignupView(CreateView):
    """User registration view"""
    template_name = 'CLEAR/auth/signup.html'
    success_url = reverse_lazy('CLEAR:dashboard')

    def form_valid(self, form):
        response = super().form_valid(form)
        login(self.request, self.object)
        messages.success(self.request, 'Welcome to CLEAR! Your account has been created.')
        return response


# ========================================
# MFA VIEWS
# ========================================

class MFASetupView(LoginRequiredMixin, FormView):
    """MFA setup view for enabling two-factor authentication"""
    template_name = 'auth/mfa_setup.html'
    form_class = MFASetupForm
    success_url = reverse_lazy('CLEAR:mfa_backup_tokens')

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_mfa_enabled:
            messages.info(request, 'MFA is already enabled for your account.')
            return redirect('CLEAR:profile_settings')
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        user = self.request.user
        secret = user.generate_totp_secret()
        user.save()

        # Store secret in session for QR code display
        self.request.session['mfa_setup_secret'] = secret

        messages.success(self.request, 'MFA secret generated. Scan the QR code with your authenticator app.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Generate QR code data if we have a secret
        if 'mfa_setup_secret' in self.request.session:
            user = self.request.user
            user.totp_secret = self.request.session['mfa_setup_secret']
            context['qr_code_uri'] = user.get_totp_uri()
            context['secret'] = self.request.session['mfa_setup_secret']

        return context


class MFAVerifySetupView(LoginRequiredMixin, FormView):
    """Verify MFA setup with TOTP token"""
    template_name = 'auth/mfa_verify_setup.html'
    form_class = MFAVerificationForm
    success_url = reverse_lazy('CLEAR:mfa_backup_tokens')

    def dispatch(self, request, *args, **kwargs):
        if 'mfa_setup_secret' not in request.session:
            messages.error(request, 'No MFA setup session found. Please start the setup process again.')
            return redirect('CLEAR:mfa_setup')
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        user = self.request.user
        user.totp_secret = self.request.session['mfa_setup_secret']
        kwargs['user'] = user
        return kwargs

    def form_valid(self, form):
        user = self.request.user
        user.totp_secret = self.request.session['mfa_setup_secret']
        user.is_mfa_enabled = True
        user.mfa_setup_completed = True
        user.save()

        # Clear setup session
        del self.request.session['mfa_setup_secret']

        messages.success(self.request, 'MFA setup completed successfully!')
        return super().form_valid(form)


class MFABackupTokensView(LoginRequiredMixin, TemplateView):
    """Display MFA backup tokens"""
    template_name = 'auth/mfa_backup_tokens.html'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_mfa_enabled:
            messages.error(request, 'MFA is not enabled for your account.')
            return redirect('CLEAR:mfa_setup')
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        user = request.user
        if not user.backup_tokens:
            user.generate_backup_tokens()
            user.save()
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['backup_tokens'] = self.request.user.backup_tokens
        return context


class MFALoginView(FormView):
    """MFA verification during login"""
    template_name = 'auth/mfa_verify.html'
    form_class = MFAVerificationForm

    def dispatch(self, request, *args, **kwargs):
        if 'mfa_user_id' not in request.session:
            return redirect('CLEAR:login')

        user_id = request.session['mfa_user_id']
        try:
            self.user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return redirect('CLEAR:login')

        if not self.user.is_mfa_enabled:
            return redirect('CLEAR:login')

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.user
        return kwargs

    def form_valid(self, form):
        # Create MFA session
        session_key = self.request.session.session_key
        mfa_session, created = MFASession.objects.get_or_create(
            user=self.user,
            session_key=session_key,
            defaults={
                'expires_at': timezone.now() + timedelta(hours=8),
                'verification_method': 'totp'
            }
        )

        mfa_session.is_verified = True
        mfa_session.verified_at = timezone.now()
        mfa_session.save()

        # Complete login
        login(self.request, self.user)

        # Handle Remember Me functionality if set during initial login
        if self.request.session.get('remember_me'):
            self.request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days
            del self.request.session['remember_me']
        else:
            self.request.session.set_expiry(0)  # Browser close

        # Update user activity
        self.user.last_login_at = timezone.now()
        self.user.last_activity = timezone.now()
        self.user.save(update_fields=['last_login_at', 'last_activity'])

        # Clear MFA session data
        del self.request.session['mfa_user_id']

        messages.success(self.request, f'Welcome back, {self.user.first_name or self.user.username}!')
        return redirect('CLEAR:dashboard')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.user
        context['backup_login_url'] = reverse_lazy('CLEAR:mfa_backup_login')
        return context


class MFABackupLoginView(FormView):
    """MFA backup token verification during login"""
    template_name = 'auth/mfa_backup_verify.html'
    form_class = MFABackupForm

    def dispatch(self, request, *args, **kwargs):
        if 'mfa_user_id' not in request.session:
            return redirect('CLEAR:login')

        user_id = request.session['mfa_user_id']
        try:
            self.user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return redirect('CLEAR:login')

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.user
        return kwargs

    def form_valid(self, form):
        # Create MFA session
        session_key = self.request.session.session_key
        mfa_session, created = MFASession.objects.get_or_create(
            user=self.user,
            session_key=session_key,
            defaults={
                'expires_at': timezone.now() + timedelta(hours=8),
                'verification_method': 'backup'
            }
        )

        mfa_session.is_verified = True
        mfa_session.verified_at = timezone.now()
        mfa_session.verification_method = 'backup'
        mfa_session.save()

        # Complete login
        login(self.request, self.user)

        # Handle Remember Me functionality if set during initial login
        if self.request.session.get('remember_me'):
            self.request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days
            del self.request.session['remember_me']
        else:
            self.request.session.set_expiry(0)  # Browser close

        # Update user activity
        self.user.last_login_at = timezone.now()
        self.user.last_activity = timezone.now()
        self.user.save(update_fields=['last_login_at', 'last_activity'])

        # Clear MFA session data
        del self.request.session['mfa_user_id']

        messages.success(self.request, f'Welcome back, {self.user.first_name or self.user.username}!')
        messages.warning(self.request, 'You used a backup code. Consider regenerating backup codes.')
        return redirect('CLEAR:dashboard')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.user
        return context


class MFADisableView(LoginRequiredMixin, FormView):
    """Disable MFA for user account"""
    template_name = 'auth/mfa_disable.html'
    form_class = MFADisableForm
    success_url = reverse_lazy('CLEAR:profile_settings')

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_mfa_enabled:
            messages.info(request, 'MFA is not enabled for your account.')
            return redirect('CLEAR:profile_settings')
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        user = self.request.user
        user.is_mfa_enabled = False
        user.totp_secret = None
        user.backup_tokens = []
        user.mfa_setup_completed = False
        user.save()

        # Delete all MFA sessions
        MFASession.objects.filter(user=user).delete()

        messages.success(self.request, 'MFA has been disabled for your account.')
        return super().form_valid(form)



@require_http_methods(["GET", "POST"])
def register_view(request):
    """User registration view (disabled for now)."""
    return HttpResponse("Registration is disabled. Contact admin for access.", status=403)


@require_http_methods(["POST"])
def check_username(request):
    """Check if username is available."""
    username = request.POST.get('username', '')
    if username:
        exists = User.objects.filter(username=username).exists()
        return JsonResponse({'available': not exists})
    return JsonResponse({'available': False})
