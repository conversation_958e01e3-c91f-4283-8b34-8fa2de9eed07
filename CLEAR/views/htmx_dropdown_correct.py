import logging

from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.http import require_http_methods

# Correct HTMX Dropdown Implementation Following htmx.org Patterns



logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def dropdown_myhub(request):
    """
    HTMX dropdown endpoint following htmx.org patterns
    
    State is in the HTML structure, not in sessions.
    This follows the HATEOAS principle where "Hypertext As The Engine Of Application State"
    """
    # Simple logic: if dropdown was previously closed, show it; if open, close it
    # We can determine state from the HTTP Referer or a simple URL parameter
    action = request.GET.get('action', 'toggle')
    
    if action == 'close':
        # Return empty content to close dropdown
        return HttpResponse('')
    
    # Return the dropdown content with HTMX-enhanced close links
    return render(request, 'components/dropdown_content/myhub.html', {
        'user': request.user
    })


@login_required 
@require_http_methods(["GET"])
def dropdown_project(request):
    """Project dropdown following same pattern"""
    action = request.GET.get('action', 'toggle')
    
    if action == 'close':
        return HttpResponse('')
    
    return render(request, 'components/dropdown_content/project.html')


@login_required
@require_http_methods(["GET"]) 
def dropdown_help(request):
    """Help dropdown following same pattern"""
    action = request.GET.get('action', 'toggle')
    
    if action == 'close':
        return HttpResponse('')
    
    return render(request, 'components/dropdown_content/help.html')


@login_required
@require_http_methods(["GET"])
def dropdown_user(request):
    """User dropdown following same pattern"""
    action = request.GET.get('action', 'toggle')
    
    if action == 'close':
        return HttpResponse('')
    
    return render(request, 'components/dropdown_content/user.html', {
        'user': request.user
    })


@login_required
@require_http_methods(["GET"])
def close_all_dropdowns(request):
    """
    Utility endpoint to close all dropdowns
    
    This can be triggered by clicking outside or on menu items
    Returns empty content for all dropdown targets
    """
    return HttpResponse('')
