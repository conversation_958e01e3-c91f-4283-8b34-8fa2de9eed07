"""
REST API views for the CLEAR application.

Provides API endpoints for external integrations and AJAX functionality.
"""

from django.contrib.auth import authenticate, login, logout
from django.db.models import Q
from django.shortcuts import get_object_or_404
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from django.db.models import Manager

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import (
    ChatMessage,
    Comment,
    Conflict,
    CoordinateSystem,
    Document,
    FeatureRequest,
    GISLayer,
    Invoice,
    KnowledgeArticle,
    Note,
    Notification,
    Organization,
    Project,
    Report,
    Stakeholder,
    Task,
    TimeEntry,
    User,
    Utility,
    WhisperMessage,
    Workflow,
)


class LoginAPIView(APIView):
    """API endpoint for user authentication"""
    authentication_classes = []
    permission_classes = []
    
    def post(self, request):
        username = request.data.get('username')
        password = request.data.get('password')
        
        if username and password:
            user = authenticate(request, username=username, password=password)
            if user:
                login(request, user)
                return Response({
                    'success': True,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'role': user.role,
                    }
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)
        
        return Response({
            'success': False,
            'error': 'Username and password required'
        }, status=status.HTTP_400_BAD_REQUEST)


class LogoutAPIView(APIView):
    """API endpoint for user logout"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        logout(request)
        return Response({'success': True})


class CurrentUserAPIView(APIView):
    """Get current authenticated user information"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        user = request.user
        return Response({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role,
            'is_admin': user.is_admin,
            'unit_preference': user.unit_preference,
        })


class ProjectViewSet(viewsets.ModelViewSet):
    """CRUD operations for projects"""
    queryset = Project.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Project.objects.all()
        
        # Filter by search term
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                name__icontains=search
            ) | queryset.filter(
                client__icontains=search
            )
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(rag_status=status_filter)
            
        return queryset.order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """List projects with pagination"""
        queryset = self.get_queryset()
        
        # Simple pagination
        page_size = int(request.query_params.get('page_size', 25))
        page = int(request.query_params.get('page', 1))
        
        start = (page - 1) * page_size
        end = start + page_size
        
        projects = queryset[start:end]
        total = queryset.count()
        
        return Response({
            'projects': [
                {
                    'id': p.id,
                    'name': p.name,
                    'client': p.client,
                    'description': p.description,
                    'rag_status': p.rag_status,
                    'created_at': p.created_at,
                    'utility_count': p.utilities.count(),
                    'conflict_count': p.conflicts.count(),
                }
                for p in projects
            ],
            'total': total,
            'page': page,
            'page_size': page_size,
        })


class UtilityViewSet(viewsets.ModelViewSet):
    """CRUD operations for utilities"""
    queryset = Utility.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Utility.objects.select_related('project')
        
        # Filter by project
        project_id = self.request.query_params.get('project_id')
        if project_id:
            queryset = queryset.filter(project_id=project_id)
            
        return queryset.order_by('type', 'name')


class ConflictViewSet(viewsets.ModelViewSet):
    """CRUD operations for conflicts"""
    queryset = Conflict.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Conflict.objects.select_related('project', 'utility', 'utility2')
        
        # Filter by project
        project_id = self.request.query_params.get('project_id')
        if project_id:
            queryset = queryset.filter(project_id=project_id)
            
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.order_by('-created_at')


class TaskViewSet(viewsets.ModelViewSet):
    """CRUD operations for tasks"""
    queryset = Task.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Task.objects.select_related('project', 'assigned_to')
        
        # Filter by project
        project_id = self.request.query_params.get('project_id')
        if project_id:
            queryset = queryset.filter(project_id=project_id)
            
        # Filter by assigned user
        if self.request.query_params.get('my_tasks') == 'true':
            queryset = queryset.filter(assigned_to=self.request.user)
            
        return queryset.order_by('due_date')


class CommentViewSet(viewsets.ModelViewSet):
    """CRUD operations for comments"""
    queryset = Comment.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Comment.objects.select_related('user')
        
        # Filter by commentable object
        commentable_type = self.request.query_params.get('commentable_type')
        commentable_id = self.request.query_params.get('commentable_id')
        
        if commentable_type and commentable_id:
            queryset = queryset.filter(
                commentable_type=commentable_type,
                commentable_id=commentable_id
            )
            
        return queryset.order_by('-created_at')


class NotificationViewSet(viewsets.ModelViewSet):
    """CRUD operations for notifications"""
    queryset = Notification.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        # Users can only see their own notifications
        return self.request.user.notifications.order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark a notification as read"""
        notification = self.get_object()
        notification.read = True
        notification.save()
        return Response({'success': True})
    
    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read"""
        count = request.user.notifications.filter(read=False).update(read=True)
        return Response({'success': True, 'marked_read': count})


class CoordinateSystemViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only access to coordinate systems"""
    queryset = CoordinateSystem.objects.all()
    permission_classes = [IsAuthenticated]


# Spatial Analysis Endpoints

class ConflictDetectionAPIView(APIView):
    """Automated conflict detection for a project"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        project_id = request.data.get('project_id')
        if not project_id:
            return Response({
                'error': 'project_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        get_object_or_404(Project, pk=project_id)
        
        # TODO: Implement actual conflict detection algorithm
        # This would use PostGIS spatial queries to detect overlapping utilities
        
        return Response({
            'success': True,
            'message': 'Conflict detection completed',
            'conflicts_detected': 0,  # Placeholder
        })


class CoordinateTransformAPIView(APIView):
    """Transform coordinates between different systems"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # TODO: Implement coordinate transformation using PostGIS
        return Response({
            'success': True,
            'transformed_coordinates': [],
        })


class BufferAnalysisAPIView(APIView):
    """Perform buffer analysis for spatial queries"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # TODO: Implement buffer analysis using PostGIS
        return Response({
            'success': True,
            'buffer_results': [],
        })


# File Upload Endpoints

class CADFileUploadAPIView(APIView):
    """Handle CAD file uploads and processing"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # TODO: Implement CAD file processing
        return Response({
            'success': True,
            'message': 'CAD file uploaded successfully',
        })


class DocumentUploadAPIView(APIView):
    """Handle document uploads"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # TODO: Implement document upload handling
        return Response({
            'success': True,
            'message': 'Document uploaded successfully',
        })


# Dashboard Data Endpoints

class DashboardStatsAPIView(APIView):
    """Get dashboard statistics"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        user = request.user
        
        # Get user's projects
        projects = Project.objects.filter(
            egis_project_manager=user.username
        )
        
        stats = {
            'total_projects': projects.count(),
            'active_projects': projects.filter(rag_status='Green').count(),
            'warning_projects': projects.filter(rag_status='Yellow').count(),
            'critical_projects': projects.filter(rag_status='Red').count(),
            'total_utilities': Utility.objects.filter(project__in=projects).count(),
            'open_conflicts': Conflict.objects.filter(
                project__in=projects, 
                status='open'
            ).count(),
            'pending_tasks': Task.objects.filter(
                project__in=projects,
                completed=False
            ).count(),
        }
        
        return Response(stats)


class RecentActivityAPIView(APIView):
    """Get recent activity for dashboard"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # TODO: Implement recent activity tracking
        return Response({
            'activities': []
        })


# Real-time Update Endpoints

class ProjectUpdatesAPIView(APIView):
    """Get real-time project updates"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_id):
        # TODO: Implement real-time updates using WebSockets
        return Response({
            'updates': []
        })


class ChatMessagesAPIView(APIView):
    """Handle chat messages"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # TODO: Implement chat message retrieval
        return Response({
            'messages': []
        })
    
    def post(self, request):
        # TODO: Implement chat message sending
        return Response({
            'success': True
        })


# Export Endpoints

class ProjectPDFExportAPIView(APIView):
    """Export project data to PDF"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_id):
        # TODO: Implement PDF export
        return Response({
            'success': True,
            'download_url': f'/exports/project_{project_id}.pdf'
        })


class ProjectExcelExportAPIView(APIView):
    """Export project data to Excel"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_id):
        # TODO: Implement Excel export
        return Response({
            'success': True,
            'download_url': f'/exports/project_{project_id}.xlsx'
        })


class ConflictCSVExportAPIView(APIView):
    """Export conflict data to CSV"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_id):
        # TODO: Implement CSV export for conflicts
        return Response({
            'success': True,
            'download_url': f'/exports/conflicts_{project_id}.csv'
        })


# ========== EXTENDED API VIEWS ==========

class DocumentViewSet(viewsets.ModelViewSet):
    """Document management API"""
    queryset = Document.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = Document.objects.filter(
            Q(uploaded_by=self.request.user) |
            Q(is_public=True) |
            Q(shares__shared_with=self.request.user)
        ).distinct()
        
        project_id = self.request.query_params.get('project_id')
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        
        return queryset.order_by('-created_at')


class ReportViewSet(viewsets.ModelViewSet):
    """Report management API"""
    queryset = Report.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Report.objects.filter(
            Q(created_by=self.request.user) |
            Q(is_public=True)
        ).order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a report"""
        self.get_object()
        # TODO: Implement report execution
        return Response({'success': True, 'execution_id': 'placeholder'})


class TimeEntryViewSet(viewsets.ModelViewSet):
    """Time tracking API"""
    queryset = TimeEntry.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return TimeEntry.objects.filter(
            user=self.request.user
        ).order_by('-start_time')


class FeatureRequestViewSet(viewsets.ModelViewSet):
    """Feature request API"""
    queryset = FeatureRequest.objects.all()
    permission_classes = [IsAuthenticated]
    
    @action(detail=True, methods=['post'])
    def vote(self, request, pk=None):
        """Vote for a feature request"""
        self.get_object()
        # TODO: Implement voting logic
        return Response({'success': True})


class KnowledgeArticleViewSet(viewsets.ModelViewSet):
    """Knowledge base API"""
    queryset = KnowledgeArticle.objects.filter(is_published=True)
    permission_classes = [IsAuthenticated]
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment view count
        instance.views_count += 1
        instance.save(update_fields=['views_count'])
        return super().retrieve(request, *args, **kwargs)


class NoteViewSet(viewsets.ModelViewSet):
    """Notes API"""
    queryset = Note.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Note.objects.filter(
            Q(author=self.request.user) |
            Q(shared_with=self.request.user)
        ).order_by('-updated_at')


class WorkflowViewSet(viewsets.ModelViewSet):
    """Workflow management API"""
    queryset = Workflow.objects.all()
    permission_classes = [IsAuthenticated]
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a workflow"""
        self.get_object()
        # TODO: Implement workflow execution
        return Response({'success': True, 'execution_id': 'placeholder'})


class StakeholderViewSet(viewsets.ModelViewSet):
    """Stakeholder management API"""
    queryset = Stakeholder.objects.all()
    permission_classes = [IsAuthenticated]


class InvoiceViewSet(viewsets.ModelViewSet):
    """Invoice management API"""
    queryset = Invoice.objects.all()
    permission_classes = [IsAuthenticated]


class GISLayerViewSet(viewsets.ModelViewSet):
    """GIS layer management API"""
    queryset = GISLayer.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return GISLayer.objects.filter(
            Q(is_public=True) |
            Q(created_by=self.request.user)
        ).order_by('z_index')


class OrganizationViewSet(viewsets.ModelViewSet):
    """Organization management API"""
    queryset = Organization.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        # Users can only see their own organizations
        return Organization.objects.filter(
            members__user=self.request.user
        )


# ========== MESSAGING AND COMMUNICATION ==========

class ChatMessageAPIView(APIView):
    """Chat message API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get chat messages"""
        channel = request.query_params.get('channel', 'general')
        project_id = request.query_params.get('project_id')
        
        queryset = ChatMessage.objects.filter(channel=channel)
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        
        messages = queryset.select_related('user').order_by('-created_at')[:50]
        
        return Response({
            'messages': [
                {
                    'id': msg.id,
                    'content': msg.content,
                    'user': {
                        'id': msg.user.id,
                        'username': msg.user.username,
                        'first_name': msg.user.first_name,
                        'last_name': msg.user.last_name,
                    },
                    'timestamp': msg.created_at,
                }
                for msg in reversed(messages)
            ]
        })
    
    def post(self, request):
        """Send a chat message"""
        content = request.data.get('content')
        channel = request.data.get('channel', 'general')
        project_id = request.data.get('project_id')
        
        if not content:
            return Response(
                {'error': 'Content is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        message = ChatMessage.objects.create(
            user=request.user,
            content=content,
            channel=channel,
            project_id=project_id if project_id else None
        )
        
        return Response({
            'success': True,
            'message': {
                'id': message.id,
                'content': message.content,
                'user': {
                    'id': message.user.id,
                    'username': message.user.username,
                    'first_name': message.user.first_name,
                    'last_name': message.user.last_name,
                },
                'timestamp': message.created_at,
            }
        })


class WhisperMessageAPIView(APIView):
    """Whisper message API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get whisper messages for user"""
        messages = WhisperMessage.objects.filter(
            recipient=request.user
        ).select_related('sender').order_by('-created_at')
        
        return Response({
            'messages': [
                {
                    'id': msg.id,
                    'message': msg.message,
                    'sender': {
                        'id': msg.sender.id,
                        'username': msg.sender.username,
                        'first_name': msg.sender.first_name,
                        'last_name': msg.sender.last_name,
                    },
                    'created_at': msg.created_at,
                    'is_read': msg.is_read,
                    'read_at': msg.read_at,
                }
                for msg in messages
            ]
        })
    
    def post(self, request):
        """Send a whisper message"""
        message_text = request.data.get('message')
        recipient_id = request.data.get('recipient_id') or request.data.get('to_user_id')  # Support both field names for compatibility
        
        if not message_text or not recipient_id:
            return Response(
                {'error': 'Message and recipient_id are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        whisper = WhisperMessage.objects.create(
            sender=request.user,
            recipient=recipient,
            message=message_text
        )
        
        return Response({
            'success': True,
            'whisper_id': whisper.id
        })


# ========== ANALYTICS AND METRICS ==========

class ProjectAnalyticsAPIView(APIView):
    """Project analytics and metrics"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_id):
        project = get_object_or_404(Project, pk=project_id)
        
        # Calculate project metrics
        total_utilities = project.utilities.count()
        total_conflicts = project.conflicts.count()
        resolved_conflicts = project.conflicts.filter(status='resolved').count()
        open_conflicts = project.conflicts.filter(status='open').count()
        
        total_tasks = project.tasks.count()
        completed_tasks = project.tasks.filter(completed=True).count()
        pending_tasks = total_tasks - completed_tasks
        
        return Response({
            'project_id': project.id,
            'project_name': project.name,
            'utilities': {
                'total': total_utilities,
                'by_type': {}
            },
            'conflicts': {
                'total': total_conflicts,
                'resolved': resolved_conflicts,
                'open': open_conflicts,
                'resolution_rate': (resolved_conflicts / total_conflicts * 100) if total_conflicts > 0 else 0
            },
            'tasks': {
                'total': total_tasks,
                'completed': completed_tasks,
                'pending': pending_tasks,
                'completion_rate': (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            }
        })


class SystemMetricsAPIView(APIView):
    """System-wide metrics and analytics"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # Only allow admin users
        if not (request.user.is_admin or request.user.is_superuser):
            return Response(
                {'error': 'Insufficient permissions'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        metrics = {
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(is_active=True).count(),
                'admin': User.objects.filter(is_admin=True).count(),
            },
            'projects': {
                'total': Project.objects.count(),
                'active': Project.objects.exclude(rag_status='Completed').count(),
            },
            'utilities': {
                'total': Utility.objects.count(),
            },
            'conflicts': {
                'total': Conflict.objects.count(),
                'open': Conflict.objects.filter(status='open').count(),
            }
        }
        
        return Response(metrics)


# ========== IMPORT/EXPORT ENDPOINTS ==========

class DataImportAPIView(APIView):
    """Data import endpoint"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # TODO: Implement data import functionality
        return Response({
            'success': True,
            'message': 'Data import functionality not yet implemented'
        })


class DataExportAPIView(APIView):
    """Data export endpoint"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        export_type = request.query_params.get('type', 'json')
        entity = request.query_params.get('entity', 'projects')
        
        # TODO: Implement data export functionality
        return Response({
            'success': True,
            'download_url': f'/exports/{entity}.{export_type}',
            'message': 'Export functionality not yet implemented'
        })