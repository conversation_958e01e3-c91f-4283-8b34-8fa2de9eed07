"""
Project Management Models
Handles projects, templates, phases, teams, and workflows
"""

import uuid
from django.contrib.gis.db import models


# from django.contrib.postgres.fields import models.JSONField  # Commented out for SQLite compatibility




class ProjectTemplate(models.Model):
    """Project templates for standardized workflows"""
    id = models.Char<PERSON>ield(max_length=255, primary_key=True)  # Changed to match Supabase text ID
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, null=True, blank=True, related_name='project_templates')
    name = models.Char<PERSON>ield(max_length=255)
    description = models.TextField(blank=True, null=True)
    icon = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    color = models.Char<PERSON><PERSON>(max_length=7, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_templates')
    workflow_phases = models.J<PERSON><PERSON><PERSON>(default=list)
    settings = models.J<PERSON><PERSON><PERSON>(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    monday_id = models.CharField(max_length=50, blank=True, null=True)

    def __str__(self):
        return self.name


class Project(models.Model):
    """Main project entity"""
    id = models.CharField(max_length=255, primary_key=True)
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, null=True, blank=True, related_name='projects')
    monday_id = models.CharField(max_length=50, blank=True, null=True)
    name = models.CharField(max_length=255)
    client = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    manager_id = models.IntegerField(blank=True, null=True)
    high_priority_items = models.IntegerField(blank=True, null=True)
    medium_priority_items = models.IntegerField(blank=True, null=True)
    low_priority_items = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    record_id = models.CharField(max_length=255, blank=True, null=True)
    client_job_number = models.CharField(max_length=255, blank=True, null=True)
    work_type = models.CharField(max_length=100, blank=True, null=True)
    rag_status = models.CharField(max_length=50, blank=True, null=True)
    project_id_only = models.CharField(max_length=100, blank=True, null=True)
    phase_id_only = models.CharField(max_length=100, blank=True, null=True)
    last_milestone = models.CharField(max_length=255, blank=True, null=True)
    coordination_type = models.CharField(max_length=100, blank=True, null=True)
    project_funding = models.CharField(max_length=100, blank=True, null=True)
    ntp_date = models.DateField(blank=True, null=True)
    letting_bid_date = models.DateField(blank=True, null=True)
    this_month_status = models.CharField(max_length=255, blank=True, null=True)
    status_update_date = models.DateField(blank=True, null=True)
    client_pm = models.CharField(max_length=255, blank=True, null=True)
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    contract_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_to_date = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    project_hours_for_billed = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    wip = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_plus_wip = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    current_cost = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    profit_to_date = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    profit_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    project_priority = models.CharField(max_length=50, blank=True, null=True)
    egis_project_manager = models.CharField(max_length=255, blank=True, null=True)
    egis_project_manager_email = models.EmailField(blank=True, null=True)
    client_contact = models.CharField(max_length=255, blank=True, null=True)
    project_health_rag = models.CharField(max_length=50, default="Project Health")
    current_phase = models.CharField(max_length=100, blank=True, null=True)
    coordinator_id = models.CharField(max_length=100, blank=True, null=True)
    template = models.ForeignKey('ProjectTemplate', on_delete=models.SET_NULL, blank=True, null=True, related_name='projects')

    def __str__(self):
        return f"{self.name} ({self.client})"
    
    def get_comment_count(self):
        """Get total number of comments for this project"""
        from django.apps import apps
        Comment = apps.get_model('CLEAR', 'Comment')
        return Comment.objects.filter(
            commentable_type='project',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).count()
    
    def get_recent_comments(self, limit=5):
        """Get recent comments for this project"""
        from django.apps import apps
        Comment = apps.get_model('CLEAR', 'Comment')
        return Comment.objects.filter(
            commentable_type='project',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).select_related('user').order_by('-created_at')[:limit]


class Stakeholder(models.Model):
    """Project stakeholders and contacts"""
    full_name = models.CharField(max_length=255)
    contact_company = models.CharField(max_length=255)
    company_abbreviation = models.CharField(max_length=20, blank=True, null=True)
    type_delivery = models.CharField(max_length=100, blank=True, null=True)
    stakeholder_type = models.CharField(max_length=100, blank=True, null=True)
    business_phone = models.CharField(max_length=20, blank=True, null=True)
    mobile_phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='stakeholders')
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    zip_code = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.full_name} ({self.contact_company})"


class Organization(models.Model):
    """Organization/company model"""
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    logo = models.URLField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    zip_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, default="United States")
    timezone = models.CharField(max_length=50, default="America/New_York")
    settings = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name


class OrganizationMember(models.Model):
    """Organization membership"""
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='org_memberships')
    role = models.CharField(max_length=50, default="member", choices=[
        ('owner', 'Owner'),
        ('admin', 'Admin'),
        ('manager', 'Manager'),
        ('member', 'Member'),
    ])
    permissions = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['organization', 'user']
    
    def __str__(self):
        return f"{self.user.get_full_name()} in {self.organization.name}"


class Task(models.Model):
    """Project tasks and task management"""
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=50, default="pending", choices=[
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ])
    priority = models.CharField(max_length=20, default="medium", choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ])
    assigned_to = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='assigned_tasks')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_tasks')
    due_date = models.DateTimeField(blank=True, null=True)
    start_date = models.DateTimeField(blank=True, null=True)
    completion_date = models.DateTimeField(blank=True, null=True)
    estimated_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    actual_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    tags = models.JSONField(default=list)
    attachments = models.JSONField(default=list)
    dependencies = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='dependents')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['project', 'status']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['due_date']),
        ]
        ordering = ['due_date', 'priority']
    
    def __str__(self):
        return f"{self.title} ({self.project.name})"
    
    def get_comment_count(self):
        """Get total number of comments for this task"""
        from django.apps import apps
        Comment = apps.get_model('CLEAR', 'Comment')
        return Comment.objects.filter(
            commentable_type='task',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).count()
    
    def get_recent_comments(self, limit=5):
        """Get recent comments for this task"""
        from django.apps import apps
        Comment = apps.get_model('CLEAR', 'Comment')
        return Comment.objects.filter(
            commentable_type='task',
            commentable_id=str(self.id),
            deleted_at__isnull=True
        ).select_related('user').order_by('-created_at')[:limit]


class ProjectMember(models.Model):
    """Project team member assignments"""
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='team_members')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='project_memberships')
    role = models.CharField(max_length=50, default="contributor", choices=[
        ('manager', 'Project Manager'),
        ('coordinator', 'Coordinator'),
        ('contributor', 'Contributor'),
        ('viewer', 'Viewer'),
    ])
    permissions = models.JSONField(default=list)
    added_by = models.ForeignKey('User', on_delete=models.SET_NULL, blank=True, null=True, related_name='added_project_members')
    added_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['project', 'user']
        indexes = [
            models.Index(fields=['project', 'is_active']),
            models.Index(fields=['user', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.user.get_full_name()} on {self.project.name} ({self.role})"


class ProjectActivity(models.Model):
    """Project activity tracking for timelines"""
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='project_activities')
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='project_activities')
    action_type = models.CharField(max_length=100, choices=[
        ('comment_added', 'Comment Added'),
        ('task_created', 'Task Created'),
        ('task_updated', 'Task Updated'),
        ('task_completed', 'Task Completed'),
        ('document_uploaded', 'Document Uploaded'),
        ('utility_updated', 'Utility Updated'),
        ('conflict_reported', 'Conflict Reported'),
        ('conflict_resolved', 'Conflict Resolved'),
        ('member_added', 'Team Member Added'),
        ('member_removed', 'Team Member Removed'),
        ('project_updated', 'Project Updated'),
    ])
    description = models.TextField()
    target_type = models.CharField(max_length=50, blank=True, null=True)  # task, utility, conflict, etc.
    target_id = models.CharField(max_length=255, blank=True, null=True)
    metadata = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['project', '-timestamp']),
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['action_type', '-timestamp']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.project.name}: {self.description} by {self.user.get_full_name()}"


class ProjectPhase(models.Model):
    """Project phase management"""
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='phases')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    order = models.IntegerField(default=0)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=50, default="pending", choices=[
        ('pending', 'Pending'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ])
    deliverables = models.JSONField(default=list)
    milestones = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['project', 'order']
        unique_together = ['project', 'name']
    
    def __str__(self):
        return f"{self.project.name} - {self.name}"


class Workflow(models.Model):
    """Workflow definitions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='workflows')
    created_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='created_workflows')
    workflow_definition = models.JSONField(default=dict)  # Step definitions, conditions, etc.
    is_active = models.BooleanField(default=True)
    version = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} v{self.version}"


class WorkflowExecution(models.Model):
    """Track workflow execution instances"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workflow = models.ForeignKey('Workflow', on_delete=models.CASCADE, related_name='executions')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='workflow_executions')
    started_by = models.ForeignKey('User', on_delete=models.CASCADE, related_name='started_workflows')
    current_step = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=50, default="running", choices=[
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ])
    execution_data = models.JSONField(default=dict)  # Step results, variables, etc.
    error_message = models.TextField(blank=True, null=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.workflow.name} - {self.project.name} ({self.status})"


class ProjectLog(models.Model):
    """Project event logging"""
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='logs')
    user = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=100)
    details = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['project', '-timestamp']),
            models.Index(fields=['action', '-timestamp']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.project.name}: {self.action}"
