"""
WebSocket routing for CLEAR application.

Defines WebSocket URL patterns for real-time features.
"""

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Project-specific WebSocket connections
    re_path(r'ws/project/(?P<project_id>\\w+)/$', consumers.ProjectConsumer.as_asgi()),
    
    # Chat and messaging
    re_path(r'ws/chat/(?P<channel_name>\\w+)/$', consumers.ChatConsumer.as_asgi()),
    
    # User notifications
    re_path(r'ws/notifications/$', consumers.NotificationConsumer.as_asgi()),
    
    # Real-time dashboard updates
    re_path(r'ws/dashboard/$', consumers.DashboardConsumer.as_asgi()),
    
    # Conflict detection updates
    re_path(r'ws/conflicts/(?P<project_id>\\w+)/$', consumers.ConflictConsumer.as_asgi()),
    
    # Task updates
    re_path(r'ws/tasks/(?P<project_id>\\w+)/$', consumers.TaskConsumer.as_asgi()),
    
    # Utility coordination updates
    re_path(r'ws/utilities/(?P<project_id>\\w+)/$', consumers.UtilityConsumer.as_asgi()),
    
    # GIS layer updates
    re_path(r'ws/gis/(?P<project_id>\\w+)/$', consumers.GISConsumer.as_asgi()),
    
    # Whisper messages (ephemeral)
    re_path(r'ws/whispers/$', consumers.WhisperConsumer.as_asgi()),
    
    # System-wide updates (admin only)
    re_path(r'ws/system/$', consumers.SystemConsumer.as_asgi()),
    
    # Document collaboration
    re_path(r'ws/documents/(?P<document_id>[0-9a-f-]+)/$', consumers.DocumentCollaborationConsumer.as_asgi()),
    
    # Spatial collaboration for mapping interface
    re_path(r'ws/spatial/(?P<project_id>\\w+)/$', consumers.SpatialCollaborationConsumer.as_asgi()),
    re_path(r'ws/spatial/(?P<project_id>\\w+)/(?P<session_id>\\w+)/$', consumers.SpatialCollaborationConsumer.as_asgi()),
]