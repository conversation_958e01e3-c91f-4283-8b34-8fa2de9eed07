"""
Django admin configuration for CLEAR models.

Provides administrative interface for managing the utility coordination platform.
"""

from typing import Any
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.gis.admin import GISModelAdmin
from django.db.models import QuerySet
from django.http import HttpRequest
from django.utils import timezone
from .models import (

    Activity,
    AnalyticsReport,
    # Knowledge Base models
    Article,
    ArticleAttachment,
    ArticleCategory,
    ArticleRevision,
    ArticleView,
    ArticleVote,
    BusinessMetric,
    ChatMessage,
    CollaborationSettings,
    Comment,
    Conflict,
    ContractAdministration,
    CoordinateSystem,
    Document,
    DocumentActivity,
    DocumentDiscussion,
    DocumentDiscussionMessage,
    DocumentDiscussionParticipant,
    DocumentReviewer,
    DocumentReviewProcess,
    DocumentShare,
    DocumentVersion,
    FeatureRequest,
    FeatureVote,
    GISLayer,
    Invoice,
    KnowledgeArticle,
    LineStyle,
    MessageMention,
    MessageReaction,
    MessageThread,
    Note,
    Notification,
    Organization,
    OrganizationMember,
    PasswordResetToken,
    Permission,
    Project,
    ProjectPhase,
    ProjectTemplate,
    Report,
    ReportExecution,
    RolePermission,
    Stakeholder,
    SystemMetric,
    Task,
    TimeEntry,
    User,
    Utility,
    UtilityLineData,
    WhisperMessage,
    Workflow,
    WorkflowExecution,
)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Enhanced user admin with CLEAR-specific fields"""
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'is_admin', 'created_at')
    list_filter = ('role', 'is_active', 'is_admin', 'created_by_saml', 'created_at')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('-created_at',)
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('CLEAR Profile', {
            'fields': ('role', 'avatar_url', 'unit_preference', 'is_admin', 'created_by_saml')
        }),
        ('Preferences', {
            'fields': ('custom_settings', 'dashboard_layout', 'ui_preferences'),
            'classes': ('collapse',)
        }),
        ('Security', {
            'fields': ('reset_password_on_login',),
        }),
    )
    
    add_fieldsets = getattr(BaseUserAdmin, "add_fieldsets", None) + (
        ('CLEAR Profile', {
            'fields': ('email', 'first_name', 'last_name', 'role')
        }),
    )


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    """Project management with comprehensive view"""
    list_display = ('name', 'client', 'egis_project_manager', 'rag_status', 'start_date', 'end_date', 'created_at')
    list_filter = ('rag_status', 'work_type', 'coordination_type', 'project_priority', 'created_at')
    search_fields = ('name', 'client', 'description', 'egis_project_manager')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)


@admin.register(Utility)
class UtilityAdmin(admin.ModelAdmin):
    """Utility company management"""
    list_display = ('name', 'type', 'project', 'status', 'contact_name', 'last_response')
    list_filter = ('type', 'status', 'created_at')
    search_fields = ('name', 'project__name', 'contact_name', 'contact_email')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)


@admin.register(Conflict)
class ConflictAdmin(admin.ModelAdmin):
    """Conflict management with priority handling"""
    list_display = ('project', 'description_short', 'status', 'priority', 'detection_method', 'created_at')
    list_filter = ('status', 'priority', 'detection_method', 'is_vertical_conflict', 'created_at')
    search_fields = ('description', 'project__name', 'utility__name')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def description_short(self, obj: Any) -> str:
        """Truncated description for list view"""
        return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
    description_short.short_description = 'Description'  # type: ignore  # type: ignore


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    """Task management with project association"""
    list_display = ('title', 'project', 'priority', 'status', 'assigned_to', 'due_date', 'created_at')
    list_filter = ('priority', 'status', 'created_at')
    search_fields = ('title', 'description', 'project__name', 'assigned_to__username')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Organization management"""
    list_display = ('name', 'email', 'city', 'state', 'is_active', 'created_at')
    list_filter = ('is_active', 'state', 'created_at')
    search_fields = ('name', 'email', 'city')
    ordering = ('-created_at',)


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    """Document management"""
    list_display = ('name', 'project', 'file_type', 'file_size_mb', 'uploaded_by', 'created_at')
    list_filter = ('file_type', 'is_public', 'created_at')
    search_fields = ('name', 'description', 'project__name', 'uploaded_by__username')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def file_size_mb(self, obj: Any) -> str:
        """Display file size in MB"""
        return f"{obj.file_size / 1024 / 1024:.2f} MB"
    file_size_mb.short_description = 'File Size'  # type: ignore


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    """Report management"""
    list_display = ('name', 'created_by', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'description', 'created_by__username')
    ordering = ('-created_at',)


@admin.register(TimeEntry)
class TimeEntryAdmin(admin.ModelAdmin):
    """Time tracking management"""
    list_display = ('user', 'project', 'start_time', 'duration_hours', 'billing_status', 'approval_status')
    list_filter = ('billing_status', 'approval_status', 'start_time')
    search_fields = ('user__username', 'project__name', 'description')
    date_hierarchy = 'start_time'
    ordering = ('-start_time',)
    
    def get_duration_display(self, obj: Any) -> str:
        """Display duration in hours"""
        if obj.duration_hours:
            return f"{obj.duration_hours:.2f} hrs"
        return "In progress"
    get_duration_display.short_description = 'Duration'  # type: ignore


@admin.register(FeatureRequest)
class FeatureRequestAdmin(admin.ModelAdmin):
    """Feature request management"""
    list_display = ('title', 'category', 'priority', 'status', 'vote_count', 'user', 'created_at')
    list_filter = ('category', 'priority', 'status', 'created_at')
    search_fields = ('title', 'description', 'user__username')
    ordering = ('-vote_count', '-created_at')


@admin.register(KnowledgeArticle)
class KnowledgeArticleAdmin(admin.ModelAdmin):
    """Knowledge base management"""
    list_display = ('title', 'category', 'is_published', 'view_count', 'author', 'created_at')
    list_filter = ('category', 'is_published', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    ordering = ('-view_count', '-created_at')
    prepopulated_fields = {'slug': ('title',)}


@admin.register(Note)
class NoteAdmin(admin.ModelAdmin):
    """Notes management"""
    list_display = ('user', 'content_preview', 'created_at', 'updated_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'user__username')
    ordering = ('-updated_at',)
    
    def content_preview(self, obj: Any) -> str:
        """Show content preview"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content Preview'  # type: ignore


@admin.register(Workflow)
class WorkflowAdmin(admin.ModelAdmin):
    """Workflow management"""
    list_display = ('name', 'version', 'is_active', 'created_by', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description', 'created_by__username')
    ordering = ('-created_at',)


@admin.register(GISLayer)
class GISLayerAdmin(admin.ModelAdmin):
    """GIS layer management"""
    list_display = ('name', 'layer_type', 'visibility', 'opacity', 'z_index', 'is_public', 'created_by')
    list_filter = ('layer_type', 'visibility', 'is_public', 'created_at')
    search_fields = ('name', 'description', 'created_by__username')
    ordering = ('z_index', 'name')


@admin.register(ContractAdministration)
class ContractAdministrationAdmin(admin.ModelAdmin):
    """Contract management"""
    list_display = ('contract_number', 'project', 'contractor_name', 'contract_amount', 'status', 'start_date', 'end_date')
    list_filter = ('status', 'start_date', 'end_date')
    search_fields = ('contract_number', 'contract_title', 'contractor_name', 'project__name')
    ordering = ('-start_date',)


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Invoice management"""
    list_display = ('invoice_number', 'project', 'invoice_date', 'total_amount', 'status', 'due_date')
    list_filter = ('status', 'invoice_date', 'due_date')
    search_fields = ('invoice_number', 'project__name')
    ordering = ('-invoice_date',)


@admin.register(SystemMetric)
class SystemMetricAdmin(admin.ModelAdmin):
    """System metrics monitoring"""
    list_display = ('metric_name', 'metric_value', 'metric_unit', 'category', 'timestamp')
    list_filter = ('metric_name', 'category', 'timestamp')
    search_fields = ('metric_name', 'category')
    date_hierarchy = 'timestamp'
    ordering = ('-timestamp',)


@admin.register(WhisperMessage)
class WhisperMessageAdmin(admin.ModelAdmin):
    """Whisper message management"""
    list_display = ('sender', 'recipient', 'message_preview', 'created_at', 'is_read', 'read_at')
    list_filter = ('created_at', 'is_read', 'read_at')
    search_fields = ('sender__username', 'recipient__username', 'message')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def message_preview(self, obj: Any) -> str:
        """Show message preview"""
        return obj.message[:50] + '...' if len(obj.message) > 50 else obj.message
    message_preview.short_description = 'Message'  # type: ignore


# Register remaining models with basic admin
admin.site.register(Permission)
admin.site.register(RolePermission)
admin.site.register(PasswordResetToken)
admin.site.register(CoordinateSystem)
admin.site.register(ProjectTemplate)
admin.site.register(Stakeholder)
admin.site.register(LineStyle)
admin.site.register(UtilityLineData, GISModelAdmin)
admin.site.register(Comment)
admin.site.register(ChatMessage)
admin.site.register(Activity)
admin.site.register(Notification)
admin.site.register(OrganizationMember)
admin.site.register(ProjectPhase)
admin.site.register(DocumentVersion)
admin.site.register(DocumentShare)
admin.site.register(FeatureVote)
admin.site.register(WorkflowExecution)


# ========== ANALYTICS ADMIN ==========

@admin.register(BusinessMetric)
class BusinessMetricAdmin(admin.ModelAdmin):
    """Business metrics administration"""
    list_display = ('category', 'value', 'unit', 'period_start', 'period_end', 'organization')
    list_filter = ('category', 'organization', 'period_start')
    search_fields = ('category', 'organization__name')
    date_hierarchy = 'period_start'
    readonly_fields = ('calculated_at',)
    ordering = ('-period_start', 'category')


@admin.register(AnalyticsReport)
class AnalyticsReportAdmin(admin.ModelAdmin):
    """Analytics reports administration"""
    list_display = ('name', 'report_type', 'created_by', 'organization', 'is_scheduled', 'last_executed_at')
    list_filter = ('report_type', 'is_scheduled', 'organization', 'created_at')
    search_fields = ('name', 'description', 'created_by__username')
    readonly_fields = ('created_at', 'last_executed_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'report_type', 'created_by', 'organization')
        }),
        ('Configuration', {
            'fields': ('config',),
            'classes': ('collapse',)
        }),
        ('Scheduling', {
            'fields': ('is_scheduled', 'schedule_config'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'last_executed_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReportExecution)
class ReportExecutionAdmin(admin.ModelAdmin):
    """Report execution tracking"""
    list_display = ('report', 'executed_by', 'status', 'started_at', 'completed_at', 'execution_time_ms')
    list_filter = ('status', 'started_at')
    search_fields = ('report__name', 'executed_by__username')
    readonly_fields = ('started_at', 'completed_at', 'execution_time_ms')
    ordering = ('-started_at',)
    
    fieldsets = (
        ('Execution Details', {
            'fields': ('report', 'executed_by', 'started_at', 'completed_at', 'status')
        }),
        ('Results', {
            'fields': ('result_file_path', 'result_data')
        }),
        ('Performance', {
            'fields': ('execution_time_ms', 'error_message'),
            'classes': ('collapse',)
        }),
    )


# ========== DOCUMENT COLLABORATION ADMIN ==========

@admin.register(DocumentActivity)
class DocumentActivityAdmin(admin.ModelAdmin):
    """Document activity tracking administration"""
    list_display = ('document', 'user', 'action', 'details_short', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('document__name', 'user__username')
    date_hierarchy = 'timestamp'
    readonly_fields = ('timestamp',)
    ordering = ('-timestamp',)
    
    def details_short(self, obj: Any) -> str:
        """Truncated details for list view"""
        details_str = str(obj.details)
        return details_str[:50] + '...' if details_str and len(details_str) > 50 else details_str
    details_short.short_description = 'Details'  # type: ignore


@admin.register(DocumentDiscussion)
class DocumentDiscussionAdmin(admin.ModelAdmin):
    """Document discussion administration"""
    list_display = ('title', 'document', 'created_by', 'is_resolved', 'message_count', 'created_at')
    list_filter = ('is_resolved', 'created_at')
    search_fields = ('title', 'description', 'document__name', 'created_by__username')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-updated_at',)
    
    def message_count(self, obj: Any) -> str:
        """Show number of messages in discussion"""
        return obj.messages.count()
    message_count.short_description = 'Messages'  # type: ignore


@admin.register(DocumentDiscussionMessage)
class DocumentDiscussionMessageAdmin(admin.ModelAdmin):
    """Document discussion message administration"""
    list_display = ('discussion', 'user', 'content_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('discussion__title', 'user__username', 'content')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    
    def content_preview(self, obj: Any) -> str:
        """Show content preview"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content'  # type: ignore


@admin.register(DocumentReviewProcess)
class DocumentReviewProcessAdmin(admin.ModelAdmin):
    """Document review process administration"""
    list_display = ('document', 'review_type', 'status', 'deadline', 'created_by')
    list_filter = ('review_type', 'status', 'created_at')
    search_fields = ('document__name', 'created_by__username')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)
    
    def approval_progress(self, obj: Any) -> str:
        """Show approval progress"""
        approved_count = obj.reviewers.filter(review_status='approved').count()
        total_reviewers = obj.reviewers.count()
        return f"{approved_count}/{total_reviewers}"
    approval_progress.short_description = 'Approvals'  # type: ignore


# Register additional models with basic admin
admin.site.register(DocumentDiscussionParticipant)
admin.site.register(DocumentReviewer)


# ========== COLLABORATION FEATURES ADMIN ==========
# Week 4 Task 4.2: Advanced Collaboration Features

@admin.register(MessageThread)
class MessageThreadAdmin(admin.ModelAdmin):
    """Admin for message threads"""
    list_display = ('title_display', 'root_message_preview', 'reply_count', 'last_reply_at')
    list_filter = ('last_reply_at',)
    search_fields = ('title', 'root_message__content', 'root_message__user__username')
    readonly_fields = ('reply_count', 'participants_display')
    raw_id_fields = ('root_message',)
    date_hierarchy = 'last_reply_at'
    ordering = ('-last_reply_at',)
    
    def title_display(self, obj: Any) -> str:
        """Display thread title or truncated root message"""
        return obj.title or f"Thread on: {obj.root_message.content[:50]}..."
    title_display.short_description = 'Thread Title'  # type: ignore
    
    def root_message_preview(self, obj: Any) -> str:
        """Show preview of root message"""
        return f"{obj.root_message.user.username}: {obj.root_message.content[:50]}..."
    root_message_preview.short_description = 'Root Message'  # type: ignore
    
    def reply_count(self, obj: Any) -> str:
        """Show number of replies"""
        return obj.get_reply_count()
    reply_count.short_description = 'Replies'  # type: ignore
    
    def participants_display(self, obj: Any) -> str:
        """Show thread participants"""
        participants = obj.get_participants()[:5]  # Show first 5
        names = [f"{p.first_name} {p.last_name}" for p in participants]
        result = ", ".join(names)
        if obj.get_participants().count() > 5:
            result += f" (+{obj.get_participants().count() - 5} more)"
        return result
    participants_display.short_description = 'Participants'  # type: ignore


@admin.register(MessageMention)
class MessageMentionAdmin(admin.ModelAdmin):
    """Admin for message mentions"""
    list_display = ('mention_text', 'mentioned_user', 'message_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('mention_text', 'mentioned_user__username', 'message__content')
    readonly_fields = ('created_at', 'start_position', 'end_position')
    raw_id_fields = ('message', 'mentioned_user')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def message_preview(self, obj: Any) -> str:
        """Show preview of message content"""
        return f"{obj.message.user.username}: {obj.message.content[:50]}..."
    message_preview.short_description = 'Message'  # type: ignore


@admin.register(MessageReaction)
class MessageReactionAdmin(admin.ModelAdmin):
    """Admin for message reactions"""
    list_display = ('emoji', 'user', 'message_preview', 'created_at')
    list_filter = ('emoji', 'created_at')
    search_fields = ('emoji', 'user__username', 'message__content')
    readonly_fields = ('created_at',)
    raw_id_fields = ('message', 'user')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def message_preview(self, obj: Any) -> str:
        """Show preview of message content"""
        return f"{obj.message.user.username}: {obj.message.content[:50]}..."
    message_preview.short_description = 'Message'  # type: ignore


@admin.register(CollaborationSettings)
class CollaborationSettingsAdmin(admin.ModelAdmin):
    """Admin for collaboration settings"""
    list_display = ('user', 'email_notifications', 'push_notifications', 'desktop_notifications', 'updated_at')
    list_filter = ('email_notifications', 'push_notifications', 'desktop_notifications', 'auto_join_project_rooms')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('user',)
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Notification Settings', {
            'fields': ('email_notifications', 'push_notifications', 'desktop_notifications'),
            'classes': ('collapse',)
        }),
        ('Collaboration Settings', {
            'fields': ('auto_join_project_rooms', 'show_typing_indicators', 'show_read_receipts', 'allow_direct_messages'),
            'classes': ('collapse',)
        }),
        ('Meeting Settings', {
            'fields': ('default_meeting_duration', 'auto_record_meetings', 'meeting_reminder_minutes'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


# ========== KNOWLEDGE BASE ADMIN ==========
# Phase 1: Core Article Management Administration

@admin.register(ArticleCategory)
class ArticleCategoryAdmin(admin.ModelAdmin):
    """Article category administration"""
    list_display = ('name', 'description', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'description')
    readonly_fields = ('created_at',)
    ordering = ('name',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    def article_count(self, obj: Any) -> str:
        """Show number of articles in category"""
        return obj.get_article_count()
    article_count.short_description = 'Articles'  # type: ignore
    
    def article_count_display(self, obj: Any) -> str:
        """Detailed article count for read-only field"""
        count = obj.get_article_count()
        published = obj.articles.filter(status='published').count()
        return f"{count} total ({published} published)"
    article_count_display.short_description = 'Article Statistics'  # type: ignore


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    """Legacy Article administration"""
    list_display = ('title', 'author', 'category', 'created_at', 'updated_at')
    list_filter = ('category', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('author',)
    date_hierarchy = 'created_at'
    ordering = ('-updated_at',)
    
    fieldsets = (
        ('Content', {
            'fields': ('title', 'content', 'author', 'category')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def primary_category(self, obj: Any) -> str:
        """Show primary category"""
        return obj.categories.first()
    primary_category.short_description = 'Primary Category'  # type: ignore
    
    def analytics_summary(self, obj: Any) -> str:
        """Comprehensive analytics summary"""
        helpful = obj.helpful_votes
        not_helpful = obj.not_helpful_votes
        total_votes = helpful + not_helpful
        helpfulness = round((helpful / total_votes) * 100) if total_votes > 0 else 0
        return f"Views: {obj.view_count} | Helpful: {helpful} | Not Helpful: {not_helpful} | Score: {helpfulness}%"
    analytics_summary.short_description = 'Analytics Summary'  # type: ignore
    
    actions = ['make_featured', 'remove_featured', 'publish_articles', 'archive_articles']
    
    def make_featured(self, request: HttpRequest, queryset: QuerySet[Any]) -> None:
        """Mark articles as featured"""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} articles marked as featured.")
    make_featured.short_description = "Mark selected articles as featured"  # type: ignore
    
    def remove_featured(self, request: HttpRequest, queryset: QuerySet[Any]) -> None:
        """Remove featured status"""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"{updated} articles removed from featured.")
    remove_featured.short_description = "Remove featured status"  # type: ignore
    
    def publish_articles(self, request: HttpRequest, queryset: QuerySet[Any]) -> None:
        """Publish selected articles"""
        updated = queryset.filter(status__in=['draft', 'review']).update(
            status='published',
            published_at=timezone.now()
        )
        self.message_user(request, f"{updated} articles published.")
    publish_articles.short_description = "Publish selected articles"  # type: ignore
    
    def archive_articles(self, request: HttpRequest, queryset: QuerySet[Any]) -> None:
        """Archive selected articles"""
        updated = queryset.update(status='archived')
        self.message_user(request, f"{updated} articles archived.")
    archive_articles.short_description = "Archive selected articles"  # type: ignore


@admin.register(ArticleRevision)
class ArticleRevisionAdmin(admin.ModelAdmin):
    """Article revision tracking administration"""
    list_display = ('article', 'title', 'changed_by', 'change_summary_short', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('article__title', 'title', 'change_summary', 'changed_by__username')
    readonly_fields = ('created_at',)
    raw_id_fields = ('article', 'changed_by')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def change_summary_short(self, obj: Any) -> str:
        """Truncated change summary"""
        return obj.change_summary[:50] + '...' if obj.change_summary and len(obj.change_summary) > 50 else obj.change_summary
    change_summary_short.short_description = 'Change Summary'  # type: ignore
    
    def content_diff_display(self, obj: Any) -> str:
        """Display content diff information"""
        if obj.content_diff:
            return f"Content changed ({len(str(obj.content_diff))} characters in diff)"
        return "No content changes"
    content_diff_display.short_description = 'Content Changes'  # type: ignore


@admin.register(ArticleView)
class ArticleViewAdmin(admin.ModelAdmin):
    """Article view tracking administration"""
    list_display = ('article', 'user', 'ip_address', 'user_agent_short', 'viewed_at')
    list_filter = ('viewed_at',)
    search_fields = ('article__title', 'user__username', 'ip_address')
    readonly_fields = ('viewed_at', 'user_agent')
    raw_id_fields = ('article', 'user')
    date_hierarchy = 'viewed_at'
    ordering = ('-viewed_at',)
    
    def user_agent_short(self, obj: Any) -> str:
        """Truncated user agent string"""
        return obj.user_agent[:50] + '...' if obj.user_agent and len(obj.user_agent) > 50 else obj.user_agent
    user_agent_short.short_description = 'User Agent'  # type: ignore


@admin.register(ArticleVote)
class ArticleVoteAdmin(admin.ModelAdmin):
    """Article voting administration"""
    list_display = ('article', 'user', 'is_helpful', 'voted_at')
    list_filter = ('is_helpful', 'voted_at')
    search_fields = ('article__title', 'user__username')
    readonly_fields = ('voted_at',)
    raw_id_fields = ('article', 'user')
    date_hierarchy = 'voted_at'
    ordering = ('-voted_at',)
    


@admin.register(ArticleAttachment)
class ArticleAttachmentAdmin(admin.ModelAdmin):
    """Article attachment administration"""
    list_display = ('name', 'article', 'file_size', 'uploaded_by', 'created_at')
    list_filter = ('mime_type', 'created_at')
    search_fields = ('name', 'description', 'article__title', 'uploaded_by__username')
    readonly_fields = ('created_at', 'file_size', 'mime_type')
    raw_id_fields = ('article', 'uploaded_by')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    def file_size_mb(self, obj: Any) -> str:
        """Display file size in MB"""
        if obj.file_size:
            return f"{obj.file_size / 1024 / 1024:.2f} MB"
        return "Unknown"
    file_size_mb.short_description = 'File Size'  # type: ignore


# Customize admin site header and title
admin.site.site_header = 'CLEAR Platform Administration'
admin.site.site_title = 'CLEAR Admin'
admin.site.index_title = 'Utility Coordination Platform Management'
