"""
CLEAR Views Compatibility Module

This module maintains backward compatibility for URL imports while the views
are being migrated to a package structure. It imports all views from the
views package to ensure that existing URL patterns continue to work.

This is a temporary compatibility layer during the migration process.
"""

from typing import Union, Callable
from django.contrib.auth import logout as auth_logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import AuthenticationForm
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect, HttpResponsePermanentRedirect
from django.shortcuts import redirect, render

# Type aliases for Django responses
DjangoResponse = Union[HttpResponse, HttpResponseRedirect, HttpResponsePermanentRedirect]

# Import all views from the views package to maintain backward compatibility
try:
    from .views.admin_views import *  # type: ignore
    from .views.analytics_views import *  # type: ignore
    from .views.auth_views import *  # type: ignore
    from .views.dashboard_views import *  # type: ignore
    from .views.document_views import *  # type: ignore
    from .views.gis_views import *  # type: ignore
    from .views.htmx_views import *  # type: ignore
    from .views.knowledge_views import *  # type: ignore
    from .views.mapping_views import *  # type: ignore
    from .views.messaging_views import *  # type: ignore
    from .views.profile_views import *  # type: ignore
    from .views.project_views import *  # type: ignore
    from .views.reporting_views import *  # type: ignore
    from .views.stakeholder_views import *  # type: ignore
    from .views.task_views import *  # type: ignore
except ImportError:
    # If views package import fails, provide critical stub functions
    
    def login_view(request: HttpRequest) -> DjangoResponse:
        """Stub login view"""
        if request.user.is_authenticated:
            return redirect('/dashboard/')
        return render(request, 'auth/login.html', {'form': AuthenticationForm()})

    def logout_view(request: HttpRequest) -> Union[HttpResponseRedirect, HttpResponsePermanentRedirect]:
        """Stub logout view"""
        auth_logout(request)
        return redirect('/login/')

    @login_required
    def dashboard(request: HttpRequest) -> HttpResponse:
        """Main dashboard view with authentication required"""
        return render(request, 'dashboard/index.html', {'user': request.user})
    
    # Provide basic stub for other critical views
    @login_required
    def dashboard_stats(request: HttpRequest) -> HttpResponse:
        return HttpResponse("Dashboard stats not implemented", status=501)

    @login_required
    def recent_activity(request: HttpRequest) -> HttpResponse:
        return HttpResponse("Recent activity not implemented", status=501)

    @login_required
    def messaging_interface(request: HttpRequest) -> HttpResponse:
        return HttpResponse("Messaging interface not implemented", status=501)
    
    # Add stubs for other critical views as needed
    class SignupView:
        @staticmethod
        def as_view() -> Callable[[HttpRequest], HttpResponse]:
            def view(request: HttpRequest) -> HttpResponse:
                return HttpResponse("Signup not implemented", status=501)
            return view

    class ProjectListView:
        @staticmethod
        def as_view() -> Callable[[HttpRequest], HttpResponse]:
            def view(request: HttpRequest) -> HttpResponse:
                return HttpResponse("Project list not implemented", status=501)
            return view

    class ProjectDetailView:
        @staticmethod
        def as_view() -> Callable[[HttpRequest], HttpResponse]:
            def view(request: HttpRequest) -> HttpResponse:
                return HttpResponse("Project detail not implemented", status=501)
            return view

    class ProjectCreateView:
        @staticmethod
        def as_view() -> Callable[[HttpRequest], HttpResponse]:
            def view(request: HttpRequest) -> HttpResponse:
                return HttpResponse("Project create not implemented", status=501)
            return view

    class ProjectEditView:
        @staticmethod
        def as_view() -> Callable[[HttpRequest], HttpResponse]:
            def view(request: HttpRequest) -> HttpResponse:
                return HttpResponse("Project edit not implemented", status=501)
            return view