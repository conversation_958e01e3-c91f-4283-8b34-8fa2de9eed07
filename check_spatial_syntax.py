#!/usr/bin/env python3
"""Check syntax of spatial.py file."""

import ast


def check_file():
    with open('CLEAR/models/spatial.py', 'r') as f:
        lines = f.readlines()
    
    # Try parsing progressively
    for i in range(len(lines)):
        try:
            code = ''.join(lines[:i+1])
            ast.parse(code)
        except SyntaxError as e:
            print(f"Syntax error at line {i+1}: {e}")
            print(f"\nLines {max(0, i-5)} to {i+1}:")
            for j in range(max(0, i-5), min(i+2, len(lines))):
                print(f"{j+1}: {lines[j].rstrip()}")
            
            # Check if it's a docstring issue
            if "triple-quoted string" in str(e):
                # Count triple quotes up to this point
                count = code.count('"""')
                print(f"\nTriple quote count up to line {i+1}: {count}")
                if count % 2 == 1:
                    print("Odd number of triple quotes - unclosed docstring!")
            
            return False
    
    print("No syntax errors found!")
    return True

if __name__ == "__main__":
    check_file()