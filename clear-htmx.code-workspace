{
    "folders": [
        {
            "name": "CLEAR HTMX Project",
            "path": "."
        }
    ],
    "settings": {
        // Python Configuration
        "python.defaultInterpreterPath": "/workspaces/clear_htmx/.conda/bin/python",
        "python.languageServer": "Pylance",
        "python.analysis.typeCheckingMode": "basic",
        
        // Django Project Structure
        "python.analysis.extraPaths": [
            "./",
            "./CLEAR",
            "./clear_htmx",
            "./templates",
            "./static"
        ],
        
        // File Associations for Django Templates
        "files.associations": {
            "*.html": "django-html",
            "*.htm": "django-html"
        },
        
        // Search Configuration
        "search.exclude": {
            "**/node_modules": true,
            "**/bower_components": true,
            "**/*.code-search": true,
            "**/__pycache__": true,
            "**/migrations": true,
            "**/staticfiles": true,
            "**/media": true,
            "**/logs": true,
            "**/coty-playground": true,
            "**/source-repo": true,
            "**/refactor_migration": true
        },
        
        // Files to exclude from file explorer
        "files.exclude": {
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/*.pyo": true,
            "**/fix_*.py": true,
            "**/check_*.py": true,
            "**/comprehensive_*.py": true,
            "**/configure_*.py": true,
            "**/run_all_*.py": true,
            "**/phase_*.json": true,
            "**/test_results*.json": true
        },
        
        // Editor Configuration
        "editor.rulers": [88, 120],
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        
        // Python Formatting
        "python.formatting.provider": "black",
        "python.formatting.blackArgs": ["--line-length=88"],
        "python.sortImports.args": ["--profile", "black"],
        
        // Django Template Configuration
        "emmet.includeLanguages": {
            "django-html": "html"
        },
        
        // Terminal Configuration
        "terminal.integrated.env.linux": {
            "DJANGO_SETTINGS_MODULE": "clear_htmx.settings",
            "PYTHONPATH": "${workspaceFolder}"
        }
    },
    "extensions": {
        "recommendations": [
            "ms-python.python",
            "ms-python.pylance",
            "batisteo.vscode-django",
            "wholroyd.jinja"
        ]
    },
    "launch": {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "Django: Debug Server",
                "type": "python",
                "request": "launch",
                "program": "${workspaceFolder}/manage.py",
                "args": ["runserver", "0.0.0.0:8000"],
                "django": true,
                "env": {
                    "DJANGO_SETTINGS_MODULE": "clear_htmx.settings",
                    "PYTHONPATH": "${workspaceFolder}",
                    "DEBUG": "True"
                },
                "console": "integratedTerminal",
                "cwd": "${workspaceFolder}",
                "python": "/workspaces/clear_htmx/.conda/bin/python"
            }
        ]
    }
}
