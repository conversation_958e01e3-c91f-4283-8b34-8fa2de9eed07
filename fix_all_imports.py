#!/usr/bin/env python3
"""
Fix all import errors comprehensively in CLEAR Django application.
"""

import os
import re
import subprocess
from pathlib import Path


def fix_all_imports():
    """Fix all missing imports in view files."""
    print("🔧 Fixing all missing imports in view files...")

    # Common imports that views typically need
    common_view_imports = """from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin, UserPassesTestMixin
from django.views.generic import (
    View, TemplateView, ListView, DetailView, CreateView, UpdateView, DeleteView,
    FormView, RedirectView
)
from django.views.generic.edit import FormMixin, ModelFormMixin, ProcessFormView, DeletionMixin
from django.views.generic.base import ContextMixin, TemplateResponseMixin
from django.contrib.auth.decorators import login_required, permission_required
from django.views.decorators.http import require_http_methods, require_GET, require_POST
from django.views.decorators.csrf import csrf_exempt, csrf_protect
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect, Http404
from django.urls import reverse, reverse_lazy
from django.contrib import messages
from django.db.models import Q, F, Count, Sum, Avg, Max, Min
from django.utils import timezone
from django.core.exceptions import ValidationError, PermissionDenied, ObjectDoesNotExist
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import transaction, connection
from django.conf import settings
import logging

logger = logging.getLogger(__name__)
"""

    # Find all view files
    view_files = subprocess.run(
        ['find', 'CLEAR/views/', '-name', '*.py', '-type', 'f'],
        capture_output=True, text=True
    ).stdout.strip().split('\n')

    for file_path in view_files:
        if not file_path or '__pycache__' in file_path:
            continue

        try:
            with open(file_path) as f:
                content = f.read()

            # Skip if already has comprehensive imports
            if 'from django.contrib.auth.mixins import LoginRequiredMixin' in content:
                continue

            # Check if file uses any of these classes
            needs_imports = False
            for pattern in ['LoginRequiredMixin', 'TemplateView', 'ListView', 'DetailView',
                          'CreateView', 'UpdateView', 'DeleteView', 'login_required',
                          'JsonResponse', 'HttpResponse', 'render', 'redirect']:
                if re.search(rf'\b{pattern}\b', content):
                    needs_imports = True
                    break

            if needs_imports:
                # Add imports after the module docstring and existing imports
                lines = content.split('\n')
                insert_index = 0
                docstring_count = 0

                for i, line in enumerate(lines):
                    # Handle module docstring
                    if line.strip().startswith('"""') or line.strip().startswith("'''"):
                        docstring_count += 1
                        if docstring_count == 2:  # End of module docstring
                            insert_index = i + 1
                            break
                    elif docstring_count == 0 and (line.startswith('import ') or line.startswith('from ')):
                        insert_index = i
                        break

                # Insert common imports
                lines.insert(insert_index, common_view_imports)

                with open(file_path, 'w') as f:
                    f.write('\n'.join(lines))
                print(f"✅ Added imports to {file_path}")

        except Exception as e:
            print(f"⚠️  Could not process {file_path}: {e}")

def remove_duplicate_imports():
    """Remove duplicate imports from files."""
    print("\n🔧 Removing duplicate imports...")

    view_files = subprocess.run(
        ['find', 'CLEAR/', '-name', '*.py', '-type', 'f'],
        capture_output=True, text=True
    ).stdout.strip().split('\n')

    for file_path in view_files:
        if not file_path or '__pycache__' in file_path:
            continue

        try:
            with open(file_path) as f:
                lines = f.readlines()

            seen_imports = set()
            new_lines = []

            for line in lines:
                if line.strip().startswith('from ') or line.strip().startswith('import '):
                    if line.strip() not in seen_imports:
                        seen_imports.add(line.strip())
                        new_lines.append(line)
                else:
                    new_lines.append(line)

            with open(file_path, 'w') as f:
                f.writelines(new_lines)

        except Exception as e:
            print(f"⚠️  Could not clean {file_path}: {e}")

def fix_specific_files():
    """Fix specific known issues."""
    print("\n🔧 Fixing specific known issues...")

    # Fix analytics_views.py
    analytics_file = Path('CLEAR/views/analytics_views.py')
    if analytics_file.exists():
        content = analytics_file.read_text()
        if 'from django.contrib.auth.mixins import LoginRequiredMixin' not in content:
            lines = content.split('\n')
            # Find where to insert
            for i, line in enumerate(lines):
                if line.startswith('from ') or line.startswith('import '):
                    lines.insert(i, 'from django.contrib.auth.mixins import LoginRequiredMixin')
                    lines.insert(i+1, 'from django.views.generic import TemplateView')
                    break
            analytics_file.write_text('\n'.join(lines))
            print("✅ Fixed analytics_views.py imports")

def main():
    """Main function."""
    print("🚀 Fixing all import errors in CLEAR Django app...")

    # Change to project directory
    os.chdir('/workspaces/clear_htmx')

    # Fix all imports
    fix_all_imports()

    # Fix specific files
    fix_specific_files()

    # Remove duplicates
    remove_duplicate_imports()

    # Run Django check
    print("\n🔍 Running Django check...")
    result = subprocess.run(
        ['python', 'manage.py', 'check', '--settings=clear_htmx.dev_settings'],
        capture_output=True, text=True
    )

    if result.returncode == 0:
        print("\n✅ Django check passed!")
    else:
        print(f"\n⚠️  Django check failed:\n{result.stderr}")

    # Run ruff check
    print("\n📊 Running ruff check...")
    result = subprocess.run(
        ['ruff', 'check', 'CLEAR/', '--statistics'],
        capture_output=True, text=True
    )

    if result.stdout:
        print(result.stdout)

    print("\n✅ Import fixing complete!")

if __name__ == "__main__":
    main()
"""
"""
