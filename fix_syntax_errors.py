#!/usr/bin/env python3
"""
Script to fix remaining syntax errors in the CLEAR codebase.
"""

import os
import re
import ast
from pathlib import Path

def find_unclosed_triple_quotes(file_path):
    """Find unclosed triple quotes in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count triple quotes
        triple_quote_count = content.count('"""')
        if triple_quote_count % 2 != 0:
            return True
        return False
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False

def fix_htmx_views():
    """Fix the unclosed triple quote in htmx_views.py."""
    file_path = '/workspaces/clear_htmx/CLEAR/views/htmx_views.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simply fix the specific issue - line 5378 needs closing quotes
        lines = content.split('\n')
        
        # Find line 5378 and add closing triple quote if needed
        for i, line in enumerate(lines):
            if i == 5377:  # Line 5378 (0-indexed)
                if '"""Poll for new notifications (WebSocket fallback)"""' in line:
                    # Already fixed
                    break
                elif '"""Poll for new notifications (WebSocket fallback)' in line and not line.strip().endswith('"""'):
                    # Fix it
                    lines[i] = line.rstrip() + '"""'
                    print(f"Fixed line {i+1}: Added closing triple quote")
                    break
        
        # Write back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"Fixed htmx_views.py")
        return True
        
    except Exception as e:
        print(f"Error fixing htmx_views.py: {e}")
        return False

def fix_migration_views():
    """Fix the unclosed triple quote in migration_views.py."""
    file_path = '/workspaces/clear_htmx/CLEAR/views/migration_views.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the specific pattern around line 82-94
        lines = content.split('\n')
        
        # Find the problematic section
        for i, line in enumerate(lines):
            if i > 80 and i < 100:  # Around line 82-94
                if '"""' in line and lines[i].count('"""') == 1:
                    # Check if this is an unclosed docstring
                    # Look ahead to see if there's a closing """
                    found_closing = False
                    for j in range(i + 1, min(i + 20, len(lines))):
                        if '"""' in lines[j]:
                            found_closing = True
                            break
                    
                    if not found_closing:
                        # Add closing triple quote
                        lines[i] = lines[i].rstrip() + '"""'
                        break
        
        # Write back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"Fixed migration_views.py")
        return True
        
    except Exception as e:
        print(f"Error fixing migration_views.py: {e}")
        return False

def fix_test_auth():
    """Fix the unclosed triple quote in test_auth.py."""
    file_path = '/workspaces/clear_htmx/CLEAR/views/test_auth.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Look around line 143
        for i, line in enumerate(lines):
            if i > 140 and i < 150:  # Around line 143
                if '"""' in line and 'Get current system status for monitoring' in line:
                    # This line has an opening """ but likely no closing
                    if line.count('"""') == 1:
                        # Add closing triple quote
                        lines[i] = line + '"""'
                        break
        
        # Write back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"Fixed test_auth.py")
        return True
        
    except Exception as e:
        print(f"Error fixing test_auth.py: {e}")
        return False

def check_syntax_errors():
    """Check for remaining syntax errors."""
    files_to_check = [
        '/workspaces/clear_htmx/CLEAR/management/commands/export_schema_migration.py',
        '/workspaces/clear_htmx/CLEAR/routing.py',
        '/workspaces/clear_htmx/CLEAR/views/dashboard_views.py',
        '/workspaces/clear_htmx/CLEAR/views/htmx_views.py',
        '/workspaces/clear_htmx/CLEAR/views/migration_views.py',
        '/workspaces/clear_htmx/CLEAR/views/test_auth.py'
    ]
    
    errors = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                print(f"✓ {file_path} - No syntax errors")
            except SyntaxError as e:
                errors.append(f"✗ {file_path}:{e.lineno} - {e.msg}")
                print(f"✗ {file_path}:{e.lineno} - {e.msg}")
            except Exception as e:
                errors.append(f"✗ {file_path} - {str(e)}")
                print(f"✗ {file_path} - {str(e)}")
    
    return errors

def fix_specific_errors():
    """Fix specific syntax errors manually."""
    
    # Fix export_schema_migration.py line 257
    file_path = '/workspaces/clear_htmx/CLEAR/management/commands/export_schema_migration.py'
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for unterminated string around line 257
        lines = content.split('\n')
        for i in range(250, min(260, len(lines))):
            if i < len(lines) and '"""' in lines[i] and lines[i].count('"""') == 1:
                lines[i] = lines[i].rstrip() + '"""'
                print(f"Fixed export_schema_migration.py line {i+1}")
                break
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
    except Exception as e:
        print(f"Error fixing export_schema_migration.py: {e}")
    
    # Fix dashboard_views.py line 177 (the docstring we tried to fix earlier)
    file_path = '/workspaces/clear_htmx/CLEAR/views/dashboard_views.py'
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Line 177 should be the docstring - make sure it's properly closed
        if len(lines) > 176:
            line = lines[176].strip()  # Line 177 (0-indexed)
            if '"""' in line and 'My Time component for dashboard' in line:
                if not line.endswith('"""'):
                    lines[176] = lines[176].rstrip() + '"""' + '\n'
                    print("Fixed dashboard_views.py line 177")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
    except Exception as e:
        print(f"Error fixing dashboard_views.py: {e}")

    # Fix migration_views.py - fix the empty docstring
    file_path = '/workspaces/clear_htmx/CLEAR/views/migration_views.py'
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix line 82 - replace empty docstring
        content = content.replace('    """"""', '    """Get the appropriate endpoint URL based on feature flags."""')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("Fixed migration_views.py empty docstring")
    except Exception as e:
        print(f"Error fixing migration_views.py: {e}")

def main():
    """Main function to fix all syntax errors."""
    print("Starting syntax error fixes...")
    
    # Fix known issues
    print("\n1. Fixing htmx_views.py...")
    fix_htmx_views()
    
    print("\n2. Fixing migration_views.py...")
    fix_migration_views()
    
    print("\n3. Fixing test_auth.py...")
    fix_test_auth()
    
    print("\n4. Fixing specific remaining errors...")
    fix_specific_errors()
    
    print("\n5. Checking for remaining syntax errors...")
    errors = check_syntax_errors()
    
    if errors:
        print(f"\n{len(errors)} syntax errors remain:")
        for error in errors:
            print(f"  {error}")
    else:
        print("\n✓ All syntax errors fixed!")
    
    print("\nSyntax error fix complete.")

if __name__ == "__main__":
    main()