#!/usr/bin/env python3
"""Fix all model file headers by removing extra docstrings."""

import os

MODELS_DIR = '/workspaces/clear_htmx/CLEAR/models'

def fix_file(filepath):
    """Fix a file by removing the extra docstring after imports."""
    with open(filepath) as f:
        content = f.read()

    # Look for pattern of closing module docstring followed by imports and then extra docstring
    lines = content.split('\n')
    fixed_lines = []
    skip_next_empty_docstring = False

    for i, line in enumerate(lines):
        # After we see the pattern of imports, check for standalone docstring
        if i > 5 and line.strip() == '"""' and not skip_next_empty_docstring:
            # Check if this is just a hanging docstring (no content before next docstring)
            found_content = False
            for j in range(i+1, min(i+5, len(lines))):
                if lines[j].strip() and not lines[j].strip().startswith('#'):
                    found_content = True
                    break

            if not found_content:
                # This is a hanging docstring, skip it
                print(f"Removing hanging docstring at line {i+1} in {os.path.basename(filepath)}")
                continue

        fixed_lines.append(line)

    content = '\n'.join(fixed_lines)

    with open(filepath, 'w') as f:
        f.write(content)

def main():
    files_to_fix = [
        'analytics.py',
        'documents.py',
        'financial.py',
        'implementation.py',
        'intelligence.py',
        'knowledge.py',
        'system.py',
        'user_activity.py',
    ]

    for filename in files_to_fix:
        filepath = os.path.join(MODELS_DIR, filename)
        if os.path.exists(filepath):
            fix_file(filepath)

if __name__ == "__main__":
    main()
