#!/usr/bin/env python3
"""Fix all unclosed module docstrings in model files."""

import os

MODELS_DIR = '/workspaces/clear_htmx/CLEAR/models'

files_to_fix = {
    'analytics.py': (16, '    """'),
    'documents.py': (18, '    """'),
    'financial.py': (18, '    """'),
    'implementation.py': (17, '    """'),
    'intelligence.py': (15, '    """'),
    'knowledge.py': (15, '    """'),
    'system.py': (21, '    """'),
    'user_activity.py': (21, '    """'),
}

def fix_file(filename, line_num, text_to_remove):
    """Fix a specific file by removing the problematic line."""
    filepath = os.path.join(MODELS_DIR, filename)

    with open(filepath) as f:
        lines = f.readlines()

    # Remove the problematic line if it exists
    if line_num <= len(lines) and lines[line_num - 1].strip() == text_to_remove.strip():
        lines.pop(line_num - 1)
        print(f"Fixed {filename} by removing line {line_num}")

    # Also check for module docstrings that need closing
    if lines and lines[0].strip() == '"""':
        # Find where to close it
        closed = False
        for i in range(1, min(20, len(lines))):
            if '"""' in lines[i]:
                closed = True
                break
            elif lines[i].strip() and not lines[i].strip().startswith('#'):
                # Found code, insert closing before it
                if lines[i].strip().startswith(('import', 'from')):
                    lines.insert(i, '"""\n\n')
                    print(f"Added closing docstring at line {i+1} in {filename}")
                    closed = True
                    break

        if not closed:
            # Add after header comments
            for i in range(1, min(10, len(lines))):
                if lines[i].strip() == '':
                    lines.insert(i, '"""\n')
                    print(f"Added closing docstring at line {i+1} in {filename}")
                    break

    with open(filepath, 'w') as f:
        f.writelines(lines)

def main():
    print("Fixing all model docstrings...")

    for filename, (line_num, text) in files_to_fix.items():
        try:
            fix_file(filename, line_num, text)
        except Exception as e:
            print(f"Error fixing {filename}: {e}")

if __name__ == "__main__":
    main()
