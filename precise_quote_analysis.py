#!/usr/bin/env python3

def find_unclosed_quote():
    with open('/workspaces/clear_htmx/CLEAR/views/htmx_views.py') as f:
        content = f.read()

    # Try to parse sections of the file to find where the error starts
    lines = content.split('\n')

    # Start from line 5200 and work forward
    for test_line in range(5200, len(lines)):
        test_content = '\n'.join(lines[:test_line])

        try:
            compile(test_content, '<string>', 'exec')
        except SyntaxError as e:
            if 'unterminated triple-quoted string literal' in str(e):
                print(f"First syntax error at line {test_line}")
                print(f"Error: {e}")
                print("Content around this line:")

                start = max(0, test_line - 10)
                end = min(len(lines), test_line + 5)

                for i in range(start, end):
                    marker = " >>> " if i + 1 == test_line else "     "
                    line_content = lines[i] if i < len(lines) else ""
                    print(f"{marker}{i+1:4d}: {line_content}")

                return test_line

    print("No syntax error found in the range tested")
    return None

if __name__ == "__main__":
    find_unclosed_quote()
