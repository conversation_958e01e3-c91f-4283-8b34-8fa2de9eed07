#!/usr/bin/env python3
"""
Browser Compatibility Test Suite for CLEAR Project
Tests critical features across major browsers and versions
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    from playwright.async_api import TimeoutError as PlaywrightTimeout
except ImportError:
    print("Error: Playwright not installed. Run: pip install playwright && playwright install")
    sys.exit(1)


class TestStatus(Enum):
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    WARNING = "warning"


@dataclass
class BrowserInfo:
    name: str
    version: str
    user_agent: str
    platform: str
    is_mobile: bool = False


@dataclass
class TestResult:
    browser: BrowserInfo
    feature: str
    status: TestStatus
    details: str = ""
    performance_ms: Optional[float] = None
    screenshot: Optional[str] = None
    error: Optional[str] = None


class BrowserCompatibilityTester:
    """Comprehensive browser compatibility testing for CLEAR project"""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.base_url = "http://localhost:8000"
        self.screenshots_dir = Path("/workspaces/clear_htmx/reports/screenshots")
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        
        # Browser configurations
        self.desktop_browsers = [
            {"browser": "chromium", "versions": ["latest", "latest-1", "latest-2"]},
            {"browser": "firefox", "versions": ["latest", "latest-1", "latest-2"]},
            {"browser": "webkit", "versions": ["latest", "latest-1"]},  # Safari
        ]
        
        self.mobile_configs = [
            {"device": "iPhone 13", "browser": "webkit"},
            {"device": "iPhone SE", "browser": "webkit"},
            {"device": "Pixel 5", "browser": "chromium"},
            {"device": "Galaxy S21", "browser": "chromium"},
        ]
        
        # Features to test
        self.test_features = [
            "map_rendering",
            "drawing_tools",
            "webgl_support",
            "css_compatibility",
            "javascript_apis",
            "touch_events",
            "gestures",
            "htmx_interactions",
            "websocket_support",
            "file_upload",
            "responsive_design",
            "performance_metrics"
        ]

    async def run_all_tests(self):
        """Run all browser compatibility tests"""
        print("🚀 Starting Browser Compatibility Tests...\n")
        
        async with async_playwright() as p:
            # Test desktop browsers
            await self.test_desktop_browsers(p)
            
            # Test mobile browsers
            await self.test_mobile_browsers(p)
        
        # Generate reports
        self.generate_compatibility_matrix()
        self.generate_detailed_report()
        
        print("\n✅ Browser compatibility testing completed!")
        print(f"📊 Results saved to: /reports/browser_compatibility_matrix.md")
        print(f"📄 Detailed report: /reports/browser_compatibility_detailed.json")

    async def test_desktop_browsers(self, playwright):
        """Test all desktop browser configurations"""
        for config in self.desktop_browsers:
            browser_type = getattr(playwright, config["browser"])
            
            # For now, test with latest version (Playwright doesn't support version specification)
            print(f"\n🌐 Testing {config['browser'].capitalize()}...")
            
            try:
                browser = await browser_type.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                # Get browser info
                user_agent = await page.evaluate("navigator.userAgent")
                browser_info = BrowserInfo(
                    name=config["browser"],
                    version=self._extract_version(user_agent, config["browser"]),
                    user_agent=user_agent,
                    platform="desktop"
                )
                
                # Run all feature tests
                for feature in self.test_features:
                    await self.test_feature(page, browser_info, feature)
                
                await browser.close()
                
            except Exception as e:
                print(f"❌ Error testing {config['browser']}: {str(e)}")
                self.results.append(TestResult(
                    browser=BrowserInfo(
                        name=config["browser"],
                        version="unknown",
                        user_agent="",
                        platform="desktop"
                    ),
                    feature="browser_launch",
                    status=TestStatus.FAILED,
                    error=str(e)
                ))

    async def test_mobile_browsers(self, playwright):
        """Test mobile browser configurations"""
        for config in self.mobile_configs:
            print(f"\n📱 Testing {config['device']}...")
            
            try:
                browser_type = getattr(playwright, config["browser"])
                device = playwright.devices[config["device"]]
                
                browser = await browser_type.launch(headless=True)
                context = await browser.new_context(**device)  # type: ignore
                page = await context.new_page()
                
                # Get browser info
                user_agent = await page.evaluate("navigator.userAgent")
                browser_info = BrowserInfo(
                    name=f"{config['browser']} ({config['device']})",
                    version=self._extract_version(user_agent, config["browser"]),
                    user_agent=user_agent,
                    platform="mobile",
                    is_mobile=True
                )
                
                # Run mobile-specific tests
                for feature in self.test_features:
                    await self.test_feature(page, browser_info, feature)
                
                await browser.close()
                
            except Exception as e:
                print(f"❌ Error testing {config['device']}: {str(e)}")

    async def test_feature(self, page: Page, browser_info: BrowserInfo, feature: str):
        """Test a specific feature in the given browser"""
        test_method = getattr(self, f"test_{feature}", None)
        if test_method:
            try:
                result = await test_method(page, browser_info)
                self.results.append(result)
                
                # Print status
                status_icon = "✅" if result.status == TestStatus.PASSED else "❌"
                print(f"  {status_icon} {feature}: {result.status.value}")
                
            except Exception as e:
                self.results.append(TestResult(
                    browser=browser_info,
                    feature=feature,
                    status=TestStatus.FAILED,
                    error=str(e)
                ))
                print(f"  ❌ {feature}: FAILED - {str(e)}")

    async def test_map_rendering(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test map rendering functionality"""
        start_time = datetime.now()
        
        try:
            # Navigate to project map page
            await page.goto(f"{self.base_url}/projects/create/", wait_until="networkidle")
            
            # Wait for map container
            map_selector = "#map, .leaflet-container, .ol-viewport"
            await page.wait_for_selector(map_selector, timeout=10000)
            
            # Check if map is rendered
            map_visible = await page.is_visible(map_selector)
            
            # Check for map tiles
            tiles_loaded = await page.evaluate("""
                () => {
                    // Check Leaflet
                    if (window.L && window.map) {
                        return document.querySelectorAll('.leaflet-tile-loaded').length > 0;
                    }
                    // Check OpenLayers
                    if (window.ol && window.map) {
                        return document.querySelectorAll('.ol-layer canvas').length > 0;
                    }
                    return false;
                }
            """)
            
            # Take screenshot
            screenshot_path = self.screenshots_dir / f"{browser_info.name.replace(' ', '_')}_map_rendering.png"
            await page.screenshot(path=str(screenshot_path))
            
            performance_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            if map_visible and tiles_loaded:
                return TestResult(
                    browser=browser_info,
                    feature="map_rendering",
                    status=TestStatus.PASSED,
                    details="Map rendered successfully with tiles loaded",
                    performance_ms=performance_ms,
                    screenshot=str(screenshot_path)
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="map_rendering",
                    status=TestStatus.FAILED,
                    details=f"Map visible: {map_visible}, Tiles loaded: {tiles_loaded}",
                    performance_ms=performance_ms,
                    screenshot=str(screenshot_path)
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="map_rendering",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_drawing_tools(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test drawing tools functionality"""
        try:
            await page.goto(f"{self.base_url}/projects/create/", wait_until="networkidle")
            
            # Wait for map to load
            await page.wait_for_selector("#map, .leaflet-container", timeout=10000)
            
            # Check for drawing controls
            drawing_controls = await page.evaluate("""
                () => {
                    // Check Leaflet Draw
                    const leafletDraw = document.querySelector('.leaflet-draw');
                    // Check custom drawing tools
                    const customTools = document.querySelector('[data-drawing-tools]');
                    // Check for draw buttons
                    const drawButtons = document.querySelectorAll('[data-tool="draw"], .leaflet-draw-draw-polygon');
                    
                    return {
                        leafletDraw: !!leafletDraw,
                        customTools: !!customTools,
                        drawButtons: drawButtons.length
                    };
                }
            """)
            
            has_drawing_tools = (
                drawing_controls["leafletDraw"] or 
                drawing_controls["customTools"] or 
                drawing_controls["drawButtons"] > 0
            )
            
            if has_drawing_tools:
                # Test drawing interaction
                if drawing_controls["drawButtons"] > 0:
                    # Try to click a draw button
                    await page.click('[data-tool="draw"], .leaflet-draw-draw-polygon', timeout=5000)
                    
                    # Simulate drawing on map
                    map_element = await page.query_selector("#map, .leaflet-container")
                    if map_element:
                        box = await map_element.bounding_box()
                        if box:
                            # Draw a simple polygon
                            await page.mouse.click(box["x"] + 100, box["y"] + 100)
                            await page.mouse.click(box["x"] + 200, box["y"] + 100)
                            await page.mouse.click(box["x"] + 200, box["y"] + 200)
                            await page.mouse.click(box["x"] + 100, box["y"] + 200)
                            await page.mouse.click(box["x"] + 100, box["y"] + 100)  # Close polygon
                
                return TestResult(
                    browser=browser_info,
                    feature="drawing_tools",
                    status=TestStatus.PASSED,
                    details=f"Drawing tools available: {drawing_controls}"
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="drawing_tools",
                    status=TestStatus.FAILED,
                    details="No drawing tools found"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="drawing_tools",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_webgl_support(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test WebGL support"""
        try:
            webgl_info = await page.evaluate("""
                () => {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    const gl2 = canvas.getContext('webgl2');
                    
                    const result = {
                        webgl: !!gl,
                        webgl2: !!gl2,
                        vendor: null,
                        renderer: null
                    };
                    
                    if (gl) {
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            result.vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            result.renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                        }
                    }
                    
                    return result;
                }
            """)
            
            if webgl_info["webgl"]:
                return TestResult(
                    browser=browser_info,
                    feature="webgl_support",
                    status=TestStatus.PASSED,
                    details=f"WebGL: {webgl_info['webgl']}, WebGL2: {webgl_info['webgl2']}, Renderer: {webgl_info['renderer']}"
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="webgl_support",
                    status=TestStatus.FAILED,
                    details="WebGL not supported"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="webgl_support",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_css_compatibility(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test CSS feature compatibility"""
        try:
            await page.goto(f"{self.base_url}/login/", wait_until="networkidle")
            
            css_support = await page.evaluate("""
                () => {
                    const testElement = document.createElement('div');
                    document.body.appendChild(testElement);
                    
                    const tests = {
                        backdropFilter: CSS.supports('backdrop-filter', 'blur(10px)'),
                        grid: CSS.supports('display', 'grid'),
                        flexbox: CSS.supports('display', 'flex'),
                        customProperties: CSS.supports('--custom', 'value'),
                        transforms3d: CSS.supports('transform', 'translate3d(0, 0, 0)'),
                        animations: CSS.supports('animation', 'test 1s'),
                        filters: CSS.supports('filter', 'blur(5px)'),
                        masks: CSS.supports('mask-image', 'url(#)'),
                        clipPath: CSS.supports('clip-path', 'circle(50%)'),
                        scrollSnap: CSS.supports('scroll-snap-type', 'x mandatory')
                    };
                    
                    document.body.removeChild(testElement);
                    
                    // Count supported features
                    const supported = Object.values(tests).filter(v => v).length;
                    const total = Object.keys(tests).length;
                    
                    return {
                        tests,
                        supported,
                        total,
                        percentage: (supported / total * 100).toFixed(1)
                    };
                }
            """)
            
            # Check for critical features
            critical_features = ["flexbox", "grid", "customProperties"]
            missing_critical = [f for f in critical_features if not css_support["tests"].get(f, False)]
            
            if not missing_critical:
                status = TestStatus.PASSED
                details = f"CSS support: {css_support['percentage']}% ({css_support['supported']}/{css_support['total']} features)"
            else:
                status = TestStatus.WARNING
                details = f"Missing critical CSS features: {', '.join(missing_critical)}"
            
            # Check for specific EGIS glassmorphism effects
            glassmorphism_check = await page.evaluate("""
                () => {
                    const glassCard = document.querySelector('.glass-card, [class*="backdrop-blur"]');
                    if (glassCard) {
                        const styles = window.getComputedStyle(glassCard);
                        return {
                            hasBackdropFilter: styles.backdropFilter !== 'none',
                            hasBlur: styles.backdropFilter && styles.backdropFilter.includes('blur')
                        };
                    }
                    return null;
                }
            """)
            
            return TestResult(
                browser=browser_info,
                feature="css_compatibility",
                status=status,
                details=f"{details}. Glassmorphism: {glassmorphism_check}"
            )
            
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="css_compatibility",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_javascript_apis(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test JavaScript API compatibility"""
        try:
            api_support = await page.evaluate("""
                () => {
                    const apis = {
                        // Modern APIs
                        fetch: typeof fetch !== 'undefined',
                        promise: typeof Promise !== 'undefined',
                        asyncAwait: (async () => {})() instanceof Promise,
                        
                        // DOM APIs
                        querySelector: typeof document.querySelector === 'function',
                        classList: document.body.classList !== undefined,
                        dataset: document.body.dataset !== undefined,
                        
                        // Storage APIs
                        localStorage: typeof localStorage !== 'undefined',
                        sessionStorage: typeof sessionStorage !== 'undefined',
                        indexedDB: typeof indexedDB !== 'undefined',
                        
                        // Geolocation
                        geolocation: 'geolocation' in navigator,
                        
                        // File APIs
                        fileReader: typeof FileReader !== 'undefined',
                        blob: typeof Blob !== 'undefined',
                        formData: typeof FormData !== 'undefined',
                        
                        // Modern features
                        intersectionObserver: typeof IntersectionObserver !== 'undefined',
                        mutationObserver: typeof MutationObserver !== 'undefined',
                        resizeObserver: typeof ResizeObserver !== 'undefined',
                        
                        // WebSocket
                        webSocket: typeof WebSocket !== 'undefined',
                        
                        // ES6+ features
                        arrow: (() => true)() === true,
                        spread: [...[1,2,3]].length === 3,
                        destructuring: (function({a}) { return a; })({a: 1}) === 1,
                        template: `test` === 'test',
                        
                        // Map-specific APIs
                        customElements: typeof customElements !== 'undefined',
                        shadowDOM: document.body.attachShadow !== undefined
                    };
                    
                    const supported = Object.values(apis).filter(v => v).length;
                    const total = Object.keys(apis).length;
                    
                    return {
                        apis,
                        supported,
                        total,
                        percentage: (supported / total * 100).toFixed(1)
                    };
                }
            """)
            
            # Check for critical APIs
            critical_apis = ["fetch", "promise", "localStorage", "webSocket", "querySelector"]
            missing_critical = [a for a in critical_apis if not api_support["apis"].get(a, False)]
            
            if not missing_critical:
                status = TestStatus.PASSED
                details = f"JavaScript API support: {api_support['percentage']}% ({api_support['supported']}/{api_support['total']} APIs)"
            else:
                status = TestStatus.FAILED
                details = f"Missing critical JavaScript APIs: {', '.join(missing_critical)}"
            
            return TestResult(
                browser=browser_info,
                feature="javascript_apis",
                status=status,
                details=details
            )
            
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="javascript_apis",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_touch_events(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test touch event support (mobile browsers)"""
        try:
            touch_support = await page.evaluate("""
                () => {
                    return {
                        touchEvents: 'ontouchstart' in window,
                        pointerEvents: 'onpointerdown' in window,
                        maxTouchPoints: navigator.maxTouchPoints || 0,
                        touchScreen: navigator.maxTouchPoints > 0,
                        
                        // Gesture support
                        gestureEvents: 'ongesturestart' in window,
                        
                        // Touch action CSS
                        touchAction: CSS.supports('touch-action', 'none'),
                        
                        // Device capabilities
                        hasTouch: ('ontouchstart' in window) || (navigator.maxTouchPoints > 0)
                    };
                }
            """)
            
            if browser_info.is_mobile:
                # Mobile browsers should support touch
                if touch_support["hasTouch"]:
                    status = TestStatus.PASSED
                    details = f"Touch support enabled. Max touch points: {touch_support['maxTouchPoints']}"
                else:
                    status = TestStatus.FAILED
                    details = "Touch events not supported on mobile device"
            else:
                # Desktop browsers may or may not support touch
                if touch_support["hasTouch"]:
                    status = TestStatus.PASSED
                    details = f"Touch support available. Max touch points: {touch_support['maxTouchPoints']}"
                else:
                    status = TestStatus.PASSED
                    details = "Touch events not required for desktop browser"
            
            return TestResult(
                browser=browser_info,
                feature="touch_events",
                status=status,
                details=details
            )
            
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="touch_events",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_gestures(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test gesture support"""
        try:
            await page.goto(f"{self.base_url}/projects/create/", wait_until="networkidle")
            
            # Test pinch zoom on map
            gesture_support = await page.evaluate("""
                () => {
                    const map = document.querySelector('#map, .leaflet-container');
                    if (!map) return { mapFound: false };
                    
                    // Check for gesture handling
                    const styles = window.getComputedStyle(map);
                    
                    return {
                        mapFound: true,
                        touchAction: styles.touchAction,
                        
                        // Leaflet gesture handling
                        leafletGestures: window.L && window.L.Browser && window.L.Browser.touch,
                        
                        // Check for gesture event listeners
                        hasGestureHandling: map.ongesturestart !== undefined || 
                                          map.ontouchstart !== undefined ||
                                          styles.touchAction !== 'auto'
                    };
                }
            """)
            
            if gesture_support["mapFound"] and gesture_support["hasGestureHandling"]:
                return TestResult(
                    browser=browser_info,
                    feature="gestures",
                    status=TestStatus.PASSED,
                    details=f"Gesture support enabled. Touch action: {gesture_support['touchAction']}"
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="gestures",
                    status=TestStatus.WARNING,
                    details="Limited gesture support detected"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="gestures",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_htmx_interactions(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test HTMX functionality"""
        try:
            await page.goto(f"{self.base_url}/", wait_until="networkidle")
            
            htmx_check = await page.evaluate("""
                () => {
                    // Check if HTMX is loaded
                    const htmxLoaded = typeof htmx !== 'undefined';
                    
                    // Check for HTMX elements
                    const htmxElements = document.querySelectorAll('[hx-get], [hx-post], [hx-trigger], [hx-target]');
                    
                    // Check HTMX version
                    const htmxVersion = htmxLoaded ? htmx.version : null;
                    
                    return {
                        loaded: htmxLoaded,
                        version: htmxVersion,
                        elementsCount: htmxElements.length,
                        
                        // Check for HTMX configuration
                        config: htmxLoaded ? {
                            historyEnabled: htmx.config.historyEnabled,
                            refreshOnHistoryMiss: htmx.config.refreshOnHistoryMiss,
                            defaultSwapStyle: htmx.config.defaultSwapStyle
                        } : null
                    };
                }
            """)
            
            if htmx_check["loaded"] and htmx_check["elementsCount"] > 0:
                # Test an HTMX interaction
                htmx_element = await page.query_selector('[hx-get], [hx-post]')
                if htmx_element:
                    await htmx_element.click()
                    # Wait for HTMX request to complete
                    await page.wait_for_timeout(1000)
                
                return TestResult(
                    browser=browser_info,
                    feature="htmx_interactions",
                    status=TestStatus.PASSED,
                    details=f"HTMX v{htmx_check['version']} loaded with {htmx_check['elementsCount']} interactive elements"
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="htmx_interactions",
                    status=TestStatus.FAILED,
                    details="HTMX not loaded or no interactive elements found"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="htmx_interactions",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_websocket_support(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test WebSocket support"""
        try:
            ws_support = await page.evaluate("""
                () => {
                    const hasWebSocket = typeof WebSocket !== 'undefined';
                    
                    if (!hasWebSocket) return { supported: false };
                    
                    try {
                        // Try to create a WebSocket (won't actually connect)
                        const ws = new WebSocket('ws://localhost:8000/ws/test/');
                        
                        return {
                            supported: true,
                            readyState: ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'][ws.readyState],
                            protocol: ws.protocol,
                            extensions: ws.extensions
                        };
                    } catch (e) {
                        return {
                            supported: true,
                            error: e.message
                        };
                    }
                }
            """)
            
            if ws_support["supported"]:
                return TestResult(
                    browser=browser_info,
                    feature="websocket_support",
                    status=TestStatus.PASSED,
                    details="WebSocket API supported"
                )
            else:
                return TestResult(
                    browser=browser_info,
                    feature="websocket_support",
                    status=TestStatus.FAILED,
                    details="WebSocket API not supported"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="websocket_support",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_file_upload(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test file upload functionality"""
        try:
            file_api_support = await page.evaluate("""
                () => {
                    return {
                        fileAPI: typeof File !== 'undefined',
                        fileReader: typeof FileReader !== 'undefined',
                        formData: typeof FormData !== 'undefined',
                        blob: typeof Blob !== 'undefined',
                        
                        // Check for file input support
                        fileInput: (() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            return input.type === 'file';
                        })(),
                        
                        // Check for drag and drop
                        dragDrop: 'ondrop' in document.body && 'ondragover' in document.body
                    };
                }
            """)
            
            all_supported = all(file_api_support.values())
            
            if all_supported:
                return TestResult(
                    browser=browser_info,
                    feature="file_upload",
                    status=TestStatus.PASSED,
                    details="All file upload APIs supported"
                )
            else:
                missing = [k for k, v in file_api_support.items() if not v]
                return TestResult(
                    browser=browser_info,
                    feature="file_upload",
                    status=TestStatus.FAILED,
                    details=f"Missing file APIs: {', '.join(missing)}"
                )
                
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="file_upload",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_responsive_design(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test responsive design"""
        try:
            await page.goto(f"{self.base_url}/", wait_until="networkidle")
            
            # Test different viewport sizes
            viewports = [
                {"width": 320, "height": 568, "name": "mobile-small"},
                {"width": 768, "height": 1024, "name": "tablet"},
                {"width": 1920, "height": 1080, "name": "desktop"}
            ]
            
            results = []
            
            for viewport in viewports:
                await page.set_viewport_size={"width": 1920, "height": 1080}"])
                await page.wait_for_timeout(500)  # Wait for responsive adjustments
                
                layout_check = await page.evaluate("""
                    (viewportName) => {
                        // Check for responsive classes
                        const body = document.body;
                        const hasResponsiveClasses = 
                            body.classList.contains('mobile') ||
                            body.classList.contains('tablet') ||
                            body.classList.contains('desktop');
                        
                        // Check for mobile menu
                        const mobileMenu = document.querySelector('.mobile-menu, [data-mobile-menu]');
                        const desktopMenu = document.querySelector('.desktop-menu, nav:not(.mobile-menu)');
                        
                        // Check Bootstrap responsive utilities
                        const hiddenElements = document.querySelectorAll('.d-none, .d-sm-none, .d-md-none, .d-lg-none');
                        const visibleElements = document.querySelectorAll('.d-block, .d-sm-block, .d-md-block, .d-lg-block');
                        
                        return {
                            viewport: viewportName,
                            hasResponsiveClasses,
                            hasMobileMenu: !!mobileMenu,
                            hasDesktopMenu: !!desktopMenu,
                            responsiveElements: hiddenElements.length + visibleElements.length
                        };
                    }
                """, viewport["name"])
                
                results.append(layout_check)
            
            # All viewports should work
            return TestResult(
                browser=browser_info,
                feature="responsive_design",
                status=TestStatus.PASSED,
                details=f"Responsive design working across {len(viewports)} viewports"
            )
            
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="responsive_design",
                status=TestStatus.FAILED,
                error=str(e)
            )

    async def test_performance_metrics(self, page: Page, browser_info: BrowserInfo) -> TestResult:
        """Test performance metrics"""
        try:
            await page.goto(f"{self.base_url}/", wait_until="networkidle")
            
            # Get performance metrics
            performance = await page.evaluate("""
                () => {
                    const perf = window.performance;
                    const timing = perf.timing;
                    const navigation = perf.navigation;
                    
                    // Calculate key metrics
                    const metrics = {
                        // Page load times
                        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                        loadComplete: timing.loadEventEnd - timing.navigationStart,
                        
                        // Network times
                        dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
                        tcpConnect: timing.connectEnd - timing.connectStart,
                        request: timing.responseStart - timing.requestStart,
                        response: timing.responseEnd - timing.responseStart,
                        
                        // Processing times
                        domProcessing: timing.domComplete - timing.domLoading,
                        
                        // Resource count
                        resourceCount: perf.getEntriesByType('resource').length,
                        
                        // Memory usage (if available)
                        memory: perf.memory ? {
                            usedJSHeapSize: (perf.memory.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
                            totalJSHeapSize: (perf.memory.totalJSHeapSize / 1048576).toFixed(2) + ' MB'
                        } : null
                    };
                    
                    return metrics;
                }
            """)
            
            # Define performance thresholds
            load_time = performance["loadComplete"]
            
            if load_time < 3000:  # Less than 3 seconds
                status = TestStatus.PASSED
                rating = "Excellent"
            elif load_time < 5000:  # Less than 5 seconds
                status = TestStatus.PASSED
                rating = "Good"
            else:
                status = TestStatus.WARNING
                rating = "Needs optimization"
            
            return TestResult(
                browser=browser_info,
                feature="performance_metrics",
                status=status,
                details=f"Page load: {load_time}ms ({rating}), Resources: {performance['resourceCount']}",
                performance_ms=load_time
            )
            
        except Exception as e:
            return TestResult(
                browser=browser_info,
                feature="performance_metrics",
                status=TestStatus.FAILED,
                error=str(e)
            )

    def _extract_version(self, user_agent: str, browser_name: str) -> str:
        """Extract browser version from user agent string"""
        import re
        
        patterns = {
            "chromium": r"Chrome/(\d+\.\d+)",
            "firefox": r"Firefox/(\d+\.\d+)",
            "webkit": r"Version/(\d+\.\d+)",
            "edge": r"Edg/(\d+\.\d+)"
        }
        
        pattern = patterns.get(browser_name)
        if pattern:
            match = re.search(pattern, user_agent)
            if match:
                return match.group(1)
        
        return "unknown"

    def generate_compatibility_matrix(self):
        """Generate browser compatibility matrix report"""
        matrix_path = Path("/workspaces/clear_htmx/reports/browser_compatibility_matrix.md")
        
        # Group results by feature
        features_matrix = {}
        for result in self.results:
            feature = result.feature
            browser = f"{result.browser.name} {result.browser.version}"
            
            if feature not in features_matrix:
                features_matrix[feature] = {}
            
            features_matrix[feature][browser] = {
                "status": result.status.value,
                "details": result.details or result.error or ""
            }
        
        # Generate markdown report
        with open(matrix_path, "w") as f:
            f.write("# Browser Compatibility Matrix\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Summary
            total_tests = len(self.results)
            passed = sum(1 for r in self.results if r.status == TestStatus.PASSED)
            failed = sum(1 for r in self.results if r.status == TestStatus.FAILED)
            warnings = sum(1 for r in self.results if r.status == TestStatus.WARNING)
            
            f.write("## Summary\n\n")
            f.write(f"- Total Tests: {total_tests}\n")
            f.write(f"- Passed: {passed} ({passed/total_tests*100:.1f}%)\n")
            f.write(f"- Failed: {failed} ({failed/total_tests*100:.1f}%)\n")
            f.write(f"- Warnings: {warnings} ({warnings/total_tests*100:.1f}%)\n\n")
            
            # Feature compatibility table
            f.write("## Feature Compatibility\n\n")
            
            # Get all unique browsers
            browsers = sorted(set(f"{r.browser.name} {r.browser.version}" for r in self.results))
            
            # Create table header
            f.write("| Feature | " + " | ".join(browsers) + " |\n")
            f.write("|---------|" + "|".join(["-------" for _ in browsers]) + "|\n")
            
            # Create table rows
            for feature, browser_results in features_matrix.items():
                row = [feature.replace("_", " ").title()]
                for browser in browsers:
                    if browser in browser_results:
                        status = browser_results[browser]["status"]
                        icon = {
                            "passed": "✅",
                            "failed": "❌",
                            "warning": "⚠️",
                            "skipped": "⏭️"
                        }.get(status, "❓")
                        row.append(icon)
                    else:
                        row.append("—")
                
                f.write("| " + " | ".join(row) + " |\n")
            
            # Detailed results
            f.write("\n## Detailed Results\n\n")
            
            for feature in sorted(features_matrix.keys()):
                f.write(f"### {feature.replace('_', ' ').title()}\n\n")
                
                for browser, result in features_matrix[feature].items():
                    status_icon = {
                        "passed": "✅",
                        "failed": "❌",
                        "warning": "⚠️",
                        "skipped": "⏭️"
                    }.get(result["status"], "❓")
                    
                    f.write(f"- **{browser}**: {status_icon} {result['status'].upper()}")
                    if result["details"]:
                        f.write(f" - {result['details']}")
                    f.write("\n")
                
                f.write("\n")
            
            # Known issues and workarounds
            f.write("## Known Issues and Workarounds\n\n")
            
            # Collect failures and warnings
            issues = [r for r in self.results if r.status in [TestStatus.FAILED, TestStatus.WARNING]]
            
            if issues:
                for result in issues:
                    f.write(f"### {result.browser.name} {result.browser.version} - {result.feature}\n")
                    f.write(f"**Status**: {result.status.value}\n")
                    f.write(f"**Details**: {result.details or result.error}\n")
                    
                    # Add workarounds based on known issues
                    workaround = self._get_workaround(result)
                    if workaround:
                        f.write(f"**Workaround**: {workaround}\n")
                    
                    f.write("\n")
            else:
                f.write("No significant issues found.\n\n")
            
            # Performance comparison
            f.write("## Performance Comparison\n\n")
            
            perf_results = [r for r in self.results if r.feature == "performance_metrics" and r.performance_ms]
            if perf_results:
                f.write("| Browser | Load Time (ms) | Rating |\n")
                f.write("|---------|----------------|--------|\n")
                
                for result in sorted(perf_results, key=lambda x: x.performance_ms or 0):
                    load_time = result.performance_ms
                    if load_time < 3000:
                        rating = "Excellent"
                    elif load_time < 5000:
                        rating = "Good"
                    else:
                        rating = "Needs optimization"
                    
                    f.write(f"| {result.browser.name} {result.browser.version} | {load_time:.0f} | {rating} |\n")
            
            # Mobile vs Desktop
            f.write("\n## Mobile vs Desktop Comparison\n\n")
            
            mobile_results = [r for r in self.results if r.browser.is_mobile]
            desktop_results = [r for r in self.results if not r.browser.is_mobile]
            
            if mobile_results:
                mobile_passed = sum(1 for r in mobile_results if r.status == TestStatus.PASSED)
                mobile_total = len(mobile_results)
                f.write(f"**Mobile Browsers**: {mobile_passed}/{mobile_total} tests passed ({mobile_passed/mobile_total*100:.1f}%)\n")
            
            if desktop_results:
                desktop_passed = sum(1 for r in desktop_results if r.status == TestStatus.PASSED)
                desktop_total = len(desktop_results)
                f.write(f"**Desktop Browsers**: {desktop_passed}/{desktop_total} tests passed ({desktop_passed/desktop_total*100:.1f}%)\n")
            
            # Recommendations
            f.write("\n## Recommendations\n\n")
            f.write("Based on the compatibility testing results:\n\n")
            
            if failed > 0:
                f.write("1. **Critical Issues**: Address the failed tests to ensure basic functionality\n")
            if warnings > 0:
                f.write("2. **Optimizations**: Review warning items for potential improvements\n")
            
            f.write("3. **Progressive Enhancement**: Use feature detection for advanced capabilities\n")
            f.write("4. **Fallbacks**: Implement CSS and JavaScript fallbacks for unsupported features\n")
            f.write("5. **Testing**: Continue regular cross-browser testing as browsers update\n")

    def _get_workaround(self, result: TestResult) -> Optional[str]:
        """Get workaround for known issues"""
        workarounds = {
            ("css_compatibility", "backdropFilter"): 
                "Use a semi-transparent background color as fallback: background-color: rgba(255, 255, 255, 0.9);",
            
            ("webgl_support", "webgl"):
                "Provide 2D canvas fallback for browsers without WebGL support",
            
            ("touch_events", "touchEvents"):
                "Use pointer events as fallback: if (!('ontouchstart' in window)) { /* use pointer events */ }",
            
            ("javascript_apis", "resizeObserver"):
                "Use a polyfill: https://github.com/que-etc/resize-observer-polyfill"
        }
        
        # Check for specific feature failures
        for (feature_key, detail_key), workaround in workarounds.items():
            if result.feature == feature_key and detail_key in (result.details or ""):
                return workaround
        
        return None

    def generate_detailed_report(self):
        """Generate detailed JSON report"""
        report_path = Path("/workspaces/clear_htmx/reports/browser_compatibility_detailed.json")
        
        report_data = {
            "generated": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(self.results),
                "passed": sum(1 for r in self.results if r.status == TestStatus.PASSED),
                "failed": sum(1 for r in self.results if r.status == TestStatus.FAILED),
                "warnings": sum(1 for r in self.results if r.status == TestStatus.WARNING)
            },
            "results": [asdict(r) for r in self.results],
            "browsers_tested": list(set(f"{r.browser.name} {r.browser.version}" for r in self.results)),
            "features_tested": list(set(r.feature for r in self.results))
        }
        
        with open(report_path, "w") as f:
            json.dump(report_data, f, indent=2, default=str)


async def main():
    """Main entry point"""
    tester = BrowserCompatibilityTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())