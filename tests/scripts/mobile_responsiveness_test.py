#!/usr/bin/env python3
"""
Mobile Responsiveness Testing Script for CLEAR Platform
Tests various device viewports, orientations, and touch interactions
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from playwright.async_api import async_playwright, <PERSON>, Browser

# Device configurations for testing
MOBILE_DEVICES = {
    "iPhone_12_Pro": {
        "viewport": {"width": 390, "height": 844},
        "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15",
        "has_touch": True,
        "is_mobile": True
    },
    "iPhone_SE": {
        "viewport": {"width": 375, "height": 667},
        "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15",
        "has_touch": True,
        "is_mobile": True
    },
    "iPad_Pro": {
        "viewport": {"width": 1024, "height": 1366},
        "user_agent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15",
        "has_touch": True,
        "is_mobile": True
    },
    "iPad_Mini": {
        "viewport": {"width": 768, "height": 1024},
        "user_agent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15",
        "has_touch": True,
        "is_mobile": True
    },
    "Pixel_5": {
        "viewport": {"width": 393, "height": 851},
        "user_agent": "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36",
        "has_touch": True,
        "is_mobile": True
    },
    "Galaxy_S21": {
        "viewport": {"width": 360, "height": 800},
        "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36",
        "has_touch": True,
        "is_mobile": True
    },
    "Galaxy_Tab_S7": {
        "viewport": {"width": 753, "height": 1205},
        "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36",
        "has_touch": True,
        "is_mobile": True
    }
}

# Test URLs
BASE_URL = "http://localhost:8000"
TEST_URLS = {
    "login": "/login/",
    "dashboard": "/",
    "map": "/projects/mapping/",
    "3d_map": "/projects/mapping/3d/",
    "projects": "/projects/",
    "messages": "/messaging/messages/",
    "documents": "/documents/"
}

class MobileResponsivenessTest:
    def __init__(self):
        self.results = {
            "test_date": datetime.now().isoformat(),
            "devices": {},
            "performance_metrics": {},
            "touch_interactions": {},
            "layout_issues": [],
            "recommendations": []
        }
        
    async def test_device(self, browser: Browser, device_name: str, device_config: Dict[str, Any]):
        """Test a specific device configuration"""
        print(f"\n📱 Testing {device_name}...")
        
        context = await browser.new_context(
            viewport=device_config["viewport"],
            user_agent=device_config["user_agent"],
            has_touch=device_config["has_touch"],
            is_mobile=device_config["is_mobile"]
        )
        
        page = await context.new_page()  # type: ignore
        device_results = {
            "portrait": {},
            "landscape": {},
            "touch_tests": {},
            "performance": {}
        }
        
        # Test both orientations
        for orientation in ["portrait", "landscape"]:
            if orientation == "landscape":
                # Swap width and height for landscape
                viewport = {
                    "width": device_config["viewport"]["height"],
                    "height": device_config["viewport"]["width"]
                }
                await context.set_viewport_size(viewport)  # type: ignore
            
            print(f"  Testing {orientation} orientation...")
            device_results[orientation] = await self.test_pages_on_device(page, device_name, orientation)
        
        # Test touch interactions on map page
        device_results["touch_tests"] = await self.test_touch_interactions(page, device_name)
        
        # Test performance
        device_results["performance"] = await self.test_performance(page, device_name)
        
        self.results["devices"][device_name] = device_results
        await context.close()  # type: ignore
        
    async def test_pages_on_device(self, page: Page, device_name: str, orientation: str) -> Dict[str, Any]:
        """Test various pages on a specific device"""
        results = {}
        
        for page_name, url in TEST_URLS.items():
            try:
                # Navigate to page
                await page.goto(BASE_URL + url, wait_until="networkidle")
                
                # Take screenshot
                screenshot_dir = f"screenshots/mobile/{device_name}/{orientation}"
                os.makedirs(screenshot_dir, exist_ok=True)
                await page.screenshot(path=f"{screenshot_dir}/{page_name}.png", full_page=True)
                
                # Check for responsive issues
                issues = await self.check_layout_issues(page, device_name, page_name, orientation)
                
                results[page_name] = {
                    "url": url,
                    "status": "success",
                    "issues": issues,
                    "screenshot": f"{screenshot_dir}/{page_name}.png"
                }
                
            except Exception as e:
                results[page_name] = {
                    "url": url,
                    "status": "error",
                    "error": str(e)
                }
                
        return results
        
    async def check_layout_issues(self, page: Page, device: str, page_name: str, orientation: str) -> List[str]:
        """Check for common responsive layout issues"""
        issues = []
        
        # Check for horizontal overflow
        horizontal_overflow = await page.evaluate("""
            () => {
                const body = document.body;
                const html = document.documentElement;
                return body.scrollWidth > window.innerWidth || 
                       html.scrollWidth > window.innerWidth;
            }
        """)
        
        if horizontal_overflow:
            issues.append("Horizontal overflow detected")
            
        # Check for overlapping elements
        overlapping = await page.evaluate("""
            () => {
                const elements = document.querySelectorAll('button, a, input, select, textarea');
                const overlapping = [];
                
                for (let i = 0; i < elements.length; i++) {
                    const rect1 = elements[i].getBoundingClientRect();
                    for (let j = i + 1; j < elements.length; j++) {
                        const rect2 = elements[j].getBoundingClientRect();
                        
                        if (!(rect1.right < rect2.left || 
                              rect2.right < rect1.left || 
                              rect1.bottom < rect2.top || 
                              rect2.bottom < rect1.top)) {
                            overlapping.push({
                                elem1: elements[i].tagName + (elements[i].id ? '#' + elements[i].id : ''),
                                elem2: elements[j].tagName + (elements[j].id ? '#' + elements[j].id : '')
                            });
                        }
                    }
                }
                return overlapping.length > 0;
            }
        """)
        
        if overlapping:
            issues.append("Overlapping interactive elements detected")
            
        # Check touch target sizes
        small_targets = await page.evaluate("""
            () => {
                const elements = document.querySelectorAll('button, a, input, select, textarea, [onclick]');
                const small = [];
                
                for (const elem of elements) {
                    const rect = elem.getBoundingClientRect();
                    if (rect.width < 44 || rect.height < 44) {
                        small.push({
                            element: elem.tagName + (elem.id ? '#' + elem.id : ''),
                            width: rect.width,
                            height: rect.height
                        });
                    }
                }
                return small;
            }
        """)
        
        if small_targets:
            issues.append(f"Found {len(small_targets)} touch targets smaller than 44x44px")
            
        # Check text readability
        small_text = await page.evaluate("""
            () => {
                const elements = document.querySelectorAll('p, span, div, a, button');
                const small = [];
                
                for (const elem of elements) {
                    const style = window.getComputedStyle(elem);
                    const fontSize = parseInt(style.fontSize);
                    if (fontSize < 14 && elem.textContent.trim()) {
                        small.push({
                            element: elem.tagName,
                            fontSize: fontSize
                        });
                    }
                }
                return small.length;
            }
        """)
        
        if small_text > 0:
            issues.append(f"Found {small_text} elements with text smaller than 14px")
            
        # Check viewport meta tag
        viewport_meta = await page.evaluate("""
            () => {
                const meta = document.querySelector('meta[name="viewport"]');
                return meta ? meta.content : null;
            }
        """)
        
        if not viewport_meta:
            issues.append("Missing viewport meta tag")
        elif "user-scalable=no" in viewport_meta:
            issues.append("Viewport prevents user scaling")
            
        return issues
        
    async def test_touch_interactions(self, page: Page, device_name: str) -> Dict[str, Any]:
        """Test touch-specific interactions on the map page"""
        results = {
            "pinch_zoom": False,
            "pan": False,
            "double_tap": False,
            "long_press": False,
            "swipe": False,
            "drawing": False
        }
        
        try:
            # Navigate to map page
            await page.goto(BASE_URL + TEST_URLS["map"], wait_until="networkidle")
            
            # Wait for map to load
            await page.wait_for_selector("#map", timeout=5000)
            
            # Test pinch zoom
            await page.evaluate("""
                () => {
                    const map = document.querySelector('#map');
                    if (map) {
                        const event = new TouchEvent('touchstart', {
                            touches: [
                                new Touch({identifier: 1, target: map, clientX: 100, clientY: 100}),
                                new Touch({identifier: 2, target: map, clientX: 200, clientY: 200})
                            ]
                        });
                        map.dispatchEvent(event);
                    }
                }
            """)
            results["pinch_zoom"] = True
            
            # Test pan
            await page.evaluate("""
                () => {
                    const map = document.querySelector('#map');
                    if (map) {
                        const start = new TouchEvent('touchstart', {
                            touches: [new Touch({identifier: 1, target: map, clientX: 100, clientY: 100})]
                        });
                        const move = new TouchEvent('touchmove', {
                            touches: [new Touch({identifier: 1, target: map, clientX: 150, clientY: 150})]
                        });
                        map.dispatchEvent(start);
                        map.dispatchEvent(move);
                    }
                }
            """)
            results["pan"] = True
            
            # Test double tap
            await page.tap("#map", {"modifiers": []})
            await page.wait_for_timeout(100)
            await page.tap("#map", {"modifiers": []})
            results["double_tap"] = True
            
            # Test drawing on touch
            if await page.locator("[data-tool='draw']").count() > 0:
                await page.tap("[data-tool='draw']")
                
                # Simulate drawing gesture
                await page.evaluate("""
                    () => {
                        const map = document.querySelector('#map');
                        if (map) {
                            const start = new TouchEvent('touchstart', {
                                touches: [new Touch({identifier: 1, target: map, clientX: 100, clientY: 100})]
                            });
                            const move1 = new TouchEvent('touchmove', {
                                touches: [new Touch({identifier: 1, target: map, clientX: 200, clientY: 100})]
                            });
                            const move2 = new TouchEvent('touchmove', {
                                touches: [new Touch({identifier: 1, target: map, clientX: 200, clientY: 200})]
                            });
                            const end = new TouchEvent('touchend', {
                                changedTouches: [new Touch({identifier: 1, target: map, clientX: 200, clientY: 200})]
                            });
                            
                            map.dispatchEvent(start);
                            map.dispatchEvent(move1);
                            map.dispatchEvent(move2);
                            map.dispatchEvent(end);
                        }
                    }
                """)
                results["drawing"] = True
                
        except Exception as e:
            print(f"    Error testing touch interactions: {e}")
            
        return results
        
    async def test_performance(self, page: Page, device_name: str) -> Dict[str, Any]:
        """Test performance on mobile device"""
        metrics = {}
        
        try:
            # Enable performance tracking
            await page.goto(BASE_URL + TEST_URLS["map"], wait_until="networkidle")
            
            # Get performance metrics
            performance_data = await page.evaluate("""
                () => {
                    const perf = window.performance;
                    const timing = perf.timing;
                    const navigation = perf.getEntriesByType('navigation')[0];
                    
                    return {
                        loadTime: timing.loadEventEnd - timing.navigationStart,
                        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                        firstPaint: perf.getEntriesByName('first-paint')[0]?.startTime || 0,
                        firstContentfulPaint: perf.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
                        resources: perf.getEntriesByType('resource').length,
                        transferSize: navigation?.transferSize || 0,
                        decodedBodySize: navigation?.decodedBodySize || 0
                    };
                }
            """)
            
            metrics = {
                "load_time_ms": performance_data.get("loadTime", 0),
                "dom_content_loaded_ms": performance_data.get("domContentLoaded", 0),
                "first_paint_ms": performance_data.get("firstPaint", 0),
                "first_contentful_paint_ms": performance_data.get("firstContentfulPaint", 0),
                "resource_count": performance_data.get("resources", 0),
                "transfer_size_kb": round(performance_data.get("transferSize", 0) / 1024, 2),
                "decoded_size_kb": round(performance_data.get("decodedBodySize", 0) / 1024, 2)
            }
            
            # Check for slow loading
            if metrics["load_time_ms"] > 3000:
                self.results["layout_issues"].append(f"{device_name}: Page load time exceeds 3 seconds")
                
            # Check bundle size
            if metrics["transfer_size_kb"] > 500:
                self.results["layout_issues"].append(f"{device_name}: Large transfer size ({metrics['transfer_size_kb']}KB)")
                
        except Exception as e:
            print(f"    Error testing performance: {e}")
            
        return metrics
        
    async def run_tests(self):
        """Run all mobile responsiveness tests"""
        print("🚀 Starting Mobile Responsiveness Tests")
        print("=" * 50)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            
            # Test each device
            for device_name, device_config in MOBILE_DEVICES.items():
                await self.test_device(browser, device_name, device_config)
                
            await browser.close()
            
        # Generate recommendations
        self.generate_recommendations()
        
        # Save results
        results_path = "test_results/mobile_responsiveness_results.json"
        os.makedirs(os.path.dirname(results_path), exist_ok=True)
        
        with open(results_path, "w") as f:
            json.dump(self.results, f, indent=2)
            
        print(f"\n✅ Test results saved to {results_path}")
        
        # Print summary
        self.print_summary()
        
    def generate_recommendations(self):
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check for common issues across devices
        touch_target_issues = 0
        overflow_issues = 0
        performance_issues = 0
        
        for device, results in self.results["devices"].items():
            for orientation in ["portrait", "landscape"]:
                if orientation in results:
                    for page, page_results in results[orientation].items():
                        if isinstance(page_results, dict) and "issues" in page_results:
                            for issue in page_results["issues"]:
                                if "touch targets" in issue:
                                    touch_target_issues += 1
                                if "overflow" in issue:
                                    overflow_issues += 1
                                    
            if "performance" in results:
                perf = results["performance"]
                if perf.get("load_time_ms", 0) > 3000:
                    performance_issues += 1
                    
        # Generate recommendations
        if touch_target_issues > 3:
            recommendations.append({
                "priority": "high",
                "category": "usability",
                "issue": "Small touch targets",
                "recommendation": "Increase button and interactive element sizes to minimum 44x44px"
            })
            
        if overflow_issues > 2:
            recommendations.append({
                "priority": "high",
                "category": "layout",
                "issue": "Horizontal overflow on mobile",
                "recommendation": "Review container widths and implement proper responsive breakpoints"
            })
            
        if performance_issues > 2:
            recommendations.append({
                "priority": "medium",
                "category": "performance",
                "issue": "Slow page loads on mobile",
                "recommendation": "Optimize bundle size, implement lazy loading, and reduce initial payload"
            })
            
        # Check touch interaction support
        touch_support_count = 0
        for device, results in self.results["devices"].items():
            if "touch_tests" in results:
                touch_support_count += sum(1 for v in results["touch_tests"].values() if v)
                
        if touch_support_count < len(MOBILE_DEVICES) * 3:
            recommendations.append({
                "priority": "high",
                "category": "functionality",
                "issue": "Limited touch gesture support",
                "recommendation": "Implement proper touch event handlers for map interactions"
            })
            
        self.results["recommendations"] = recommendations
        
    def print_summary(self):
        """Print test summary"""
        print("\n📊 Mobile Responsiveness Test Summary")
        print("=" * 50)
        
        # Device summary
        print(f"\n✅ Tested {len(MOBILE_DEVICES)} devices")
        for device in MOBILE_DEVICES.keys():
            print(f"  • {device}")
            
        # Issue summary
        total_issues = len(self.results["layout_issues"])
        print(f"\n⚠️  Found {total_issues} layout issues")
        
        # Recommendation summary
        recommendations = self.results["recommendations"]
        if recommendations:
            print(f"\n💡 {len(recommendations)} Recommendations:")
            for rec in recommendations:
                print(f"  • [{rec['priority'].upper()}] {rec['issue']}")
                print(f"    → {rec['recommendation']}")
                
        # Performance summary
        print("\n⚡ Performance Metrics:")
        avg_load_time = 0
        count = 0
        
        for device, results in self.results["devices"].items():
            if "performance" in results and "load_time_ms" in results["performance"]:
                avg_load_time += results["performance"]["load_time_ms"]
                count += 1
                
        if count > 0:
            avg_load_time /= count
            print(f"  • Average load time: {avg_load_time:.0f}ms")
            
        print("\n✅ Test complete!")

async def main():
    """Main test runner"""
    tester = MobileResponsivenessTest()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())