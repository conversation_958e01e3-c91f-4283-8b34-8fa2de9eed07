"""
Main pytest configuration for CLEAR unified test suite.
"""

import os
import sys
import django
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.dev_settings')
django.setup()

# Import test utilities

# Configure pytest
def pytest_configure(config):
    """Configure pytest for Django testing."""
    config.addinivalue_line(
        "markers", "django_db: mark test to use Django database"
    )
    config.addinivalue_line(
        "markers", "htmx: mark test as HTMX functionality test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "security: mark test as security test"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers."""
    for item in items:
        # Add django_db marker to Django tests
        if 'test_' in item.nodeid and any(x in item.nodeid for x in ['core/', 'e2e/python/']):
            item.add_marker('django_db')
            
        # Add specific markers based on test location
        if 'core/htmx/' in item.nodeid:
            item.add_marker('htmx')
        elif 'e2e/' in item.nodeid:
            item.add_marker('e2e')
        elif 'core/performance/' in item.nodeid:
            item.add_marker('performance')
        elif 'core/security/' in item.nodeid:
            item.add_marker('security')
