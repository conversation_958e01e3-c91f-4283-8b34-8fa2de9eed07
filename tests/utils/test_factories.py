"""
Test Factories for CLEAR Platform

Provides factory classes for creating test data consistently across test suites.
"""

# Factory Boy imports with graceful fallback
try:
    import factory  # type: ignore
    from factory.django import DjangoModelFactory  # type: ignore
except ImportError:
    # Factory Boy not available - skip factory definitions
    factory = None  # type: ignore
    DjangoModelFactory = object  # type: ignore
from django.utils import timezone
from datetime import timedelta
import uuid

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage, Conversation,
    Task, Comment, TimeEntry, Stakeholder, OrganizationMember, ProjectMember
)


class OrganizationFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Organization instances."""
    
    class Meta:
        model = Organization
        
    name = factory.Sequence(lambda n: f"Test Organization {n}")  # type: ignore
    description = factory.Faker('text', max_nb_chars=200)
    website = factory.Faker('url')
    email = factory.Faker('email')
    phone = factory.Faker('phone_number')
    city = factory.Faker('city')
    state = factory.Faker('state')
    country = "United States"
    timezone = "America/New_York"


class UserFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating User instances."""
    
    class Meta:
        model = User
        
    username = factory.Sequence(lambda n: f"testuser{n}")  # type: ignore
    email = factory.Faker('email')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    organization = factory.SubFactory(OrganizationFactory)
    role = "user"
    is_active = True
    
    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        if not create:
            return
        password = extracted or 'testpass123'
        self.set_password(password)
        self.save()


class AdminUserFactory(UserFactory):  # type: ignore
    """Factory for creating admin User instances."""
    
    role = "admin"
    is_staff = True
    is_superuser = True


class ProjectFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Project instances."""
    
    class Meta:
        model = Project
        
    id = factory.LazyFunction(lambda: f"test-project-{uuid.uuid4().hex[:8]}")
    name = factory.Sequence(lambda n: f"Test Project {n}")  # type: ignore
    client = factory.Faker('company')
    description = factory.Faker('text', max_nb_chars=500)
    organization = factory.SubFactory(OrganizationFactory)
    start_date = factory.LazyFunction(lambda: timezone.now().date())
    end_date = factory.LazyFunction(lambda: (timezone.now() + timedelta(days=90)).date())


class DocumentFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Document instances."""
    
    class Meta:
        model = Document
        
    name = factory.Sequence(lambda n: f"Test Document {n}")  # type: ignore
    file_path = factory.LazyAttribute(lambda obj: f"/test/documents/{obj.name.lower().replace(' ', '_')}.pdf")  # type: ignore
    file_type = "pdf"
    mime_type = "application/pdf"
    file_size = factory.Faker('random_int', min=1024, max=10485760)  # 1KB to 10MB
    organization = factory.SubFactory(OrganizationFactory)
    uploaded_by = factory.SubFactory(UserFactory)
    project = factory.SubFactory(ProjectFactory)
    current_version = 1
    is_locked = False
    is_archived = False
    is_public = False
    tags = factory.LazyFunction(list)
    metadata = factory.LazyFunction(dict)


class TaskFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Task instances."""
    
    class Meta:
        model = Task
        
    project = factory.SubFactory(ProjectFactory)
    title = factory.Sequence(lambda n: f"Test Task {n}")  # type: ignore
    description = factory.Faker('text', max_nb_chars=300)
    status = "pending"
    priority = "medium"
    created_by = factory.SubFactory(UserFactory)
    assigned_to = factory.SubFactory(UserFactory)
    due_date = factory.LazyFunction(lambda: timezone.now() + timedelta(days=7))
    estimated_hours = factory.Faker('pydecimal', left_digits=2, right_digits=2, positive=True)
    tags = factory.LazyFunction(list)
    attachments = factory.LazyFunction(list)


class CompletedTaskFactory(TaskFactory):  # type: ignore
    """Factory for creating completed Task instances."""
    
    status = "completed"
    completion_date = factory.LazyFunction(timezone.now)
    actual_hours = factory.Faker('pydecimal', left_digits=2, right_digits=2, positive=True)


class ConversationFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Conversation instances."""
    
    class Meta:
        model = Conversation
        
    name = factory.Sequence(lambda n: f"Test Conversation {n}")  # type: ignore
    conversation_type = "group"
    created_by = factory.SubFactory(UserFactory)
    project = factory.SubFactory(ProjectFactory)
    is_active = True


class ChatMessageFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating ChatMessage instances."""
    
    class Meta:
        model = ChatMessage
        
    user = factory.SubFactory(UserFactory)
    conversation = factory.SubFactory(ConversationFactory)
    content = factory.Faker('text', max_nb_chars=500)
    channel = "general"
    project = factory.SubFactory(ProjectFactory)
    message_type = "text"
    is_urgent = False


class UrgentChatMessageFactory(ChatMessageFactory):  # type: ignore
    """Factory for creating urgent ChatMessage instances."""
    
    is_urgent = True
    content = factory.LazyAttribute(lambda obj: f"URGENT: {factory.Faker('text', max_nb_chars=400).generate()}")  # type: ignore


class CommentFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Comment instances."""
    
    class Meta:
        model = Comment
        
    content = factory.Faker('text', max_nb_chars=300)
    author = factory.SubFactory(UserFactory)
    commentable_type = "task"
    commentable_id = factory.LazyAttribute(lambda obj: str(uuid.uuid4()))  # type: ignore
    is_edited = False


class TimeEntryFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating TimeEntry instances."""
    
    class Meta:
        model = TimeEntry
        
    user = factory.SubFactory(UserFactory)
    project = factory.SubFactory(ProjectFactory)
    description = factory.Faker('text', max_nb_chars=200)
    start_time = factory.LazyFunction(lambda: timezone.now() - timedelta(hours=4))
    end_time = factory.LazyFunction(timezone.now)
    hours = factory.Faker('pydecimal', left_digits=1, right_digits=2, positive=True, max_value=8)


class StakeholderFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating Stakeholder instances."""
    
    class Meta:
        model = Stakeholder
        
    full_name = factory.Faker('name')
    email = factory.Faker('email')
    phone = factory.Faker('phone_number')
    contact_company = factory.Faker('company')
    role = factory.Faker('job')
    notes = factory.Faker('text', max_nb_chars=300)


class OrganizationMemberFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating OrganizationMember instances."""
    
    class Meta:
        model = OrganizationMember
        
    organization = factory.SubFactory(OrganizationFactory)
    user = factory.SubFactory(UserFactory)
    role = "member"
    is_active = True
    permissions = factory.LazyFunction(list)


class AdminOrganizationMemberFactory(OrganizationMemberFactory):  # type: ignore
    """Factory for creating admin OrganizationMember instances."""
    
    role = "admin"
    user = factory.SubFactory(AdminUserFactory)


class ProjectMemberFactory(DjangoModelFactory):  # type: ignore
    """Factory for creating ProjectMember instances."""
    
    class Meta:
        model = ProjectMember
        
    project = factory.SubFactory(ProjectFactory)
    user = factory.SubFactory(UserFactory)
    role = "member"
    permissions = factory.LazyFunction(list)


class ProjectManagerFactory(ProjectMemberFactory):  # type: ignore
    """Factory for creating project manager ProjectMember instances."""
    
    role = "manager"
    user = factory.SubFactory(AdminUserFactory)


# Utility functions for creating test data sets

def create_test_organization_with_users(user_count=5):
    """Create an organization with multiple users."""
    org = OrganizationFactory()
    users = UserFactory.create_batch(user_count, organization=org)
    
    # Create organization memberships
    for user in users:
        OrganizationMemberFactory(organization=org, user=user)
    
    return org, users


def create_test_project_with_team(team_size=3):
    """Create a project with a team of users."""
    project = ProjectFactory()
    users = UserFactory.create_batch(team_size, organization=project.organization)
    
    # Create project memberships
    members = []
    for i, user in enumerate(users):
        role = "manager" if i == 0 else "member"
        member = ProjectMemberFactory(project=project, user=user, role=role)
        members.append(member)
    
    return project, users, members


def create_test_project_with_tasks(task_count=10):
    """Create a project with multiple tasks."""
    project = ProjectFactory()
    users = UserFactory.create_batch(3, organization=project.organization)
    
    tasks = []
    for i in range(task_count):
        if i < task_count // 2:
            task = TaskFactory(
                project=project,
                created_by=users[0],
                assigned_to=users[i % len(users)]
            )
        else:
            task = CompletedTaskFactory(
                project=project,
                created_by=users[0],
                assigned_to=users[i % len(users)]
            )
        tasks.append(task)
    
    return project, users, tasks


def create_test_conversation_with_messages(message_count=20):
    """Create a conversation with multiple messages."""
    conversation = ConversationFactory()
    users = UserFactory.create_batch(3, organization=conversation.project.organization)
    
    # Add users to conversation
    from CLEAR.models import ConversationMember
    for user in users:
        ConversationMember.objects.create(
            conversation=conversation,
            user=user
        )
    
    # Create messages
    messages = []
    for i in range(message_count):
        if i % 5 == 0:  # Every 5th message is urgent
            message = UrgentChatMessageFactory(
                conversation=conversation,
                user=users[i % len(users)],
                project=conversation.project
            )
        else:
            message = ChatMessageFactory(
                conversation=conversation,
                user=users[i % len(users)],
                project=conversation.project
            )
        messages.append(message)
    
    return conversation, users, messages
