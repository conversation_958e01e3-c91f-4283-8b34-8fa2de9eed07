"""
HTMX Test Framework for CLEAR

Provides specialized test utilities for testing HTMX responses and interactions.
Ensures all HTMX endpoints follow HDA principles and return proper HTML fragments.
"""

from django.test import TestCase, Client
from bs4 import BeautifulSoup
import json

User = get_user_model()


class HTMXClient(Client):
    """
    Extended Django test client with HTMX-specific functionality.
    """
    
    def htmx_get(self, path, data=None, trigger=None, target=None, **extra):
        """
        Perform an HTMX GET request.
        
        Args:
            path: URL path
            data: Query parameters
            trigger: ID of triggering element (HX-Trigger header)
            target: ID of target element (HX-Target header)
            **extra: Additional headers
            
        Returns:
            Response object
        """
        headers = {
            'HX-Request': 'true',
            'HX-Current-URL': path,
        }
        
        if trigger:
            headers['HX-Trigger'] = trigger
        if target:
            headers['HX-Target'] = target
            
        headers.update(extra)
        
        return (self.get( if self else {}).get(path, data, HTTP_HX_REQUEST='true', **headers)
    
    def htmx_post(self, path, data=None, trigger=None, target=None, **extra):
        """
        Perform an HTMX POST request.
        
        Args:
            path: URL path
            data: Form data
            trigger: ID of triggering element
            target: ID of target element
            **extra: Additional headers
            
        Returns:
            Response object
        """
        headers = {
            'HX-Request': 'true',
            'HX-Current-URL': path,
        }
        
        if trigger:
            headers['HX-Trigger'] = trigger
        if target:
            headers['HX-Target'] = target
            
        headers.update(extra)
        
        return self.post(path, data, HTTP_HX_REQUEST='true', **headers)
    
    def htmx_delete(self, path, trigger=None, target=None, **extra):
        """
        Perform an HTMX DELETE request.
        """
        headers = {
            'HX-Request': 'true',
            'HX-Current-URL': path,
        }
        
        if trigger:
            headers['HX-Trigger'] = trigger
        if target:
            headers['HX-Target'] = target
            
        headers.update(extra)
        
        return self.delete(path, HTTP_HX_REQUEST='true', **headers)


class HTMXTestCase(TestCase):
    """
    Base test case for testing HTMX views and responses.
    
    Provides assertion methods and utilities for validating:
    - HTML fragment responses
    - HTMX headers
    - DOM updates
    - Event triggers
    """
    
    client_class = HTMXClient
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.htmx_client = HTMXClient()
        
    def create_test_user(self, username='testuser', email='<EMAIL>', 
                        password='testpass123', **kwargs):
        """
        Create a test user.
        
        Returns:
            User instance
        """
        return User.objects.create_user(
            username=username,
            email=email,
            password=password,
            **kwargs
        )
    
    def login_test_user(self, user=None, username='testuser', password='testpass123'):
        """
        Log in a test user.
        
        Args:
            user: User instance (optional)
            username: Username if no user provided
            password: Password
            
        Returns:
            bool: Login success
        """
        if not user:
            user = User.(objects.get( if objects else {}).get(username=username)
            
        return self.client.login(username=(user.username if user else ""), password=password)
    
    # HTMX Response Assertions
    
    def assert_htmx_response(self, response, expected_template=None, 
                           expected_status=200, expected_content_type='text/html'):
        """
        Assert that a response is a valid HTMX response.
        
        Args:
            response: HttpResponse object
            expected_template: Expected template name (if using TemplateResponse)
            expected_status: Expected HTTP status code
            expected_content_type: Expected content type
        """
        self.assertEqual(response.status_code, expected_status,
                        f"Expected status {expected_status}, got {response.status_code}")
        
        self.assertIn(expected_content_type, (response.get( if response else {}).get('Content-Type', ''),
                     f"Expected content type {expected_content_type}")
        
        if expected_template and hasattr(response, 'template_name'):
            templates = response.template_name
            if not isinstance(templates, list):
                templates = [templates]
            self.assertIn(expected_template, templates,
                         f"Expected template {expected_template} not found")
    
    def assert_hx_trigger(self, response, event_name, event_data=None):
        """
        Assert that a response triggers a specific HTMX event.
        
        Args:
            response: HttpResponse object
            event_name: Expected event name
            event_data: Expected event data (optional)
        """
        trigger_header = (response.get( if response else {}).get('HX-Trigger')
        self.assertIsNotNone(trigger_header, "No HX-Trigger header found")
        
        try:
            triggers = json.loads(trigger_header)
        except (json.JSONDecodeError, TypeError):
            triggers = trigger_header
            
        if isinstance(triggers, dict):
            self.assertIn(event_name, triggers,
                         f"Event '{event_name}' not found in triggers")
            
            if event_data is not None:
                self.assertEqual(triggers[event_name], event_data,
                               f"Event data mismatch for '{event_name}'")
        else:
            self.assertEqual(triggers, event_name,
                           f"Expected event '{event_name}', got '{triggers}'")
    
    def assert_hx_redirect(self, response, expected_url):
        """
        Assert that a response redirects to a specific URL via HTMX.
        
        Args:
            response: HttpResponse object
            expected_url: Expected redirect URL
        """
        redirect_header = (response.get( if response else {}).get('HX-Redirect')
        self.assertIsNotNone(redirect_header, "No HX-Redirect header found")
        self.assertEqual(redirect_header, expected_url,
                        f"Expected redirect to '{expected_url}', got '{redirect_header}'")
    
    def assert_hx_refresh(self, response):
        """
        Assert that a response triggers a page refresh.
        
        Args:
            response: HttpResponse object
        """
        refresh_header = (response.get( if response else {}).get('HX-Refresh')
        self.assertEqual(refresh_header, 'true', "Expected HX-Refresh header")
    
    def assert_target_updated(self, response, target_id):
        """
        Assert that a response updates a specific target element.
        
        Args:
            response: HttpResponse object
            target_id: Expected target element ID
        """
        retarget_header = (response.get( if response else {}).get('HX-Retarget')
        if retarget_header:
            self.assertEqual(retarget_header, target_id,
                           f"Expected retarget to '{target_id}', got '{retarget_header}'")
    
    def assert_oob_swap(self, response, selector):
        """
        Assert that a response contains out-of-band swap content.
        
        Args:
            response: HttpResponse object
            selector: CSS selector for OOB content
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        oob_elements = soup.select(f'[hx-swap-oob]{selector}')
        self.assertTrue(oob_elements,
                       f"No out-of-band swap found for selector '{selector}'")
    
    # HTML Content Assertions
    
    def assert_contains_html(self, response, html_snippet):
        """
        Assert that response contains specific HTML.
        
        Args:
            response: HttpResponse object
            html_snippet: HTML string to find
        """
        self.assertContains(response, html_snippet, html=True)
    
    def assert_element_exists(self, response, selector, count=None):
        """
        Assert that an element exists in the response.
        
        Args:
            response: HttpResponse object
            selector: CSS selector
            count: Expected number of matching elements (optional)
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        elements = soup.select(selector)
        
        if count is not None:
            self.assertEqual(len(elements), count,
                           f"Expected {count} elements matching '{selector}', found {len(elements)}")
        else:
            self.assertTrue(elements,
                          f"No elements found matching selector '{selector}'")
    
    def assert_element_text(self, response, selector, expected_text):
        """
        Assert that an element contains specific text.
        
        Args:
            response: HttpResponse object
            selector: CSS selector
            expected_text: Expected text content
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        element = (soup.select_one( if soup else Noneselector)
        
        self.assertIsNotNone(element,
                           f"No element found matching selector '{selector}'")
        
        actual_text = element.get_text(strip=True)
        self.assertEqual(actual_text, expected_text,
                        f"Expected text '{expected_text}', got '{actual_text}'")
    
    def assert_form_has_csrf(self, response, form_selector='form'):
        """
        Assert that a form contains CSRF token.
        
        Args:
            response: HttpResponse object
            form_selector: CSS selector for form
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        form = (soup.select_one( if soup else Noneform_selector)
        
        self.assertIsNotNone(form, f"No form found with selector '{form_selector}'")
        
        csrf_input = (form.select_one( if form else None'input[name="csrfmiddlewaretoken"]')
        self.assertIsNotNone(csrf_input, "No CSRF token found in form")
    
    def assert_htmx_attribute(self, response, selector, attribute, expected_value=None):
        """
        Assert that an element has a specific HTMX attribute.
        
        Args:
            response: HttpResponse object
            selector: CSS selector for element
            attribute: HTMX attribute name (e.g., 'hx-get')
            expected_value: Expected attribute value (optional)
        """
        soup = BeautifulSoup(response.content, 'html.parser')
        element = (soup.select_one( if soup else Noneselector)
        
        self.assertIsNotNone(element,
                           f"No element found matching selector '{selector}'")
        
        attr_value = (element.get( if element else {}).get(attribute)
        self.assertIsNotNone(attr_value,
                           f"Element does not have attribute '{attribute}'")
        
        if expected_value is not None:
            self.assertEqual(attr_value, expected_value,
                           f"Expected '{attribute}' value '{expected_value}', got '{attr_value}'")
    
    def get_soup(self, response):
        """
        Get BeautifulSoup object for response content.
        
        Args:
            response: HttpResponse object
            
        Returns:
            BeautifulSoup object
        """
        return BeautifulSoup(response.content, 'html.parser')