#!/usr/bin/env python3
"""
Comprehensive Test Runner for CLEAR

Executes all test suites with coverage reporting, performance metrics,
and detailed reporting. Supports running specific test categories
and generating comprehensive reports.
"""

import os
import sys
import time
import json
import subprocess
import argparse
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import django
from django.conf import settings
from django.test.utils import get_runner
from django.core.management import execute_from_command_line

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'clear_htmx.settings')
django.setup()


class ComprehensiveTestRunner:
    """Comprehensive test runner with coverage and performance metrics."""
    
    def __init__(self, verbosity=2, coverage=True, performance=True, parallel=False):
        self.verbosity = verbosity
        self.coverage = coverage
        self.performance = performance
        self.parallel = parallel
        self.results = {}
        self.start_time = None
        
    def run_tests(self, test_categories=None):
        """Run comprehensive test suite."""
        self.start_time = time.time()
        
        print("🚀 Starting Comprehensive Test Suite for CLEAR")
        print("=" * 60)
        
        # Define test categories
        all_categories = {
            'htmx': {
                'description': 'HTMX Functionality Tests',
                'pattern': 'test_htmx_*.py',
                'files': [
                    'test_htmx_auth.py',
                    'test_htmx_projects.py',
                    'test_htmx_messaging.py',
                    'test_htmx_mapping.py',
                    'test_htmx_documents.py',
                    'test_htmx_integration.py',
                    'test_htmx_coverage.py',
                    'test_htmx_framework.py'
                ]
            },
            'services': {
                'description': 'Service Layer Tests',
                'pattern': 'test_services.py',
                'files': ['test_services.py']
            },
            'models': {
                'description': 'Model Tests',
                'pattern': 'test_models_comprehensive.py',
                'files': ['test_models_comprehensive.py']
            },
            'api': {
                'description': 'API Endpoint Tests',
                'pattern': 'test_api_endpoints.py',
                'files': ['test_api_endpoints.py']
            },
            'performance': {
                'description': 'Performance Tests',
                'pattern': 'test_performance.py',
                'files': ['test_performance.py']
            },
            'security': {
                'description': 'Security Tests',
                'pattern': 'test_security.py',
                'files': ['test_security.py']
            },
            'integration': {
                'description': 'Integration Tests',
                'pattern': 'test_integration.py',
                'files': ['test_integration.py']
            },
            'views': {
                'description': 'View Tests',
                'pattern': 'test_views.py',
                'files': ['test_views.py']
            },
            'websockets': {
                'description': 'WebSocket Tests',
                'pattern': 'test_websockets.py',
                'files': ['test_websockets.py']
            }
        }
        
        # Filter categories if specified
        if test_categories:
            filtered_categories = {k: v for k, v in all_categories.items() 
                                 if k in test_categories}
        else:
            filtered_categories = all_categories
            
        # Run tests for each category
        for category, config in filtered_categories.items():
            print(f"\n📋 Running {config['description']}")
            print("-" * 40)
            
            try:
                result = self._run_category(category, config)
                self.results[category] = result
            except Exception as e:
                print(f"❌ Error running {category} tests: {e}")
                self.results[category] = {
                    'success': False,
                    'error': str(e),
                    'tests_run': 0,
                    'tests_failed': 0,
                    'tests_skipped': 0,
                    'execution_time': 0
                }
                
        # Generate comprehensive report
        self._generate_report()
        
    def _run_category(self, category, config):
        """Run tests for a specific category."""
        start_time = time.time()
        
        # Build test command
        test_files = ' '.join([f'CLEAR.tests.{file[:-3]}' for file in config['files']])
        
        if self.coverage:
            cmd = [
                'coverage', 'run', '--source=CLEAR',
                'manage.py', 'test', test_files,
                '--settings=clear_htmx.dev_settings',
                f'--verbosity={self.verbosity}'
            ]
        else:
            cmd = [
                'python', 'manage.py', 'test', test_files,
                '--settings=clear_htmx.dev_settings',
                f'--verbosity={self.verbosity}'
            ]
            
        if self.parallel:
            cmd.append('--parallel')
            
        # Execute tests
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        execution_time = time.time() - start_time
        
        # Parse results
        success = result.returncode == 0
        output = result.stdout
        error = result.stderr
        
        # Extract test statistics
        tests_run = 0
        tests_failed = 0
        tests_skipped = 0
        
        if success:
            # Parse Django test output
            for line in output.split('\n'):
                if 'Ran' in line and 'test' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'Ran':
                            tests_run = int(parts[i + 1])
                        elif part == 'failed':
                            tests_failed = int(parts[i - 1])
                        elif part == 'skipped':
                            tests_skipped = int(parts[i - 1])
                            
        return {
            'success': success,
            'tests_run': tests_run,
            'tests_failed': tests_failed,
            'tests_skipped': tests_skipped,
            'execution_time': execution_time,
            'output': output,
            'error': error
        }
        
    def _generate_report(self):
        """Generate comprehensive test report."""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # Summary statistics
        total_tests = sum(r.get('tests_run', 0) for r in self.results.values())
        total_failed = sum(r.get('tests_failed', 0) for r in self.results.values())
        total_skipped = sum(r.get('tests_skipped', 0) for r in self.results.values())
        successful_categories = sum(1 for r in self.results.values() if r.get('success', False))
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Test Categories: {len(self.results)}")
        print(f"   Successful Categories: {successful_categories}")
        print(f"   Total Tests Run: {total_tests}")
        print(f"   Total Tests Failed: {total_failed}")
        print(f"   Total Tests Skipped: {total_skipped}")
        print(f"   Total Execution Time: {total_time:.2f} seconds")
        
        if total_tests > 0:
            success_rate = ((total_tests - total_failed) / total_tests) * 100
            print(f"   Success Rate: {success_rate:.1f}%")
            
        # Category breakdown
        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, result in self.results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            tests_run = result.get('tests_run', 0)
            tests_failed = result.get('tests_failed', 0)
            execution_time = result.get('execution_time', 0)
            
            print(f"   {category.upper():<15} {status:<8} "
                  f"{tests_run:>3} tests, {tests_failed:>2} failed, "
                  f"{execution_time:>6.2f}s")
                  
        # Performance metrics
        if self.performance:
            self._generate_performance_report()
            
        # Coverage report
        if self.coverage:
            self._generate_coverage_report()
            
        # Save detailed report
        self._save_detailed_report()
        
    def _generate_performance_report(self):
        """Generate performance metrics report."""
        print(f"\n⚡ PERFORMANCE METRICS:")
        
        # Calculate performance statistics
        execution_times = [r.get('execution_time', 0) for r in self.results.values()]
        if execution_times:
            avg_time = sum(execution_times) / len(execution_times)
            max_time = max(execution_times)
            min_time = min(execution_times)
            
            print(f"   Average Execution Time: {avg_time:.2f} seconds")
            print(f"   Fastest Category: {min_time:.2f} seconds")
            print(f"   Slowest Category: {max_time:.2f} seconds")
            
        # Performance recommendations
        print(f"\n💡 PERFORMANCE RECOMMENDATIONS:")
        slow_categories = [(k, v.get('execution_time', 0)) 
                          for k, v in self.results.items() 
                          if v.get('execution_time', 0) > 10]
        
        if slow_categories:
            print("   Slow test categories (consider optimization):")
            for category, time_taken in sorted(slow_categories, key=lambda x: x[1], reverse=True):  # type: ignore
                print(f"     - {category}: {time_taken:.2f}s")
        else:
            print("   All test categories are performing well!")
            
    def _generate_coverage_report(self):
        """Generate coverage report."""
        print(f"\n📊 COVERAGE REPORT:")
        
        try:
            # Run coverage report
            result = subprocess.run(['coverage', 'report', '--include=CLEAR/*'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("   Code Coverage Summary:")
                lines = result.stdout.split('\n')
                for line in lines[2:]:  # Skip header lines
                    if line.strip() and 'CLEAR' in line:
                        print(f"     {line}")
            else:
                print("   Coverage report generation failed")
                
        except Exception as e:
            print(f"   Error generating coverage report: {e}")
            
    def _save_detailed_report(self):
        """Save detailed test report to file."""
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_execution_time': time.time() - self.start_time,
            'results': self.results,
            'summary': {
                'total_tests': sum(r.get('tests_run', 0) for r in self.results.values()),
                'total_failed': sum(r.get('tests_failed', 0) for r in self.results.values()),
                'total_skipped': sum(r.get('tests_skipped', 0) for r in self.results.values()),
                'successful_categories': sum(1 for r in self.results.values() if r.get('success', False))
            }
        }
        
        # Save JSON report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
            
        print(f"\n💾 Detailed report saved to: {report_file}")


def main():
    """Main function to run comprehensive tests."""
    parser = argparse.ArgumentParser(description='Run comprehensive tests for CLEAR')
    parser.add_argument('--categories', nargs='+', 
                       help='Specific test categories to run')
    parser.add_argument('--no-coverage', action='store_true',
                       help='Disable coverage reporting')
    parser.add_argument('--no-performance', action='store_true',
                       help='Disable performance metrics')
    parser.add_argument('--parallel', action='store_true',
                       help='Run tests in parallel')
    parser.add_argument('--verbosity', type=int, default=2,
                       help='Test verbosity level (0-3)')
    
    args = parser.parse_args()
    
    # Create test runner
    runner = ComprehensiveTestRunner(
        verbosity=args.verbosity,
        coverage=not args.no_coverage,
        performance=not args.no_performance,
        parallel=args.parallel
    )
    
    # Run tests
    runner.run_tests(args.categories)


if __name__ == '__main__':
    main() 