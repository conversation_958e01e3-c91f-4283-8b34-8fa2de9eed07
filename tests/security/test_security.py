"""
Security Tests for CLEAR Platform

Tests authentication, authorization, data protection, and security features.
"""

from django.test import TestCase, Client, override_settings
from django.contrib.auth import authenticate
from django.urls import reverse
from django.utils import timezone
import json

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage,
    Task, OrganizationMember, ProjectMember
)


class AuthenticationSecurityTests(TestCase):
    """Test authentication security features."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = Client()
        self.org = Organization.objects.create(name="Security Test Org")
        
        self.user = User.objects.create_user(
            username='securityuser',
            email='<EMAIL>',
            password='SecurePass123!',
            organization=self.org
        )
        
    def test_password_strength_requirements(self):
        """Test password strength validation."""
        # Test weak passwords
        weak_passwords = [
            'password',
            '123456',
            'abc123',
            'password123'
        ]
        
        for weak_password in weak_passwords:
            with self.assertRaises(Exception):
                User.objects.create_user(
                    username=f'weakuser_{weak_password}',
                    email=f'weak_{weak_password}@example.com',
                    password=weak_password
                )
                
    def test_user_authentication(self):
        """Test user authentication process."""
        # Test valid authentication
        user = authenticate(username='securityuser', password='SecurePass123!')
        self.assertIsNotNone(user)
        self.assertEqual((user.username if user else ""), 'securityuser')
        
        # Test invalid authentication
        invalid_user = authenticate(username='securityuser', password='wrongpassword')
        self.assertIsNone(invalid_user)
        
    def test_login_attempt_tracking(self):
        """Test login attempt tracking for security."""
        # Test successful login
        response = self.client.post('/login/', {
            'username': 'securityuser',
            'password': 'SecurePass123!'
        })
        
        # Test failed login attempts
        for i in range(3):
            response = self.client.post('/login/', {
                'username': 'securityuser',
                'password': 'wrongpassword'
            })
            
    def test_session_security(self):
        """Test session security features."""
        # Login user
        self.client.login(username='securityuser', password='SecurePass123!')
        
        # Check session exists
        session = self.client.session
        self.assertIsNotNone(session.session_key)
        
        # Test session timeout (would need custom middleware)
        # This is a placeholder for session timeout testing
        
    def test_password_reset_security(self):
        """Test password reset security."""
        # Test password reset token generation
        from django.contrib.auth.tokens import default_token_generator
        token = default_token_generator.make_token(self.user)
        self.assertIsNotNone(token)
        
        # Test token validation
        is_valid = default_token_generator.check_token(self.user, token)
        self.assertTrue(is_valid)


class AuthorizationSecurityTests(TestCase):
    """Test authorization and permission security."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = Client()
        self.org = Organization.objects.create(name="Auth Test Org")
        
        # Create users with different roles
        self.admin_user = User.objects.create_user(
            username='adminuser',
            email='<EMAIL>',
            password='AdminPass123!',
            organization=self.org,
            role='admin'
        )
        
        self.regular_user = User.objects.create_user(
            username='regularuser',
            email='<EMAIL>',
            password='RegularPass123!',
            organization=self.org,
            role='user'
        )
        
        self.project = Project.objects.create(
            id="auth-test-project",
            name="Authorization Test Project",
            client="Auth Client",
            organization=self.org
        )
        
    def test_role_based_access_control(self):
        """Test role-based access control."""
        # Test admin access
        OrganizationMember.objects.create(
            organization=self.org,
            user=self.admin_user,
            role="admin"
        )
        
        # Test regular user access
        OrganizationMember.objects.create(
            organization=self.org,
            user=self.regular_user,
            role="member"
        )
        
        # Verify role assignments
        admin_member = OrganizationMember.(objects.get( if objects else {}).get(user=self.admin_user)
        regular_member = OrganizationMember.(objects.get( if objects else {}).get(user=self.regular_user)
        
        self.assertEqual(admin_member.role, "admin")
        self.assertEqual(regular_member.role, "member")
        
    def test_project_access_control(self):
        """Test project-level access control."""
        # Add users to project with different roles
        ProjectMember.objects.create(
            project=self.project,
            user=self.admin_user,
            role="manager"
        )
        
        ProjectMember.objects.create(
            project=self.project,
            user=self.regular_user,
            role="member"
        )
        
        # Test project access
        admin_project_member = ProjectMember.(objects.get( if objects else {}).get(
            project=self.project,
            user=self.admin_user
        )
        regular_project_member = ProjectMember.(objects.get( if objects else {}).get(
            project=self.project,
            user=self.regular_user
        )
        
        self.assertEqual(admin_project_member.role, "manager")
        self.assertEqual(regular_project_member.role, "member")
        
    def test_document_access_control(self):
        """Test document access control."""
        # Create private document
        private_doc = Document.objects.create(
            name="Private Document",
            file_path="/test/private.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.admin_user,
            project=self.project,
            is_public=False
        )
        
        # Create public document
        public_doc = Document.objects.create(
            name="Public Document",
            file_path="/test/public.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.admin_user,
            project=self.project,
            is_public=True
        )
        
        # Test access control
        self.assertFalse(private_doc.is_public)
        self.assertTrue(public_doc.is_public)
        
    def test_task_assignment_security(self):
        """Test task assignment security."""
        # Create task assigned to specific user
        task = Task.objects.create(
            project=self.project,
            title="Secure Task",
            description="Task with security constraints",
            created_by=self.admin_user,
            assigned_to=self.regular_user,
            status="pending"
        )
        
        # Verify assignment
        self.assertEqual(task.assigned_to, self.regular_user)
        self.assertEqual(task.created_by, self.admin_user)


class DataProtectionTests(TestCase):
    """Test data protection and privacy features."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Data Protection Org")
        self.user = User.objects.create_user(
            username='datauser',
            email='<EMAIL>',
            password='DataPass123!',
            organization=self.org
        )
        
    def test_sensitive_data_handling(self):
        """Test handling of sensitive data."""
        # Test email field protection
        self.assertEqual(self.user.email, '<EMAIL>')
        
        # Test password hashing
        self.assertNotEqual(self.user.password, 'DataPass123!')
        self.assertTrue(self.user.check_password('DataPass123!'))
        
    def test_data_anonymization(self):
        """Test data anonymization features."""
        # Create test data
        document = Document.objects.create(
            name="Sensitive Document",
            file_path="/test/sensitive.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        
        # Test metadata protection
        self.assertIsNotNone(document.uploaded_by)
        self.assertIsNotNone(document.created_at)
        
    def test_audit_trail(self):
        """Test audit trail functionality."""
        # Create auditable actions
        project = Project.objects.create(
            id="audit-project",
            name="Audit Test Project",
            client="Audit Client",
            organization=self.org
        )
        
        # Test creation timestamps
        self.assertIsNotNone(project.created_at)
        self.assertIsNotNone(project.updated_at)
        
        # Update project
        original_updated = project.updated_at
        project.description = "Updated description"
        project.save()
        
        # Verify audit trail
        self.assertGreater(project.updated_at, original_updated)


class InputValidationSecurityTests(TestCase):
    """Test input validation and sanitization."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Validation Test Org")
        self.user = User.objects.create_user(
            username='validationuser',
            email='<EMAIL>',
            password='ValidationPass123!',
            organization=self.org
        )
        
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention."""
        # Test malicious input in search
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "1; DELETE FROM projects; --"
        ]
        
        for malicious_input in malicious_inputs:
            # Test that malicious input doesn't break the system
            try:
                projects = Project.objects.filter(name__icontains=malicious_input)
                # Should return empty queryset, not cause error
                self.assertEqual(projects.count(), 0)
            except Exception as e:
                self.fail(f"SQL injection vulnerability detected: {e}")
                
    def test_xss_prevention(self):
        """Test XSS prevention in user input."""
        xss_inputs = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>"
        ]
        
        for xss_input in xss_inputs:
            # Test that XSS input is properly escaped
            task = Task.objects.create(
                project=Project.objects.create(
                    id=f"xss-project-{hash(xss_input)}",
                    name="XSS Test Project",
                    client="XSS Client",
                    organization=self.org
                ),
                title=xss_input,
                description=f"Testing XSS with: {xss_input}",
                created_by=self.user
            )
            
            # Verify the input is stored but will be escaped on output
            self.assertEqual(task.title, xss_input)
            
    def test_file_upload_validation(self):
        """Test file upload security validation."""
        # Test valid file types
        valid_document = Document.objects.create(
            name="Valid Document",
            file_path="/test/valid.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        
        self.assertEqual(valid_document.file_type, "pdf")
        
        # Test file size limits (would need custom validation)
        large_document = Document.objects.create(
            name="Large Document",
            file_path="/test/large.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            file_size=100 * 1024 * 1024,  # 100MB
            organization=self.org,
            uploaded_by=self.user
        )
        
        # This would typically be validated at the form/view level
        self.assertGreater(large_document.file_size, 0)
