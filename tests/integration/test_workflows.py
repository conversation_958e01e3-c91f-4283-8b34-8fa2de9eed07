"""
Integration Tests for CLEAR Workflows

Tests complete user workflows and end-to-end functionality.
"""

from django.test import TestCase, TransactionTestCase
from django.utils import timezone
import json

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage, Conversation,
    Task, Comment, TimeEntry, Stakeholder, ProjectMember
)


class ProjectWorkflowTests(TransactionTestCase):
    """Test complete project workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(
            name="Workflow Test Organization",
            description="Organization for testing workflows"
        )
        
        self.project_manager = User.objects.create_user(
            username='pm_user',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org,
            role='admin'
        )
        
        self.team_member = User.objects.create_user(
            username='team_user',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org,
            role='user'
        )
        
    def test_complete_project_lifecycle(self):
        """Test complete project lifecycle from creation to completion."""
        # 1. Create project
        project = Project.objects.create(
            id="workflow-project-001",
            name="Complete Workflow Project",
            client="Workflow Client",
            description="Testing complete project workflow",
            organization=self.org,
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=90)).date()
        )
        
        self.assertEqual(project.name, "Complete Workflow Project")
        
        # 2. Add team members
        pm_member = ProjectMember.objects.create(
            project=project,
            user=self.project_manager,
            role="manager"
        )
        
        team_member = ProjectMember.objects.create(
            project=project,
            user=self.team_member,
            role="member"
        )
        
        self.assertEqual(project.team_members.count(), 2)
        
        # 3. Create tasks
        task1 = Task.objects.create(
            project=project,
            title="Initial Planning Task",
            description="Plan the project workflow",
            status="pending",
            priority="high",
            created_by=self.project_manager,
            assigned_to=self.team_member
        )
        
        task2 = Task.objects.create(
            project=project,
            title="Implementation Task",
            description="Implement project features",
            status="pending",
            priority="medium",
            created_by=self.project_manager,
            assigned_to=self.team_member
        )
        
        self.assertEqual(project.tasks.count(), 2)
        
        # 4. Add documents
        document = Document.objects.create(
            name="Project Requirements",
            file_path="/test/requirements.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.project_manager,
            project=project
        )
        
        self.assertEqual(project.documents.count(), 1)
        
        # 5. Track time entries
        time_entry = TimeEntry.objects.create(
            user=self.team_member,
            project=project,
            description="Working on planning task",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=4),
            hours=4.0
        )
        
        # 6. Complete tasks
        task1.status = "completed"
        task1.completion_date = timezone.now()
        task1.save()
        
        # 7. Verify project progress
        completed_tasks = project.tasks.filter(status="completed").count()
        total_tasks = project.tasks.count()
        completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0
        
        self.assertEqual(completed_tasks, 1)
        self.assertEqual(completion_rate, 0.5)


class DocumentCollaborationWorkflowTests(TransactionTestCase):
    """Test document collaboration workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Doc Collaboration Org")
        
        self.author = User.objects.create_user(
            username='author',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        self.reviewer = User.objects.create_user(
            username='reviewer',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        self.project = Project.objects.create(
            id="doc-collab-project",
            name="Document Collaboration Project",
            client="Collaboration Client",
            organization=self.org
        )
        
    def test_document_review_workflow(self):
        """Test complete document review workflow."""
        # 1. Create initial document
        document = Document.objects.create(
            name="Design Specification",
            file_path="/test/design_spec_v1.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.author,
            project=self.project,
            current_version=1
        )
        
        # 2. Add comments for review
        comment = Comment.objects.create(
            content="Please review the technical specifications section",
            author=self.author,
            commentable_type="document",
            commentable_id=str(document.id)
        )
        
        # 3. Reviewer adds feedback
        review_comment = Comment.objects.create(
            content="Technical specs look good, but need more detail on implementation",
            author=self.reviewer,
            commentable_type="document",
            commentable_id=str(document.id),
            parent=comment
        )
        
        # 4. Create new version based on feedback
        from CLEAR.models import DocumentVersion
        new_version = DocumentVersion.objects.create(
            document=document,
            version_number=2,
            file_path="/test/design_spec_v2.pdf",
            file_size=2048,
            checksum="abc123",
            change_summary="Added implementation details based on review",
            uploaded_by=self.author
        )
        
        # Update document version
        document.current_version = 2
        document.save()
        
        # 5. Verify workflow completion
        self.assertEqual(document.current_version, 2)
        self.assertEqual(document.versions.count(), 1)
        
        # Get comments for this document
        document_comments = Comment.objects.filter(
            commentable_type="document",
            commentable_id=str(document.id)
        )
        self.assertEqual(document_comments.count(), 2)


class CommunicationWorkflowTests(TransactionTestCase):
    """Test communication and messaging workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Communication Test Org")
        
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        self.project = Project.objects.create(
            id="comm-project",
            name="Communication Project",
            client="Communication Client",
            organization=self.org
        )
        
    def test_team_communication_workflow(self):
        """Test team communication workflow."""
        # 1. Create conversation
        conversation = Conversation.objects.create(
            name="Project Discussion",
            conversation_type="group",
            created_by=self.user1,
            project=self.project
        )
        
        # 2. Add participants
        from CLEAR.models import ConversationMember
        ConversationMember.objects.create(
            conversation=conversation,
            user=self.user1,
            is_admin=True
        )
        
        ConversationMember.objects.create(
            conversation=conversation,
            user=self.user2
        )
        
        # 3. Exchange messages
        message1 = ChatMessage.objects.create(
            user=self.user1,
            conversation=conversation,
            content="Let's discuss the project timeline",
            project=self.project
        )
        
        message2 = ChatMessage.objects.create(
            user=self.user2,
            conversation=conversation,
            content="I think we need 2 more weeks for testing",
            project=self.project,
            reply_to=message1
        )
        
        # 4. Create urgent message
        urgent_message = ChatMessage.objects.create(
            user=self.user1,
            conversation=conversation,
            content="URGENT: Client meeting moved to tomorrow",
            project=self.project,
            is_urgent=True
        )
        
        # 5. Verify communication flow
        self.assertEqual(conversation.messages.count(), 3)
        self.assertEqual(conversation.members.count(), 2)
        
        urgent_messages = conversation.messages.filter(is_urgent=True)
        self.assertEqual(urgent_messages.count(), 1)
        
        # Test message threading
        replies = ChatMessage.objects.filter(reply_to=message1)
        self.assertEqual(replies.count(), 1)


class UserOnboardingWorkflowTests(TransactionTestCase):
    """Test user onboarding workflows."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(
            name="Onboarding Test Org",
            description="Organization for testing user onboarding"
        )
        
    def test_new_user_onboarding_workflow(self):
        """Test complete new user onboarding workflow."""
        # 1. Create new user
        new_user = User.objects.create_user(
            username='newuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='New',
            last_name='User',
            organization=self.org
        )
        
        # 2. Add to organization
        from CLEAR.models import OrganizationMember
        org_member = OrganizationMember.objects.create(
            organization=self.org,
            user=new_user,
            role="member"
        )
        
        # 3. Create welcome notification
        from CLEAR.models import Notification
        welcome_notification = Notification.objects.create(
            user=new_user,
            title="Welcome to CLEAR!",
            message="Welcome to the CLEAR platform. Let's get you started.",
            notification_type="info"
        )
        
        # 4. Assign to first project
        onboarding_project = Project.objects.create(
            id="onboarding-project",
            name="Onboarding Project",
            client="Internal",
            description="Project for new user onboarding",
            organization=self.org
        )
        
        ProjectMember.objects.create(
            project=onboarding_project,
            user=new_user,
            role="member"
        )
        
        # 5. Create first task
        first_task = Task.objects.create(
            project=onboarding_project,
            title="Complete Profile Setup",
            description="Please complete your user profile",
            status="pending",
            priority="medium",
            created_by=new_user,
            assigned_to=new_user
        )
        
        # 6. Verify onboarding completion
        self.assertTrue(new_user.is_active)
        self.assertEqual(new_user.organization, self.org)
        self.assertEqual(org_member.role, "member")
        self.assertEqual(new_user.assigned_tasks.count(), 1)
        self.assertEqual(new_user.notifications.count(), 1)
