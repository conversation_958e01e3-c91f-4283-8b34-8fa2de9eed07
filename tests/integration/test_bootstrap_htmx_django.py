"""
Comprehensive Integration Tests for Django + Bootstrap 5 + HTMX

Tests the complete stack integration including:
- Bootstrap 5 CSS/JS components
- HTMX interactions and responses
- Django template rendering
- HTMX-specific Bootstrap behaviors
"""

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.http import HttpResponse
from bs4 import BeautifulSoup
import json

from tests.utils.base import HTMXTestCase
from CLEAR.models import Project, Organization, Document, ChatMessage, Conversation

User = get_user_model()


class BootstrapHTMXIntegrationTests(HTMXTestCase):
    """Test Bootstrap 5 and HTMX integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.org = Organization.objects.create(name="Test Organization")
        self.user = self.create_test_user(organization=self.org)
        self.login_test_user(self.user)
        
        self.project = Project.objects.create(
            name="Test Project",
            description="A test project",
            organization=self.org,
            created_by=self.user,
            status="active"
        )
    
    def test_bootstrap_modal_htmx_integration(self):
        """Test Bootstrap modals with HTMX content loading."""
        # Test that modal triggers work with HTMX
        response = self.client.htmx_get(
            reverse('CLEAR:project_modal', kwargs={'pk': self.project.pk})  # type: ignore,
            target='modal-content'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap modal structure
        modal = soup.find('div', class_='modal')
        self.assertIsNotNone(modal, "Modal container should exist")
        
        modal_header = soup.find('div', class_='modal-header')
        self.assertIsNotNone(modal_header, "Modal header should exist")
        
        modal_body = soup.find('div', class_='modal-body')
        self.assertIsNotNone(modal_body, "Modal body should exist")
        
        # Check for HTMX attributes
        htmx_elements = soup.find_all(attrs={'hx-get': True})
        self.assertGreater(len(htmx_elements), 0, "Should have HTMX-enabled elements")
    
    def test_bootstrap_forms_htmx_validation(self):
        """Test Bootstrap form validation with HTMX."""
        form_data = {
            'name': '',  # Invalid - required field
            'description': 'Test description'
        }
        
        response = self.client.htmx_post(
            reverse('CLEAR:project_create_htmx')  # type: ignore,
            data=form_data,
            target='form-container'
        )
        
        self.assert_htmx_response(response, status_code=400)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap validation classes
        invalid_fields = soup.find_all(class_='is-invalid')
        self.assertGreater(len(invalid_fields), 0, "Should have invalid form fields")
        
        # Check for Bootstrap alert/error messages
        alerts = soup.find_all('div', class_='alert-danger')  # type: ignore
        self.assertGreater(len(alerts), 0, "Should have error alerts")
    
    def test_bootstrap_cards_htmx_updates(self):
        """Test Bootstrap cards with HTMX content updates."""
        response = self.client.htmx_get(
            reverse('CLEAR:project_card', kwargs={'pk': self.project.pk})  # type: ignore,
            target='project-card'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap card structure
        card = soup.find('div', class_='card')
        self.assertIsNotNone(card, "Card container should exist")
        
        card_header = soup.find('div', class_='card-header')
        card_body = soup.find('div', class_='card-body')
        
        self.assertIsNotNone(card_body, "Card body should exist")
        
        # Check for HTMX swap attributes
        self.assertTrue(
            any(elem.get('hx-swap') for elem in soup.find_all(attrs={'hx-swap': True})),
            "Should have HTMX swap directives"
        )
    
    def test_bootstrap_navigation_htmx(self):
        """Test Bootstrap navigation with HTMX page loading."""
        response = self.client.htmx_get(
            reverse('CLEAR:project_list_htmx')  # type: ignore,
            target='main-content'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap navigation components
        nav_elements = soup.find_all(['nav', 'ul'], class_=lambda x: x and 'nav' in x)  # type: ignore
        
        # Check for proper HTMX attributes on navigation links
        nav_links = soup.find_all('a', attrs={'hx-get': True})
        for link in nav_links:
            self.assertTrue(
                link.get('hx-target') or link.get('hx-swap'),
                "Navigation links should have HTMX targets or swap directives"
            )
    
    def test_bootstrap_tables_htmx_sorting(self):
        """Test Bootstrap tables with HTMX sorting and pagination."""
        # Create additional projects for table testing
        for i in range(10):
            Project.objects.create(
                name=f"Project {i}",
                organization=self.org,
                created_by=self.user
            )
        
        response = self.client.htmx_get(
            reverse('CLEAR:project_table_htmx')  # type: ignore,
            data={'sort': 'name', 'order': 'asc'},
            target='table-container'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap table structure
        table = soup.find('table', class_='table')
        self.assertIsNotNone(table, "Bootstrap table should exist")
        
        # Check for sortable headers with HTMX
        sortable_headers = soup.find_all('th', attrs={'hx-get': True})
        self.assertGreater(len(sortable_headers), 0, "Should have sortable table headers")
        
        # Check for pagination with HTMX
        pagination = soup.find('nav', attrs={'aria-label': 'Page navigation'})
        if pagination:
            page_links = pagination.find_all('a', attrs={'hx-get': True})
            self.assertGreater(len(page_links), 0, "Pagination links should use HTMX")
    
    def test_bootstrap_alerts_htmx_dismissal(self):
        """Test Bootstrap alerts with HTMX dismissal."""
        # Create a scenario that generates an alert
        response = self.client.htmx_post(
            reverse('CLEAR:project_update_htmx', kwargs={'pk': self.project.pk})  # type: ignore,
            data={'name': 'Updated Project Name', 'description': 'Updated'},
            target='project-form'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap success alert
        alerts = soup.find_all('div', class_='alert')  # type: ignore
        if alerts:
            alert = alerts[0]
            # Check for dismissible alert with HTMX
            dismiss_btn = alert.find('button', attrs={'hx-delete': True})
            if dismiss_btn:
                self.assertTrue(
                    dismiss_btn.get('hx-target'),
                    "Alert dismiss button should have HTMX target"
                )
    
    def test_bootstrap_components_responsive(self):
        """Test Bootstrap responsive components with HTMX."""
        response = self.client.htmx_get(
            reverse('CLEAR:dashboard_htmx')  # type: ignore,
            target='dashboard-content'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for Bootstrap responsive classes
        responsive_elements = soup.find_all(class_=lambda x: x and any(  # type: ignore
            responsive in x for responsive in ['col-', 'row', 'd-', 'flex-']
        ))
        self.assertGreater(len(responsive_elements), 0, "Should have responsive Bootstrap classes")
        
        # Check that HTMX elements preserve responsive behavior
        htmx_elements = soup.find_all(attrs={'hx-get': True})
        for element in htmx_elements:
            classes = element.get('class', [])
            if isinstance(classes, str):
                classes = classes.split()
            
            # Elements with HTMX should maintain Bootstrap classes
            bootstrap_classes = [cls for cls in classes if any(
                bs_prefix in cls for bs_prefix in ['btn-', 'card-', 'nav-', 'col-']
            )]
            
    def test_htmx_csrf_with_bootstrap_forms(self):
        """Test CSRF protection works with HTMX and Bootstrap forms."""
        # Test form submission without CSRF token
        response = self.client.post(
            reverse('CLEAR:project_create_htmx'),
            data={'name': 'Test Project', 'description': 'Test'},
            HTTP_HX_REQUEST='true'
        )
        
        # Should get CSRF error
        self.assertEqual(response.status_code, 403)
        
        # Test with proper CSRF handling
        csrf_response = self.client.get(reverse('CLEAR:project_create_form'))
        soup = BeautifulSoup(csrf_response.content, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})
        
        if csrf_token:
            response = self.client.htmx_post(
                reverse('CLEAR:project_create_htmx')  # type: ignore,
                data={
                    'name': 'Test Project',
                    'description': 'Test',
                    'csrfmiddlewaretoken': csrf_token['value']  # type: ignore
                },
                target='form-container'
            )
            
            self.assertIn(response.status_code, [200, 201, 302])
    
    def test_bootstrap_tooltips_popovers_htmx(self):
        """Test Bootstrap tooltips and popovers work with HTMX content."""
        response = self.client.htmx_get(
            reverse('CLEAR:project_detail_htmx', kwargs={'pk': self.project.pk})  # type: ignore,
            target='content-area'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for tooltip elements
        tooltip_elements = soup.find_all(attrs={'data-bs-toggle': 'tooltip'})
        popover_elements = soup.find_all(attrs={'data-bs-toggle': 'popover'})
        
        # Verify these elements can be dynamically loaded via HTMX
        for element in tooltip_elements + popover_elements:
            # Should have proper Bootstrap data attributes
            self.assertTrue(
                element.get('data-bs-toggle') in ['tooltip', 'popover'],
                "Bootstrap tooltip/popover elements should have proper toggle attribute"
            )


class HTMXResponseTests(HTMXTestCase):
    """Test HTMX-specific response behaviors."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.org = Organization.objects.create(name="Test Organization")
        self.user = self.create_test_user(organization=self.org)
        self.login_test_user(self.user)
    
    def test_htmx_headers_in_response(self):
        """Test that HTMX responses include proper headers."""
        response = self.client.htmx_get(
            reverse('CLEAR:project_list_htmx')  # type: ignore,
            target='main-content'
        )
        
        self.assert_htmx_response(response)
        
        # Check for HTMX-specific headers
        htmx_headers = [
            'HX-Trigger', 'HX-Location', 'HX-Push-Url',
            'HX-Redirect', 'HX-Refresh', 'HX-Replace-Url'
        ]
        
        # At least some HTMX responses should include these headers when appropriate
        self.assertTrue(
            any(header in response for header in htmx_headers) or response.status_code == 200,
            "HTMX responses should include appropriate headers or be successful"
        )
    
    def test_htmx_json_triggers(self):
        """Test HTMX JSON trigger responses."""
        # Create a scenario that should trigger client-side events
        project = Project.objects.create(
            name="Trigger Test Project",
            organization=self.org,
            created_by=self.user
        )
        
        response = self.client.htmx_delete(
            reverse('CLEAR:project_delete_htmx', kwargs={'pk': project.pk})  # type: ignore,
            target=f'project-{project.pk}'
        )
        
        # Should return successful deletion
        self.assertIn(response.status_code, [200, 204])
        
        # Check if HX-Trigger header is present for client-side events
        if 'HX-Trigger' in response:
            trigger_data = response['HX-Trigger']
            # Should be valid JSON if it's a complex trigger
            try:
                if trigger_data.startswith('{'):
                    json.loads(trigger_data)
            except json.JSONDecodeError:
                self.fail("HX-Trigger header should contain valid JSON for complex triggers")
    
    def test_htmx_partial_rendering(self):
        """Test that HTMX requests return partial HTML, not full pages."""
        response = self.client.htmx_get(
            reverse('CLEAR:project_list_htmx')  # type: ignore,
            target='content-area'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # HTMX responses should not include full HTML structure
        self.assertIsNone(soup.find('html'), "HTMX responses should not include <html> tag")
        self.assertIsNone(soup.find('head'), "HTMX responses should not include <head> tag")
        self.assertIsNone(soup.find('body'), "HTMX responses should not include <body> tag")
        
        # Should contain the actual content
        self.assertGreater(len(soup.text.strip()), 0, "Response should contain content")


class RealTimeFeatureTests(HTMXTestCase):
    """Test real-time features using HTMX."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.org = Organization.objects.create(name="Test Organization")
        self.user1 = self.create_test_user(username='user1', organization=self.org)
        self.user2 = self.create_test_user(username='user2', organization=self.org)
        
        self.conversation = Conversation.objects.create(
            name="Test Chat",
            created_by=self.user1,
            conversation_type='group'
        )
        self.conversation.participants.add(self.user1, self.user2)
    
    def test_live_chat_htmx(self):
        """Test live chat functionality with HTMX."""
        self.login_test_user(self.user1)
        
        # Send a message via HTMX
        response = self.client.htmx_post(
            reverse('CLEAR:chat_send_htmx')  # type: ignore,
            data={
                'conversation': self.conversation.pk,
                'content': 'Hello from HTMX!',
                'message_type': 'text'
            },
            target='chat-messages'
        )
        
        self.assert_htmx_response(response, status_code=201)
        
        # Check message was created
        message = ChatMessage.objects.filter(content='Hello from HTMX!').first()
        self.assertIsNotNone(message)
        self.assertEqual(message.user, self.user1)
        
        # Check response contains message HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        message_elements = soup.find_all(class_=lambda x: x and 'message' in x)  # type: ignore
        self.assertGreater(len(message_elements), 0, "Should contain message elements")
    
    def test_notification_updates_htmx(self):
        """Test real-time notification updates via HTMX."""
        self.login_test_user(self.user1)
        
        # Get notifications via HTMX
        response = self.client.htmx_get(
            reverse('CLEAR:notifications_htmx')  # type: ignore,
            target='notifications-container'
        )
        
        self.assert_htmx_response(response)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Should have notification structure
        notification_container = soup.find(id='notifications-container') or soup
        self.assertIsNotNone(notification_container)
        
        # Check for polling attributes (for real-time updates)
        polling_elements = soup.find_all(attrs={'hx-trigger': lambda x: x and 'every' in x})  # type: ignore
        # Real-time features often use polling or SSE
    
    def test_live_document_collaboration_htmx(self):
        """Test live document collaboration features."""
        self.login_test_user(self.user1)
        
        project = Project.objects.create(
            name="Collab Project",
            organization=self.org,
            created_by=self.user1
        )
        
        document = Document.objects.create(
            title="Collaborative Document",
            content="Initial content",
            project=project,
            uploaded_by=self.user1
        )
        
        # Test document editing via HTMX
        response = self.client.htmx_post(
            reverse('CLEAR:document_edit_htmx', kwargs={'pk': document.pk})  # type: ignore,
            data={
                'content': 'Updated content via HTMX',
                'title': 'Collaborative Document'
            },
            target='document-content'
        )
        
        self.assert_htmx_response(response)
        
        # Check document was updated
        document.refresh_from_db()
        self.assertEqual(document.content, 'Updated content via HTMX')


if __name__ == '__main__':
    import django
    django.setup()
    import unittest
    unittest.main()
