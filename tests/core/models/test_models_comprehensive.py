"""
Comprehensive Model Tests for CLEAR - Complete Test Suite

Tests all models for validation, relationships, methods, and edge cases.
Ensures data integrity and proper model behavior.
"""

from django.test import TestCase, override_settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone
from decimal import Decimal
import json
import uuid

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage, Conversation,
    UserActivity, TimeEntry, KnowledgeArticle,
    Task, Comment, Notification, ProjectMember,
    DocumentVersion, Stakeholder, OrganizationMember
)


class UserModelTests(TestCase):
    """Test User model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Organization")

    def test_user_creation(self):
        """Test basic user creation."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            organization=self.org
        )

        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.organization, self.org)
        self.assertTrue(user.check_password('testpass123'))

    def test_user_str_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        # Default Django User str is username
        self.assertEqual(str(user), 'testuser')

    def test_user_email_unique(self):
        """Test user email uniqueness."""
        User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )

        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                username='user2',
                email='<EMAIL>',  # Duplicate email
                password='testpass123'
            )

    def test_user_get_initials(self):
        """Test user get_initials method."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        self.assertEqual(user.get_initials(), 'TU')

    def test_user_role_choices(self):
        """Test user role field choices."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            role='admin'
        )

        self.assertEqual(user.role, 'admin')

        # Test default role
        user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )

        self.assertEqual(user2.role, 'user')  # Default value


class OrganizationModelTests(TestCase):
    """Test Organization model."""

    def test_organization_creation(self):
        """Test basic organization creation."""
        org = Organization.objects.create(
            name="Test Organization",
            description="A test organization",
            website="https://test.com",
            email="<EMAIL>",
            phone="555-1234",
            city="Test City",
            state="Test State",
            country="United States"
        )

        self.assertEqual(org.name, "Test Organization")
        self.assertEqual(org.description, "A test organization")
        self.assertEqual(org.website, "https://test.com")
        self.assertEqual(org.email, "<EMAIL>")
        self.assertEqual(org.phone, "555-1234")
        self.assertEqual(org.city, "Test City")
        self.assertEqual(org.state, "Test State")
        self.assertEqual(org.country, "United States")
        self.assertIsNotNone(org.created_at)
        self.assertIsNotNone(org.updated_at)

    def test_organization_str_representation(self):
        """Test organization string representation."""
        org = Organization.objects.create(name="Test Org")
        self.assertEqual(str(org), "Test Org")

    def test_organization_validation(self):
        """Test organization field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Organization.objects.create()  # No name

    def test_organization_defaults(self):
        """Test organization default values."""
        org = Organization.objects.create(name="Test Org")

        # Test default values
        self.assertEqual(org.country, "United States")
        self.assertEqual(org.timezone, "America/New_York")
        self.assertIsNotNone(org.created_at)
        self.assertIsNotNone(org.updated_at)


class ProjectModelTests(TestCase):
    """Test Project model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_project_creation(self):
        """Test basic project creation."""
        project = Project.objects.create(
            id="test-project-1",
            name="Test Project",
            client="Test Client",
            description="A test project",
            organization=self.org,
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timezone.timedelta(days=30)).date()
        )

        self.assertEqual(project.name, "Test Project")
        self.assertEqual(project.client, "Test Client")
        self.assertEqual(project.organization, self.org)
        self.assertEqual(project.description, "A test project")

    def test_project_str_representation(self):
        """Test project string representation."""
        project = Project.objects.create(
            id="test-project-str",
            name="Test Project",
            client="Test Client",
            organization=self.org
        )
        self.assertEqual(str(project), "Test Project (Test Client)")

    def test_project_validation(self):
        """Test project field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Project.objects.create()  # No required fields


class DocumentModelTests(TestCase):
    """Test Document model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            id="test-project-doc",
            name="Test Project",
            client="Test Client",
            organization=self.org
        )

    def test_document_creation(self):
        """Test basic document creation."""
        document = Document.objects.create(
            name="Test Document",
            file_path="/test/path/document.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            file_size=1024,
            organization=self.org,
            uploaded_by=self.user,
            project=self.project
        )

        self.assertEqual(document.name, "Test Document")
        self.assertEqual(document.file_path, "/test/path/document.pdf")
        self.assertEqual(document.file_type, "pdf")
        self.assertEqual(document.mime_type, "application/pdf")
        self.assertEqual(document.file_size, 1024)
        self.assertEqual(document.organization, self.org)
        self.assertEqual(document.uploaded_by, self.user)
        self.assertEqual(document.project, self.project)

    def test_document_defaults(self):
        """Test document default values."""
        document = Document.objects.create(
            name="Test Document",
            file_path="/test/path/document.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )

        # Test default values
        self.assertEqual(document.current_version, 1)
        self.assertFalse(document.is_locked)
        self.assertFalse(document.is_archived)
        self.assertFalse(document.is_public)
        self.assertEqual(document.file_size, 0)  # Default value
        self.assertEqual(document.tags, [])
        self.assertEqual(document.metadata, {})

    def test_document_str_representation(self):
        """Test document string representation."""
        document = Document.objects.create(
            name="Test Document",
            file_path="/test/path/document.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        self.assertEqual(str(document), "Test Document")


class TaskModelTests(TestCase):
    """Test Task model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            id="test-project-task",
            name="Test Project",
            client="Test Client",
            organization=self.org
        )

    def test_task_creation(self):
        """Test basic task creation."""
        task = Task.objects.create(
            project=self.project,
            title="Test Task",
            description="A test task",
            status="pending",
            priority="medium",
            created_by=self.user,
            assigned_to=self.user
        )

        self.assertEqual(task.project, self.project)
        self.assertEqual(task.title, "Test Task")
        self.assertEqual(task.description, "A test task")
        self.assertEqual(task.status, "pending")
        self.assertEqual(task.priority, "medium")
        self.assertEqual(task.created_by, self.user)
        self.assertEqual(task.assigned_to, self.user)

    def test_task_str_representation(self):
        """Test task string representation."""
        task = Task.objects.create(
            project=self.project,
            title="Test Task",
            created_by=self.user
        )
        self.assertEqual(str(task), "Test Task (Test Project)")

    def test_task_defaults(self):
        """Test task default values."""
        task = Task.objects.create(
            project=self.project,
            title="Test Task",
            created_by=self.user
        )

        # Test default values
        self.assertEqual(task.status, "pending")
        self.assertEqual(task.priority, "medium")
        self.assertIsNotNone(task.created_at)
        self.assertIsNotNone(task.updated_at)
        self.assertEqual(task.tags, [])
        self.assertEqual(task.attachments, [])

    def test_task_comment_methods(self):
        """Test task comment-related methods."""
        task = Task.objects.create(
            project=self.project,
            title="Test Task",
            created_by=self.user
        )

        # Test comment count method
        comment_count = task.get_comment_count()
        self.assertEqual(comment_count, 0)

        # Test recent comments method
        recent_comments = task.get_recent_comments()
        self.assertEqual(len(recent_comments), 0)


class ChatMessageModelTests(TestCase):
    """Test ChatMessage model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            id="test-project-chat",
            name="Test Project",
            client="Test Client",
            organization=self.org
        )

    def test_chat_message_creation(self):
        """Test basic chat message creation."""
        message = ChatMessage.objects.create(
            user=self.user,
            content="Test message content",
            channel="general",
            project=self.project
        )

        self.assertEqual(message.user, self.user)
        self.assertEqual(message.content, "Test message content")
        self.assertEqual(message.channel, "general")
        self.assertEqual(message.project, self.project)
        self.assertIsNotNone(message.timestamp)
        self.assertIsNotNone(message.created_at)

    def test_chat_message_defaults(self):
        """Test chat message default values."""
        message = ChatMessage.objects.create(
            user=self.user,
            content="Test message"
        )

        # Test default values
        self.assertEqual(message.channel, "general")
        self.assertEqual(message.message_type, "text")
        self.assertFalse(message.is_urgent)


class ConversationModelTests(TestCase):
    """Test Conversation model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_conversation_creation(self):
        """Test basic conversation creation."""
        conversation = Conversation.objects.create(
            name="Test Conversation",
            conversation_type="direct",
            created_by=self.user1
        )

        self.assertEqual(conversation.name, "Test Conversation")
        self.assertEqual(conversation.conversation_type, "direct")
        self.assertEqual(conversation.created_by, self.user1)
        self.assertIsNotNone(conversation.created_at)

    def test_conversation_defaults(self):
        """Test conversation default values."""
        conversation = Conversation.objects.create(
            created_by=self.user1
        )

        # Test default values
        self.assertEqual(conversation.conversation_type, "direct")
        self.assertTrue(conversation.is_active)


class TimeEntryModelTests(TestCase):
    """Test TimeEntry model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            id="test-project-time",
            name="Test Project",
            client="Test Client",
            organization=self.org
        )

    def test_time_entry_creation(self):
        """Test basic time entry creation."""
        start_time = timezone.now()
        end_time = start_time + timezone.timedelta(hours=2)

        time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work session",
            start_time=start_time,
            end_time=end_time,
            hours=2.0
        )

        self.assertEqual(time_entry.user, self.user)
        self.assertEqual(time_entry.project, self.project)
        self.assertEqual(time_entry.description, "Test work session")
        self.assertEqual(time_entry.hours, 2.0)

    def test_time_entry_validation(self):
        """Test time entry field validation."""
        start_time = timezone.now()

        # Test required fields
        with self.assertRaises(IntegrityError):
            TimeEntry.objects.create()  # No required fields


class KnowledgeArticleModelTests(TestCase):
    """Test KnowledgeArticle model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_knowledge_article_creation(self):
        """Test basic knowledge article creation."""
        article = KnowledgeArticle.objects.create(
            title="Test Article",
            content="Test article content",
            organization=self.org,
            author=self.user,
            slug="test-article"
        )

        self.assertEqual(article.title, "Test Article")
        self.assertEqual(article.content, "Test article content")
        self.assertEqual(article.organization, self.org)
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.slug, "test-article")

    def test_knowledge_article_defaults(self):
        """Test knowledge article default values."""
        article = KnowledgeArticle.objects.create(
            title="Test Article",
            content="Test content",
            organization=self.org,
            author=self.user,
            slug="test-article"
        )

        # Test default values
        self.assertTrue(article.is_published)
        self.assertTrue(article.is_public)
        self.assertEqual(article.view_count, 0)
        self.assertEqual(article.helpful_votes, 0)
        self.assertEqual(article.not_helpful_votes, 0)


class StakeholderModelTests(TestCase):
    """Test Stakeholder model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")

    def test_stakeholder_creation(self):
        """Test basic stakeholder creation."""
        stakeholder = Stakeholder.objects.create(
            full_name="John Doe",
            email="<EMAIL>",
            phone="555-1234",
            contact_company="Test Company",
            role="Project Manager"
        )

        self.assertEqual(stakeholder.full_name, "John Doe")
        self.assertEqual(stakeholder.email, "<EMAIL>")
        self.assertEqual(stakeholder.phone, "555-1234")
        self.assertEqual(stakeholder.contact_company, "Test Company")
        self.assertEqual(stakeholder.role, "Project Manager")

    def test_stakeholder_str_representation(self):
        """Test stakeholder string representation."""
        stakeholder = Stakeholder.objects.create(
            full_name="Jane Smith",
            contact_company="ABC Corp"
        )
        self.assertEqual(str(stakeholder), "Jane Smith (ABC Corp)")


class OrganizationMemberModelTests(TestCase):
    """Test OrganizationMember model."""

    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_organization_member_creation(self):
        """Test basic organization member creation."""
        member = OrganizationMember.objects.create(
            organization=self.org,
            user=self.user,
            role="admin"
        )

        self.assertEqual(member.organization, self.org)
        self.assertEqual(member.user, self.user)
        self.assertEqual(member.role, "admin")
        self.assertTrue(member.is_active)

    def test_organization_member_defaults(self):
        """Test organization member default values."""
        member = OrganizationMember.objects.create(
            organization=self.org,
            user=self.user
        )

        # Test default values
        self.assertEqual(member.role, "member")
        self.assertTrue(member.is_active)
        self.assertEqual(member.permissions, [])

    def test_organization_member_unique_constraint(self):
        """Test organization member unique constraint."""
        OrganizationMember.objects.create(
            organization=self.org,
            user=self.user
        )

        # Should not be able to create duplicate membership
        with self.assertRaises(IntegrityError):
            OrganizationMember.objects.create(
                organization=self.org,
                user=self.user
            )


# End of comprehensive model tests
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        
    def test_document_creation(self):
        """Test basic document creation."""
        document = Document.objects.create(
            title="Test Document",
            content="This is test content",
            uploaded_by=self.user,
            project=self.project,
            file_type="pdf",
            file_size=1024,
            is_private=False
        )
        
        self.assertEqual(document.title, "Test Document")
        self.assertEqual(document.content, "This is test content")
        self.assertEqual(document.uploaded_by, self.user)
        self.assertEqual(document.project, self.project)
        self.assertEqual(document.file_type, "pdf")
        self.assertEqual(document.file_size, 1024)
        self.assertFalse(document.is_private)
        
    def test_document_str_representation(self):
        """Test document string representation."""
        document = Document.objects.create(
            title="Test Document",
            content="Test content",
            uploaded_by=self.user,
            project=self.project
        )
        self.assertEqual(str(document), "Test Document")
        
    def test_document_validation(self):
        """Test document field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Document.objects.create()  # No required fields
            
        # Test file size validation
        with self.assertRaises(ValidationError):
            document = Document(
                title="Test Document",
                content="Test content",
                uploaded_by=self.user,
                project=self.project,
                file_size=-1  # Invalid file size
            )
            document.full_clean()
            
    def test_document_methods(self):
        """Test document methods."""
        document = Document.objects.create(
            title="Test Document",
            content="This is test content with multiple words",
            uploaded_by=self.user,
            project=self.project,
            file_size=1024
        )
        
        # Test get_word_count method
        self.assertEqual(document.get_word_count(), 7)
        
        # Test get_file_size_mb method
        self.assertEqual(document.get_file_size_mb(), 0.001)
        
        # Test can_user_access method
        self.assertTrue(document.can_user_access(self.user))
        
    def test_document_permissions(self):
        """Test document permissions."""
        # Create private document
        private_doc = Document.objects.create(
            title="Private Document",
            content="Private content",
            uploaded_by=self.user,
            project=self.project,
            is_private=True
        )
        
        # Owner should have access
        self.assertTrue(private_doc.can_user_access(self.user))
        
        # Other user should not have access
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertFalse(private_doc.can_user_access(other_user))


class MessageModelTests(TestCase):
    """Test Message model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = Conversation.objects.create(
            title="Test Conversation",
            created_by=self.user
        )
        
    def test_message_creation(self):
        """Test basic message creation."""
        message = ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Hello, this is a test message",
            message_type="text"
        )
        
        self.assertEqual(message.conversation, self.conversation)
        self.assertEqual(message.sender, self.user)
        self.assertEqual(message.content, "Hello, this is a test message")
        self.assertEqual(message.message_type, "text")
        self.assertFalse(message.is_edited)
        
    def test_message_str_representation(self):
        """Test message string representation."""
        message = ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Test message"
        )
        self.assertIn("Test message", str(message))
        
    def test_message_validation(self):
        """Test message field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            ChatMessage.objects.create()  # No required fields
            
        # Test empty content
        with self.assertRaises(ValidationError):
            message = ChatMessage(
                project=self.project,
                sender=self.user,
                content=""
            )
            message.full_clean()
            
    def test_message_methods(self):
        """Test message methods."""
        message = ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Original content"
        )
        
        # Test edit_message method
        message.edit_message("Updated content", self.user)
        self.assertEqual(message.content, "Updated content")
        self.assertTrue(message.is_edited)
        self.assertEqual(message.edited_by, self.user)
        
        # Test get_reactions method
        self.assertEqual(message.get_reactions().count(), 0)
        
    def test_message_attachments(self):
        """Test message attachments."""
        message = ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Message with attachment"
        )
        
        # Test adding attachment
        attachment = Document.objects.create(
            message=message,
            file_name="test.pdf",
            file_size=1024,
            file_type="pdf"
        )
        
        self.assertEqual(message.attachments.count(), 1)
        self.assertEqual(message.attachments.first(), attachment)


class TimeEntryModelTests(TestCase):
    """Test TimeEntry model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        
    def test_time_entry_creation(self):
        """Test basic time entry creation."""
        start_time = timezone.now()
        end_time = start_time + timezone.timedelta(hours=2)
        
        time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work session",
            start_time=start_time,
            end_time=end_time,
            duration_minutes=120
        )
        
        self.assertEqual(time_entry.user, self.user)
        self.assertEqual(time_entry.project, self.project)
        self.assertEqual(time_entry.description, "Test work session")
        self.assertEqual(time_entry.duration_minutes, 120)
        
    def test_time_entry_str_representation(self):
        """Test time entry string representation."""
        start_time = timezone.now()
        time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work",
            start_time=start_time,
            end_time=start_time + timezone.timedelta(hours=1),
            duration_minutes=60
        )
        self.assertIn("Test work", str(time_entry))
        
    def test_time_entry_validation(self):
        """Test time entry field validation."""
        start_time = timezone.now()
        end_time = start_time + timezone.timedelta(hours=1)
        
        # Test required fields
        with self.assertRaises(IntegrityError):
            TimeEntry.objects.create()  # No required fields
            
        # Test end_time after start_time
        with self.assertRaises(ValidationError):
            time_entry = TimeEntry(
                user=self.user,
                project=self.project,
                description="Test",
                start_time=end_time,
                end_time=start_time
            )
            time_entry.full_clean()
            
        # Test positive duration
        with self.assertRaises(ValidationError):
            time_entry = TimeEntry(
                user=self.user,
                project=self.project,
                description="Test",
                start_time=start_time,
                end_time=end_time,
                duration_minutes=-60
            )
            time_entry.full_clean()
            
    def test_time_entry_methods(self):
        """Test time entry methods."""
        start_time = timezone.now()
        end_time = start_time + timezone.timedelta(hours=2, minutes=30)
        
        time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work",
            start_time=start_time,
            end_time=end_time,
            duration_minutes=150
        )
        
        # Test get_duration_hours method
        self.assertEqual(time_entry.get_duration_hours(), 2.5)
        
        # Test get_formatted_duration method
        self.assertEqual(time_entry.get_formatted_duration(), "2h 30m")
        
    def test_time_entry_calculations(self):
        """Test time entry duration calculations."""
        start_time = timezone.now()
        end_time = start_time + timezone.timedelta(hours=1, minutes=45)
        
        time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work",
            start_time=start_time,
            end_time=end_time
        )
        
        # Duration should be calculated automatically
        self.assertEqual(time_entry.duration_minutes, 105)


class KnowledgeArticleModelTests(TestCase):
    """Test KnowledgeArticle model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
    def test_knowledge_article_creation(self):
        """Test basic knowledge article creation."""
        article = KnowledgeArticle.objects.create(
            title="Test Article",
            content="This is test content for the knowledge article",
            author=self.user,
            category="Technical",
            tags=["test", "knowledge", "article"],
            is_published=True
        )
        
        self.assertEqual(article.title, "Test Article")
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.category, "Technical")
        self.assertEqual(article.tags, ["test", "knowledge", "article"])
        self.assertTrue(article.is_published)
        
    def test_knowledge_article_str_representation(self):
        """Test knowledge article string representation."""
        article = KnowledgeArticle.objects.create(
            title="Test Article",
            content="Test content",
            author=self.user
        )
        self.assertEqual(str(article), "Test Article")
        
    def test_knowledge_article_validation(self):
        """Test knowledge article field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            KnowledgeArticle.objects.create()  # No required fields
            
        # Test unique title
        KnowledgeArticle.objects.create(
            title="Unique Article",
            content="Content",
            author=self.user
        )
        with self.assertRaises(IntegrityError):
            KnowledgeArticle.objects.create(
                title="Unique Article",
                content="Different content",
                author=self.user
            )
            
    def test_knowledge_article_methods(self):
        """Test knowledge article methods."""
        article = KnowledgeArticle.objects.create(
            title="Test Article",
            content="This is a test article with multiple words",
            author=self.user,
            category="Technical"
        )
        
        # Test get_word_count method
        self.assertEqual(article.get_word_count(), 8)
        
        # Test get_read_time method
        self.assertGreater(article.get_read_time(), 0)
        
        # Test get_view_count method
        self.assertEqual(article.get_view_count(), 0)
        
    def test_knowledge_article_search(self):
        """Test knowledge article search functionality."""
        # Create multiple articles
        KnowledgeArticle.objects.create(
            title="Python Programming",
            content="Guide to Python programming language",
            author=self.user,
            category="Programming"
        )
        KnowledgeArticle.objects.create(
            title="Django Framework",
            content="Introduction to Django web framework",
            author=self.user,
            category="Programming"
        )
        KnowledgeArticle.objects.create(
            title="Project Management",
            content="Best practices for project management",
            author=self.user,
            category="Management"
        )
        
        # Test search by title
        python_articles = KnowledgeArticle.objects.filter(title__icontains="Python")
        self.assertEqual(python_articles.count(), 1)
        
        # Test search by content
        programming_articles = KnowledgeArticle.objects.filter(content__icontains="programming")
        self.assertEqual(programming_articles.count(), 2)


class SpatialDataModelTests(TestCase):
    """Test SpatialData model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
    def test_spatial_data_creation(self):
        """Test basic spatial data creation."""
        spatial_data = SpatialData.objects.create(
            name="Test Location",
            description="A test location",
            created_by=self.user,
            data_type="point",
            coordinates=json.dumps({
                "type": "Point",
                "coordinates": [-87.6298, 41.8781]  # Chicago coordinates
            }),
            properties=json.dumps({
                "address": "123 Test St",
                "city": "Chicago",
                "state": "IL"
            })
        )
        
        self.assertEqual(spatial_data.name, "Test Location")
        self.assertEqual(spatial_data.created_by, self.user)
        self.assertEqual(spatial_data.data_type, "point")
        
    def test_spatial_data_str_representation(self):
        """Test spatial data string representation."""
        spatial_data = SpatialData.objects.create(
            name="Test Location",
            description="Test description",
            created_by=self.user,
            data_type="point",
            coordinates=json.dumps({"type": "Point", "coordinates": [0, 0]})
        )
        self.assertEqual(str(spatial_data), "Test Location")
        
    def test_spatial_data_validation(self):
        """Test spatial data field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            SpatialData.objects.create()  # No required fields
            
        # Test valid JSON coordinates
        with self.assertRaises(ValidationError):
            spatial_data = SpatialData(
                name="Test",
                created_by=self.user,
                data_type="point",
                coordinates="invalid json"
            )
            spatial_data.full_clean()
            
    def test_spatial_data_methods(self):
        """Test spatial data methods."""
        spatial_data = SpatialData.objects.create(
            name="Test Location",
            created_by=self.user,
            data_type="point",
            coordinates=json.dumps({
                "type": "Point",
                "coordinates": [-87.6298, 41.8781]
            })
        )
        
        # Test get_coordinates method
        coords = spatial_data.get_coordinates()
        self.assertEqual(coords["type"], "Point")
        self.assertEqual(coords["coordinates"], [-87.6298, 41.8781])
        
        # Test get_properties method
        properties = spatial_data.get_properties()
        self.assertIsInstance(properties, dict)
        
    def test_spatial_data_geometry_types(self):
        """Test different geometry types."""
        # Point
        point = SpatialData.objects.create(
            name="Point",
            created_by=self.user,
            data_type="point",
            coordinates=json.dumps({
                "type": "Point",
                "coordinates": [0, 0]
            })
        )
        
        # LineString
        line = SpatialData.objects.create(
            name="Line",
            created_by=self.user,
            data_type="linestring",
            coordinates=json.dumps({
                "type": "LineString",
                "coordinates": [[0, 0], [1, 1], [2, 2]]
            })
        )
        
        # Polygon
        polygon = SpatialData.objects.create(
            name="Polygon",
            created_by=self.user,
            data_type="polygon",
            coordinates=json.dumps({
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
            })
        )
        
        self.assertEqual(point.data_type, "point")
        self.assertEqual(line.data_type, "linestring")
        self.assertEqual(polygon.data_type, "polygon")


class TaskModelTests(TestCase):
    """Test Task model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        
    def test_task_creation(self):
        """Test basic task creation."""
        task = Task.objects.create(
            title="Test Task",
            description="A test task description",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user,
            status="pending",
            priority="medium",
            due_date=timezone.now() + timezone.timedelta(days=7)
        )
        
        self.assertEqual(task.title, "Test Task")
        self.assertEqual(task.project, self.project)
        self.assertEqual(task.assigned_to, self.user)
        self.assertEqual(task.status, "pending")
        self.assertEqual(task.priority, "medium")
        
    def test_task_str_representation(self):
        """Test task string representation."""
        task = Task.objects.create(
            title="Test Task",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user
        )
        self.assertEqual(str(task), "Test Task")
        
    def test_task_validation(self):
        """Test task field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Task.objects.create()  # No required fields
            
        # Test due_date in future
        with self.assertRaises(ValidationError):
            task = Task(
                title="Test Task",
                project=self.project,
                assigned_to=self.user,
                created_by=self.user,
                due_date=timezone.now() - timezone.timedelta(days=1)
            )
            task.full_clean()
            
    def test_task_methods(self):
        """Test task methods."""
        task = Task.objects.create(
            title="Test Task",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user,
            due_date=timezone.now() + timezone.timedelta(days=1)
        )
        
        # Test is_overdue method
        self.assertFalse(task.is_overdue())
        
        # Test get_comments method
        self.assertEqual(task.get_comments().count(), 0)
        
        # Test get_time_entries method
        self.assertEqual(task.get_time_entries().count(), 0)
        
    def test_task_status_transitions(self):
        """Test task status transitions."""
        task = Task.objects.create(
            title="Test Task",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user
        )
        
        # Test valid status transitions
        task.status = "in_progress"
        task.save()
        self.assertEqual(task.status, "in_progress")
        
        task.status = "completed"
        task.save()
        self.assertEqual(task.status, "completed")


class CommentModelTests(TestCase):
    """Test Comment model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        
    def test_comment_creation(self):
        """Test basic comment creation."""
        comment = Comment.objects.create(
            content="This is a test comment",
            author=self.user,
            project=self.project
        )
        
        self.assertEqual(comment.content, "This is a test comment")
        self.assertEqual(comment.author, self.user)
        self.assertEqual(comment.project, self.project)
        self.assertFalse(comment.is_edited)
        
    def test_comment_str_representation(self):
        """Test comment string representation."""
        comment = Comment.objects.create(
            content="Test comment",
            author=self.user,
            project=self.project
        )
        self.assertIn("Test comment", str(comment))
        
    def test_comment_validation(self):
        """Test comment field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Comment.objects.create()  # No required fields
            
        # Test empty content
        with self.assertRaises(ValidationError):
            comment = Comment(
                content="",
                author=self.user,
                project=self.project
            )
            comment.full_clean()
            
    def test_comment_methods(self):
        """Test comment methods."""
        comment = Comment.objects.create(
            content="Original comment",
            author=self.user,
            project=self.project
        )
        
        # Test edit_comment method
        comment.edit_comment("Updated comment", self.user)
        self.assertEqual(comment.content, "Updated comment")
        self.assertTrue(comment.is_edited)
        self.assertEqual(comment.edited_by, self.user)
        
        # Test get_replies method
        self.assertEqual(comment.get_replies().count(), 0)


class NotificationModelTests(TestCase):
    """Test Notification model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
    def test_notification_creation(self):
        """Test basic notification creation."""
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification message",
            notification_type="info",
            is_read=False
        )
        
        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.title, "Test Notification")
        self.assertEqual(notification.message, "This is a test notification message")
        self.assertEqual(notification.notification_type, "info")
        self.assertFalse(notification.is_read)
        
    def test_notification_str_representation(self):
        """Test notification string representation."""
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="Test message",
            notification_type="info"
        )
        self.assertEqual(str(notification), "Test Notification")
        
    def test_notification_validation(self):
        """Test notification field validation."""
        # Test required fields
        with self.assertRaises(IntegrityError):
            Notification.objects.create()  # No required fields
            
        # Test valid notification types
        valid_types = ["info", "success", "warning", "error"]
        for notification_type in valid_types:
            notification = Notification(
                user=self.user,
                title="Test",
                message="Test message",
                notification_type=notification_type
            )
            notification.full_clean()  # Should not raise error
            
        # Test invalid notification type
        with self.assertRaises(ValidationError):
            notification = Notification(
                user=self.user,
                title="Test",
                message="Test message",
                notification_type="invalid_type"
            )
            notification.full_clean()
            
    def test_notification_methods(self):
        """Test notification methods."""
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="Test message",
            notification_type="info"
        )
        
        # Test mark_as_read method
        self.assertFalse(notification.is_read)
        notification.mark_as_read()
        self.assertTrue(notification.is_read)
        
        # Test mark_as_unread method
        notification.mark_as_unread()
        self.assertFalse(notification.is_read)


if __name__ == '__main__':
    import django
    django.setup()
    import unittest
    unittest.main() 