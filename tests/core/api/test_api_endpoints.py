"""
API Endpoint Tests for CLEAR

Tests REST API endpoints for authentication, permissions, response formats,
and business logic validation.
"""

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from rest_framework.authtoken.models import Token
import json

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage, Conversation,
    UserActivity, TimeEntry, KnowledgeArticle,
    Task, Comment, Notification, ProjectMember
)


class APIBaseTestCase(APITestCase):
    """Base test case for API testing."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = APIClient()
        self.org = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
    def authenticate_user(self, user=None):
        """Authenticate a user for API requests."""
        if user is None:
            user = self.user
        token = Token.objects.get_or_create(user=user)[0]
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')


class AuthenticationAPITests(APIBaseTestCase):
    """Test authentication API endpoints."""
    
    def test_user_registration(self):
        """Test user registration endpoint."""
        url = reverse('api:user-register')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'first_name': 'New',
            'last_name': 'User',
            'organization_name': 'New Organization'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('token', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['username'], 'newuser')
        
        # Verify user was created
        user = User.objects.get(username='newuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, 'New')
        self.assertEqual(user.last_name, 'User')
        
    def test_user_login(self):
        """Test user login endpoint."""
        url = reverse('api:user-login')
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['username'], 'testuser')
        
    def test_user_login_invalid_credentials(self):
        """Test user login with invalid credentials."""
        url = reverse('api:user-login')
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        
    def test_user_logout(self):
        """Test user logout endpoint."""
        url = reverse('api:user-logout')
        
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Verify token was deleted
        self.assertFalse(Token.objects.filter(user=self.user).exists())
        
    def test_user_profile(self):
        """Test user profile endpoint."""
        url = reverse('api:user-profile')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], 'testuser')
        self.assertEqual(response.data['email'], '<EMAIL>')
        
    def test_user_profile_update(self):
        """Test user profile update endpoint."""
        url = reverse('api:user-profile')
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'email': '<EMAIL>'
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated')
        self.assertEqual(response.data['last_name'], 'Name')
        self.assertEqual(response.data['email'], '<EMAIL>')
        
        # Verify database was updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')
        self.assertEqual(self.user.email, '<EMAIL>')
        
    def test_password_change(self):
        """Test password change endpoint."""
        url = reverse('api:user-change-password')
        data = {
            'old_password': 'testpass123',
            'new_password': 'newpass123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpass123'))
        
    def test_password_change_invalid_old_password(self):
        """Test password change with invalid old password."""
        url = reverse('api:user-change-password')
        data = {
            'old_password': 'wrongpassword',
            'new_password': 'newpass123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)


class ProjectAPITests(APIBaseTestCase):
    """Test project API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            description="A test project",
            organization=self.org,
            created_by=self.user,
            status="active"
        )
        
    def test_project_list(self):
        """Test project list endpoint."""
        url = reverse('api:project-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Test Project')
        
    def test_project_create(self):
        """Test project creation endpoint."""
        url = reverse('api:project-list')
        data = {
            'name': 'New Project',
            'description': 'A new test project',
            'status': 'active',
            'priority': 'medium',
            'start_date': '2024-01-01',
            'end_date': '2024-12-31'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Project')
        self.assertEqual(response.data['description'], 'A new test project')
        
        # Verify project was created
        project = Project.objects.get(name='New Project')
        self.assertEqual(project.created_by, self.user)
        self.assertEqual(project.organization, self.org)
        
    def test_project_detail(self):
        """Test project detail endpoint."""
        url = reverse('api:project-detail', kwargs={'pk': self.project.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Project')
        self.assertEqual(response.data['description'], 'A test project')
        
    def test_project_update(self):
        """Test project update endpoint."""
        url = reverse('api:project-detail', kwargs={'pk': self.project.pk})
        data = {
            'name': 'Updated Project',
            'description': 'Updated description',
            'status': 'completed'
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Project')
        self.assertEqual(response.data['description'], 'Updated description')
        self.assertEqual(response.data['status'], 'completed')
        
        # Verify database was updated
        self.project.refresh_from_db()
        self.assertEqual(self.project.name, 'Updated Project')
        self.assertEqual(self.project.status, 'completed')
        
    def test_project_delete(self):
        """Test project deletion endpoint."""
        url = reverse('api:project-detail', kwargs={'pk': self.project.pk})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify project was deleted
        self.assertFalse(Project.objects.filter(pk=self.project.pk).exists())
        
    def test_project_members(self):
        """Test project members endpoint."""
        url = reverse('api:project-members', kwargs={'pk': self.project.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('members', response.data)
        
    def test_project_statistics(self):
        """Test project statistics endpoint."""
        url = reverse('api:project-statistics', kwargs={'pk': self.project.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_hours', response.data)
        self.assertIn('total_tasks', response.data)
        self.assertIn('completion_percentage', response.data)
        
    def test_project_permissions(self):
        """Test project permissions."""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        # Test access without authentication
        self.client.credentials()  # Remove authentication
        url = reverse('api:project-detail', kwargs={'pk': self.project.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test access with different user
        self.authenticate_user(other_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)  # Same org should have access


class DocumentAPITests(APIBaseTestCase):
    """Test document API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        self.document = Document.objects.create(
            title="Test Document",
            content="Test document content",
            uploaded_by=self.user,
            project=self.project,
            file_type="pdf",
            file_size=1024
        )
        
    def test_document_list(self):
        """Test document list endpoint."""
        url = reverse('api:document-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Test Document')
        
    def test_document_create(self):
        """Test document creation endpoint."""
        url = reverse('api:document-list')
        data = {
            'title': 'New Document',
            'content': 'New document content',
            'project': self.project.pk,
            'file_type': 'docx',
            'file_size': 2048,
            'is_private': False
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Document')
        self.assertEqual(response.data['content'], 'New document content')
        
        # Verify document was created
        document = Document.objects.get(title='New Document')
        self.assertEqual(document.uploaded_by, self.user)
        self.assertEqual(document.project, self.project)
        
    def test_document_detail(self):
        """Test document detail endpoint."""
        url = reverse('api:document-detail', kwargs={'pk': self.document.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Document')
        self.assertEqual(response.data['content'], 'Test document content')
        
    def test_document_update(self):
        """Test document update endpoint."""
        url = reverse('api:document-detail', kwargs={'pk': self.document.pk})
        data = {
            'title': 'Updated Document',
            'content': 'Updated content',
            'is_private': True
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Updated Document')
        self.assertEqual(response.data['content'], 'Updated content')
        self.assertTrue(response.data['is_private'])
        
        # Verify database was updated
        self.document.refresh_from_db()
        self.assertEqual(self.document.title, 'Updated Document')
        self.assertTrue(self.document.is_private)
        
    def test_document_delete(self):
        """Test document deletion endpoint."""
        url = reverse('api:document-detail', kwargs={'pk': self.document.pk})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify document was deleted
        self.assertFalse(Document.objects.filter(pk=self.document.pk).exists())
        
    def test_document_search(self):
        """Test document search endpoint."""
        # Create additional documents
        Document.objects.create(
            title="Python Guide",
            content="Guide to Python programming",
            uploaded_by=self.user,
            project=self.project
        )
        Document.objects.create(
            title="Django Tutorial",
            content="Tutorial for Django framework",
            uploaded_by=self.user,
            project=self.project
        )
        
        url = reverse('api:document-search')
        response = self.client.get(url, {'q': 'Python'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Python Guide')
        
    def test_document_permissions(self):
        """Test document permissions."""
        # Create private document
        private_doc = Document.objects.create(
            title="Private Document",
            content="Private content",
            uploaded_by=self.user,
            project=self.project,
            is_private=True
        )
        
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        # Test access with different user
        self.authenticate_user(other_user)
        url = reverse('api:document-detail', kwargs={'pk': private_doc.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TimeEntryAPITests(APIBaseTestCase):
    """Test time entry API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        self.time_entry = TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work session",
            start_time='2024-01-01T09:00:00Z',
            end_time='2024-01-01T11:00:00Z',
            duration_minutes=120
        )
        
    def test_time_entry_list(self):
        """Test time entry list endpoint."""
        url = reverse('api:timeentry-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['description'], 'Test work session')
        
    def test_time_entry_create(self):
        """Test time entry creation endpoint."""
        url = reverse('api:timeentry-list')
        data = {
            'project': self.project.pk,
            'description': 'New work session',
            'start_time': '2024-01-02T09:00:00Z',
            'end_time': '2024-01-02T10:30:00Z',
            'duration_minutes': 90
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['description'], 'New work session')
        self.assertEqual(response.data['duration_minutes'], 90)
        
        # Verify time entry was created
        time_entry = TimeEntry.objects.get(description='New work session')
        self.assertEqual(time_entry.user, self.user)
        self.assertEqual(time_entry.project, self.project)
        
    def test_time_entry_detail(self):
        """Test time entry detail endpoint."""
        url = reverse('api:timeentry-detail', kwargs={'pk': self.time_entry.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['description'], 'Test work session')
        self.assertEqual(response.data['duration_minutes'], 120)
        
    def test_time_entry_update(self):
        """Test time entry update endpoint."""
        url = reverse('api:timeentry-detail', kwargs={'pk': self.time_entry.pk})
        data = {
            'description': 'Updated work session',
            'duration_minutes': 150
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['description'], 'Updated work session')
        self.assertEqual(response.data['duration_minutes'], 150)
        
        # Verify database was updated
        self.time_entry.refresh_from_db()
        self.assertEqual(self.time_entry.description, 'Updated work session')
        self.assertEqual(self.time_entry.duration_minutes, 150)
        
    def test_time_entry_delete(self):
        """Test time entry deletion endpoint."""
        url = reverse('api:timeentry-detail', kwargs={'pk': self.time_entry.pk})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify time entry was deleted
        self.assertFalse(TimeEntry.objects.filter(pk=self.time_entry.pk).exists())
        
    def test_time_entry_validation(self):
        """Test time entry validation."""
        url = reverse('api:timeentry-list')
        data = {
            'project': self.project.pk,
            'description': 'Invalid time entry',
            'start_time': '2024-01-01T11:00:00Z',
            'end_time': '2024-01-01T09:00:00Z',  # End before start
            'duration_minutes': 120
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        
    def test_time_entry_user_filter(self):
        """Test time entry filtering by user."""
        # Create time entry for different user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        TimeEntry.objects.create(
            user=other_user,
            project=self.project,
            description="Other user's work",
            start_time='2024-01-01T09:00:00Z',
            end_time='2024-01-01T10:00:00Z',
            duration_minutes=60
        )
        
        url = reverse('api:timeentry-list')
        response = self.client.get(url, {'user': self.user.pk})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['user'], self.user.pk)


class MessageAPITests(APIBaseTestCase):
    """Test message API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.conversation = Conversation.objects.create(
            title="Test Conversation",
            created_by=self.user
        )
        self.message = ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Test message content",
            message_type="text"
        )
        
    def test_message_list(self):
        """Test message list endpoint."""
        url = reverse('api:message-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['content'], 'Test message content')
        
    def test_message_create(self):
        """Test message creation endpoint."""
        url = reverse('api:message-list')
        data = {
            'conversation': self.conversation.pk,
            'content': 'New message content',
            'message_type': 'text'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['content'], 'New message content')
        self.assertEqual(response.data['sender'], self.user.pk)
        
        # Verify message was created
        message = ChatMessage.objects.get(content='New message content')
        self.assertEqual(message.sender, self.user)
        self.assertEqual(message.conversation, self.conversation)
        
    def test_message_detail(self):
        """Test message detail endpoint."""
        url = reverse('api:message-detail', kwargs={'pk': self.message.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['content'], 'Test message content')
        self.assertEqual(response.data['sender'], self.user.pk)
        
    def test_message_update(self):
        """Test message update endpoint."""
        url = reverse('api:message-detail', kwargs={'pk': self.message.pk})
        data = {
            'content': 'Updated message content'
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['content'], 'Updated message content')
        self.assertTrue(response.data['is_edited'])
        
        # Verify database was updated
        self.message.refresh_from_db()
        self.assertEqual(self.message.content, 'Updated message content')
        self.assertTrue(self.message.is_edited)
        
    def test_message_delete(self):
        """Test message deletion endpoint."""
        url = reverse('api:message-detail', kwargs={'pk': self.message.pk})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify message was deleted
        self.assertFalse(ChatMessage.objects.filter(pk=self.message.pk).exists())
        
    def test_conversation_messages(self):
        """Test conversation messages endpoint."""
        # Create additional messages
        ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Second message",
            message_type="text"
        )
        ChatMessage.objects.create(
            project=self.project,
            sender=self.user,
            content="Third message",
            message_type="text"
        )
        
        url = reverse('api:conversation-messages', kwargs={'pk': self.conversation.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)
        
    def test_message_permissions(self):
        """Test message permissions."""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        # Test updating another user's message
        self.authenticate_user(other_user)
        url = reverse('api:message-detail', kwargs={'pk': self.message.pk})
        data = {'content': 'Unauthorized update'}
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TaskAPITests(APIBaseTestCase):
    """Test task API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        self.task = Task.objects.create(
            title="Test Task",
            description="Test task description",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user,
            status="pending",
            priority="medium"
        )
        
    def test_task_list(self):
        """Test task list endpoint."""
        url = reverse('api:task-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Test Task')
        
    def test_task_create(self):
        """Test task creation endpoint."""
        url = reverse('api:task-list')
        data = {
            'title': 'New Task',
            'description': 'New task description',
            'project': self.project.pk,
            'assigned_to': self.user.pk,
            'status': 'pending',
            'priority': 'high',
            'due_date': '2024-12-31T23:59:59Z'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Task')
        self.assertEqual(response.data['description'], 'New task description')
        self.assertEqual(response.data['created_by'], self.user.pk)
        
        # Verify task was created
        task = Task.objects.get(title='New Task')
        self.assertEqual(task.created_by, self.user)
        self.assertEqual(task.project, self.project)
        
    def test_task_detail(self):
        """Test task detail endpoint."""
        url = reverse('api:task-detail', kwargs={'pk': self.task.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Task')
        self.assertEqual(response.data['description'], 'Test task description')
        
    def test_task_update(self):
        """Test task update endpoint."""
        url = reverse('api:task-detail', kwargs={'pk': self.task.pk})
        data = {
            'title': 'Updated Task',
            'description': 'Updated description',
            'status': 'in_progress',
            'priority': 'high'
        }
        
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Updated Task')
        self.assertEqual(response.data['status'], 'in_progress')
        self.assertEqual(response.data['priority'], 'high')
        
        # Verify database was updated
        self.task.refresh_from_db()
        self.assertEqual(self.task.title, 'Updated Task')
        self.assertEqual(self.task.status, 'in_progress')
        
    def test_task_delete(self):
        """Test task deletion endpoint."""
        url = reverse('api:task-detail', kwargs={'pk': self.task.pk})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify task was deleted
        self.assertFalse(Task.objects.filter(pk=self.task.pk).exists())
        
    def test_task_status_transitions(self):
        """Test task status transitions."""
        url = reverse('api:task-detail', kwargs={'pk': self.task.pk})
        
        # Test valid status transitions
        statuses = ['pending', 'in_progress', 'review', 'completed']
        for status_value in statuses:
            data = {'status': status_value}
            response = self.client.patch(url, data, format='json')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['status'], status_value)
            
    def test_task_filtering(self):
        """Test task filtering."""
        # Create tasks with different statuses
        Task.objects.create(
            title="Completed Task",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user,
            status="completed"
        )
        Task.objects.create(
            title="In Progress Task",
            project=self.project,
            assigned_to=self.user,
            created_by=self.user,
            status="in_progress"
        )
        
        url = reverse('api:task-list')
        
        # Filter by status
        response = self.client.get(url, {'status': 'completed'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Completed Task')
        
        # Filter by assigned user
        response = self.client.get(url, {'assigned_to': self.user.pk})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)


class AnalyticsAPITests(APIBaseTestCase):
    """Test analytics API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.org,
            created_by=self.user
        )
        
        # Create test data for analytics
        for i in range(5):
            TimeEntry.objects.create(
                user=self.user,
                project=self.project,
                description=f"Work session {i}",
                start_time=f'2024-01-{i+1:02d}T09:00:00Z',
                end_time=f'2024-01-{i+1:02d}T11:00:00Z',
                duration_minutes=120
            )
            
    def test_user_analytics(self):
        """Test user analytics endpoint."""
        url = reverse('api:user-analytics')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_hours', response.data)
        self.assertIn('projects_worked_on', response.data)
        self.assertIn('average_session_length', response.data)
        self.assertIn('most_productive_day', response.data)
        
    def test_project_analytics(self):
        """Test project analytics endpoint."""
        url = reverse('api:project-analytics', kwargs={'pk': self.project.pk})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_hours', response.data)
        self.assertIn('total_entries', response.data)
        self.assertIn('average_session_length', response.data)
        self.assertIn('completion_percentage', response.data)
        
    def test_organization_analytics(self):
        """Test organization analytics endpoint."""
        url = reverse('api:organization-analytics')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_users', response.data)
        self.assertIn('total_projects', response.data)
        self.assertIn('total_hours', response.data)
        self.assertIn('active_projects', response.data)
        
    def test_analytics_date_range(self):
        """Test analytics with date range filtering."""
        url = reverse('api:user-analytics')
        params = {
            'start_date': '2024-01-01',
            'end_date': '2024-01-05'
        }
        
        response = self.client.get(url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_hours', response.data)
        self.assertGreater(response.data['total_hours'], 0)


if __name__ == '__main__':
    import django
    django.setup()
    import unittest
    unittest.main()