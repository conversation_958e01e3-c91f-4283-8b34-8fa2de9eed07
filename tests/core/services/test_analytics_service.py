"""
Comprehensive Service Tests for CLEAR Analytics

Tests business logic, service layer functionality, and integration between components.
"""

from django.test import TestCase, override_settings
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
import json

from CLEAR.models import (
    User, Organization, Project, Document, ChatMessage, Conversation,
    Task, Comment, TimeEntry, Stakeholder
)
from CLEAR.services.analytics import AnalyticsEngine
from CLEAR.services.notifications import NotificationService
from CLEAR.services.document_processing import DocumentProcessor


class AnalyticsServiceTests(TestCase):
    """Test Analytics service functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(
            name="Test Organization",
            description="Test organization for analytics"
        )
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
        self.project = Project.objects.create(
            id="test-project-analytics",
            name="Analytics Test Project",
            client="Test Client",
            organization=self.org,
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=30)).date()
        )
        
        self.analytics_engine = AnalyticsEngine(organization=self.org)
        
    def test_analytics_engine_initialization(self):
        """Test analytics engine initialization."""
        engine = AnalyticsEngine()
        self.assertIsNone(engine.organization)
        
        engine_with_org = AnalyticsEngine(organization=self.org)
        self.assertEqual(engine_with_org.organization, self.org)
        
    def test_project_analytics(self):
        """Test project analytics calculations."""
        # Create test tasks
        Task.objects.create(
            project=self.project,
            title="Test Task 1",
            status="completed",
            created_by=self.user
        )
        Task.objects.create(
            project=self.project,
            title="Test Task 2",
            status="in_progress",
            created_by=self.user
        )
        
        # Test project metrics
        metrics = self.analytics_engine.get_project_metrics(self.project.id)
        self.assertIsInstance(metrics, dict)
        self.assertIn('total_tasks', metrics)
        self.assertIn('completed_tasks', metrics)
        
    def test_user_activity_analytics(self):
        """Test user activity analytics."""
        # Create test activities
        TimeEntry.objects.create(
            user=self.user,
            project=self.project,
            description="Test work",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=2),
            hours=2.0
        )
        
        # Test user metrics
        metrics = self.analytics_engine.get_user_activity_metrics(self.user.id)
        self.assertIsInstance(metrics, dict)
        
    def test_organization_analytics(self):
        """Test organization-wide analytics."""
        # Create test data
        Document.objects.create(
            name="Test Document",
            file_path="/test/path/doc.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        
        # Test organization metrics
        metrics = self.analytics_engine.get_organization_metrics()
        self.assertIsInstance(metrics, dict)
        self.assertIn('total_projects', metrics)
        self.assertIn('total_users', metrics)


class NotificationServiceTests(TestCase):
    """Test Notification service functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        self.notification_service = NotificationService()
        
    def test_notification_creation(self):
        """Test notification creation."""
        notification = self.notification_service.create_notification(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            notification_type="info"
        )
        
        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.title, "Test Notification")
        self.assertEqual(notification.message, "This is a test notification")
        self.assertEqual(notification.notification_type, "info")
        
    def test_bulk_notification_creation(self):
        """Test bulk notification creation."""
        users = [self.user]
        notifications = self.notification_service.create_bulk_notifications(
            users=users,
            title="Bulk Test",
            message="Bulk notification test",
            notification_type="info"
        )
        
        self.assertEqual(len(notifications), 1)
        self.assertEqual(notifications[0].user, self.user)
        
    def test_notification_delivery(self):
        """Test notification delivery methods."""
        notification = self.notification_service.create_notification(
            user=self.user,
            title="Delivery Test",
            message="Test delivery",
            notification_type="info"
        )
        
        # Test marking as delivered
        self.notification_service.mark_as_delivered(notification)
        notification.refresh_from_db()
        self.assertIsNotNone(notification.delivered_at)


class DocumentProcessingServiceTests(TestCase):
    """Test Document processing service functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        self.document_processor = DocumentProcessor()
        
    def test_document_metadata_extraction(self):
        """Test document metadata extraction."""
        document = Document.objects.create(
            name="Test Document",
            file_path="/test/path/document.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        
        # Test metadata extraction
        metadata = self.document_processor.extract_metadata(document)
        self.assertIsInstance(metadata, dict)
        self.assertIn('file_type', metadata)
        self.assertIn('file_size', metadata)
        
    def test_document_indexing(self):
        """Test document search indexing."""
        document = Document.objects.create(
            name="Searchable Document",
            file_path="/test/path/searchable.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user
        )
        
        # Test indexing
        result = self.document_processor.index_document(document)
        self.assertTrue(result)
        
    def test_document_version_management(self):
        """Test document version management."""
        document = Document.objects.create(
            name="Versioned Document",
            file_path="/test/path/versioned.pdf",
            file_type="pdf",
            mime_type="application/pdf",
            organization=self.org,
            uploaded_by=self.user,
            current_version=1
        )
        
        # Test version creation
        new_version = self.document_processor.create_version(
            document=document,
            file_path="/test/path/versioned_v2.pdf",
            uploaded_by=self.user,
            change_summary="Updated content"
        )
        
        self.assertEqual(new_version.version_number, 2)
        self.assertEqual(new_version.document, document)
        
        # Refresh document and check version
        document.refresh_from_db()
        self.assertEqual(document.current_version, 2)


class ServiceIntegrationTests(TestCase):
    """Test integration between different services."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.org = Organization.objects.create(name="Integration Test Org")
        self.user = User.objects.create_user(
            username='integrationuser',
            email='<EMAIL>',
            password='testpass123',
            organization=self.org
        )
        
    def test_analytics_notification_integration(self):
        """Test integration between analytics and notification services."""
        analytics_engine = AnalyticsEngine(organization=self.org)
        notification_service = NotificationService()
        
        # Create test project
        project = Project.objects.create(
            id="integration-project",
            name="Integration Project",
            client="Integration Client",
            organization=self.org
        )
        
        # Create tasks to trigger analytics
        for i in range(5):
            Task.objects.create(
                project=project,
                title=f"Task {i}",
                status="completed" if i < 3 else "pending",
                created_by=self.user
            )
        
        # Get analytics
        metrics = analytics_engine.get_project_metrics(project.id)
        
        # Create notification based on analytics
        if metrics.get('completion_rate', 0) > 0.5:
            notification = notification_service.create_notification(
                user=self.user,
                title="Project Progress",
                message=f"Project completion rate: {metrics.get('completion_rate', 0):.1%}",
                notification_type="success"
            )
            self.assertIsNotNone(notification)
            
    def test_document_analytics_integration(self):
        """Test integration between document processing and analytics."""
        document_processor = DocumentProcessor()
        analytics_engine = AnalyticsEngine(organization=self.org)
        
        # Create test documents
        for i in range(3):
            document = Document.objects.create(
                name=f"Test Document {i}",
                file_path=f"/test/path/doc{i}.pdf",
                file_type="pdf",
                mime_type="application/pdf",
                organization=self.org,
                uploaded_by=self.user
            )
            
            # Process document
            document_processor.index_document(document)
        
        # Get document analytics
        metrics = analytics_engine.get_document_metrics()
        self.assertIsInstance(metrics, dict)
        self.assertGreaterEqual(metrics.get('total_documents', 0), 3)
