"""
Test cases for CLEAR application views.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, timedelta
import json

from CLEAR.models import (
    User, Project, Utility, Conflict, Task, Stakeholder,
    Notification, ChatMessage, Organization
)


class BaseViewTest(TestCase):
    """Base test class with common setup"""
    
    def setUp(self):
        self.client = Client()
        
        # Create test users
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        self.admin_user = User.objects.create_user(
            username='adminuser',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )

        # Create test organization
        self.organization = Organization.objects.create(
            name='Test Organization',
            description='Test organization for unit tests'
        )

        # Create test project
        self.project = Project.objects.create(
            id='TEST-2024-001',
            name='Test Infrastructure Project',
            client='Test City',
            description='Test project for unit tests',
            organization=self.organization,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=365),
            egis_project_manager=self.user.username,
            egis_project_manager_email=self.user.email
        )


class AuthenticationViewTest(BaseViewTest):
    """Test authentication views"""
    
    def test_login_view_get(self):
        """Test login view renders correctly"""
        response = self.client.get(reverse('CLEAR:login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'login')
    
    def test_login_view_post_valid(self):
        """Test successful login"""
        response = self.client.post(reverse('CLEAR:login'), {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        # Should redirect after successful login
        self.assertEqual(response.status_code, 302)
    
    def test_login_view_post_invalid(self):
        """Test failed login"""
        response = self.client.post(reverse('CLEAR:login'), {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invalid username/email or password')
    
    def test_logout_view(self):
        """Test logout"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:logout'))
        
        self.assertEqual(response.status_code, 302)


class DashboardViewTest(BaseViewTest):
    """Test dashboard views"""
    
    def test_dashboard_requires_login(self):
        """Test dashboard requires authentication"""
        response = self.client.get(reverse('CLEAR:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_dashboard_authenticated_user(self):
        """Test dashboard for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard')
        self.assertContains(response, self.user.first_name)
    
    def test_dashboard_stats(self):
        """Test dashboard statistics"""
        # Create some test data
        utility = Utility.objects.create(
            project=self.project,
            name='Test Utility',
            type='Electric'
        )
        
        conflict = Conflict.objects.create(
            project=self.project,
            utility=utility,
            description='Test conflict',
            priority='High'
        )
        
        task = Task.objects.create(
            id='TASK-001',
            project=self.project,
            title='Test Task',
            assigned_to=self.user
        )
        
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:dashboard'))
        
        self.assertEqual(response.status_code, 200)
        
        # Check that stats are in context
        context = response.context
        self.assertIn('stats', context)
        self.assertGreater(context['stats']['total_projects'], 0)


class ProjectViewTest(BaseViewTest):
    """Test project-related views"""
    
    def test_project_list_view(self):
        """Test project list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.project.name)
    
    def test_project_detail_view(self):
        """Test project detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:detail', kwargs={'pk': self.project.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.project.name)
        self.assertContains(response, self.project.client)
    
    def test_project_create_view_get(self):
        """Test project creation form"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create')
    
    def test_project_create_view_post(self):
        """Test project creation"""
        self.client.login(username='testuser', password='testpass123')
        
        project_data = {
            'name': 'New Test Project',
            'client': 'New Test Client',
            'description': 'A new test project',
            'start_date': date.today(),
            'end_date': date.today() + timedelta(days=180)
        }
        
        response = self.client.post(reverse('CLEAR:create'), project_data)
        
        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)
        
        # Verify project was created
        self.assertTrue(
            Project.objects.filter(name='New Test Project').exists()
        )


class UtilityViewTest(BaseViewTest):
    """Test utility-related views"""
    
    def setUp(self):
        super().setUp()
        self.utility = Utility.objects.create(
            project=self.project,
            name='Test Electric Company',
            type='Electric',
            contact_name='John Doe',
            contact_email='<EMAIL>'
        )
    
    def test_utility_list_view(self):
        """Test utility list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:utility_list', kwargs={'project_id': self.project.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.utility.name)
    
    def test_utility_detail_view(self):
        """Test utility detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:utility_detail', kwargs={
                'project_id': self.project.pk,
                'pk': self.utility.pk
            })
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.utility.name)
        self.assertContains(response, self.utility.contact_name)
    
    def test_utility_create_view(self):
        """Test utility creation"""
        self.client.login(username='testuser', password='testpass123')
        
        utility_data = {
            'name': 'Gas Company',
            'type': 'Gas',
            'contact_name': 'Jane Smith',
            'contact_email': '<EMAIL>',
            'contact_phone': '555-0123'
        }
        
        response = self.client.post(
            reverse('CLEAR:utility_create', kwargs={'project_id': self.project.pk}),
            utility_data
        )
        
        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)
        
        # Verify utility was created
        self.assertTrue(
            Utility.objects.filter(name='Gas Company').exists()
        )


class ConflictViewTest(BaseViewTest):
    """Test conflict-related views"""
    
    def setUp(self):
        super().setUp()
        self.utility1 = Utility.objects.create(
            project=self.project,
            name='Electric Company',
            type='Electric'
        )
        
        self.utility2 = Utility.objects.create(
            project=self.project,
            name='Gas Company',
            type='Gas'
        )
        
        self.conflict = Conflict.objects.create(
            project=self.project,
            utility=self.utility1,
            utility2=self.utility2,
            description='Utilities cross at intersection',
            priority='High',
            location='Main St & 1st Ave'
        )
    
    def test_conflict_list_view(self):
        """Test conflict list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:conflict_list', kwargs={'project_id': self.project.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.conflict.description)
    
    def test_conflict_detail_view(self):
        """Test conflict detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:conflict_detail', kwargs={
                'project_id': self.project.pk,
                'pk': self.conflict.pk
            })
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.conflict.description)
        self.assertContains(response, self.conflict.priority)


class TaskViewTest(BaseViewTest):
    """Test task-related views"""
    
    def setUp(self):
        super().setUp()
        self.task = Task.objects.create(
            id='TASK-001',
            project=self.project,
            title='Review utility plans',
            description='Review all utility coordination plans',
            priority='High',
            due_date=date.today() + timedelta(days=7),
            assigned_to=self.user
        )
    
    def test_task_list_view(self):
        """Test task list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:task_list', kwargs={'project_id': self.project.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.task.title)
    
    def test_task_detail_view(self):
        """Test task detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:task_detail', kwargs={
                'project_id': self.project.pk,
                'pk': self.task.pk
            })
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.task.title)
        self.assertContains(response, self.task.description)


class HTMXViewTest(BaseViewTest):
    """Test HTMX endpoint views"""
    
    def test_whispers_count(self):
        """Test whispers count HTMX endpoint"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test with HTMX header
        response = self.client.get(
            reverse('CLEAR:whispers_count'),
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
    
    def test_notifications_dropdown(self):
        """Test notifications dropdown HTMX endpoint"""
        # Create a test notification
        notification = Notification.objects.create(
            user=self.user,
            type='info',
            title='Test Notification',
            message='This is a test notification'
        )
        
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:notifications_dropdown'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, notification.title)
    
    def test_dashboard_stats_htmx(self):
        """Test dashboard stats HTMX endpoint"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:dashboard_stats'))
        
        self.assertEqual(response.status_code, 200)
    
    def test_create_task_htmx(self):
        """Test task creation via HTMX"""
        self.client.login(username='testuser', password='testpass123')
        
        task_data = {
            'title': 'HTMX Test Task',
            'description': 'Task created via HTMX',
            'project_id': self.project.pk
        }
        
        response = self.client.post(
            reverse('CLEAR:htmx_create_task'),
            task_data
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify task was created
        self.assertTrue(
            Task.objects.filter(title='HTMX Test Task').exists()
        )


class AdminViewTest(BaseViewTest):
    """Test admin-specific views"""
    
    def test_admin_panel_requires_admin(self):
        """Test admin panel requires admin privileges"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:admin_panel'))
        
        # Should deny access for non-admin user
        self.assertEqual(response.status_code, 403)
    
    def test_admin_panel_admin_access(self):
        """Test admin panel allows admin access"""
        self.client.login(username='adminuser', password='adminpass123')
        response = self.client.get(reverse('CLEAR:admin_panel'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Admin')
    
    def test_user_management_view(self):
        """Test user management view"""
        self.client.login(username='adminuser', password='adminpass123')
        response = self.client.get(reverse('CLEAR:user_management'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.username)


class ProfileViewTest(BaseViewTest):
    """Test user profile views"""
    
    def test_profile_view(self):
        """Test user profile view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:profile'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.first_name)
        self.assertContains(response, self.user.email)
    
    def test_settings_view(self):
        """Test user settings view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:user_settings'))
        
        self.assertEqual(response.status_code, 200)


class StakeholderViewTest(BaseViewTest):
    """Test stakeholder views"""
    
    def setUp(self):
        super().setUp()
        self.stakeholder = Stakeholder.objects.create(
            full_name='John Smith',
            contact_company='ACME Utilities',
            type_delivery='Email',
            email='<EMAIL>'
        )
    
    def test_stakeholder_list_view(self):
        """Test stakeholder list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:stakeholders'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.stakeholder.full_name)
    
    def test_stakeholder_detail_view(self):
        """Test stakeholder detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:stakeholder_detail', kwargs={'pk': self.stakeholder.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.stakeholder.full_name)
        self.assertContains(response, self.stakeholder.contact_company)
    
    def test_stakeholder_search_htmx(self):
        """Test stakeholder search HTMX endpoint"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:htmx_stakeholder_search'),
            {'q': 'John'}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.stakeholder.full_name)


class NotesViewTest(BaseViewTest):
    """Test notes and notebook views"""
    
    def setUp(self):
        super().setUp()
        self.note = Note.objects.create(
            title='Test Note',
            content='This is a test note content',
            author=self.user,
            project=self.project
        )
    
    def test_notebook_view(self):
        """Test notebook view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:notebook'))
        
        self.assertEqual(response.status_code, 200)
    
    def test_notes_list_view(self):
        """Test notes list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:notes'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.note.title)
    
    def test_note_detail_view(self):
        """Test note detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('CLEAR:note_detail', kwargs={'pk': self.note.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.note.title)
        self.assertContains(response, self.note.content)
    
    def test_note_create_view(self):
        """Test note creation"""
        self.client.login(username='testuser', password='testpass123')
        
        note_data = {
            'title': 'New Test Note',
            'content': 'Content of the new test note',
            'project': self.project.pk,
            'is_private': True
        }
        
        response = self.client.post(reverse('CLEAR:note_create'), note_data)
        
        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)
        
        # Verify note was created
        self.assertTrue(
            Note.objects.filter(title='New Test Note').exists()
        )


class TimesheetViewTest(BaseViewTest):
    """Test timesheet views"""
    
    def test_timesheet_view(self):
        """Test timesheet view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:timesheet'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Timesheet')


class MessagesViewTest(BaseViewTest):
    """Test messaging views"""
    
    def setUp(self):
        super().setUp()
        self.chat_message = ChatMessage.objects.create(
            user=self.user,
            content='Hello, this is a test message',
            channel='general'
        )
    
    def test_messages_view(self):
        """Test messages view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('CLEAR:messages'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.chat_message.content)