#!/usr/bin/env python3
"""
Fix syntax errors specifically in the CLEAR Django application files.
Only touches project files, not system libraries.
"""

import ast
import logging
import os
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PROJECT_ROOT = '/workspaces/clear_htmx'
CLEAR_DIRS = ['CLEAR', 'clear_htmx', 'templates', 'static', 'tests']

def is_project_file(filepath):
    """Check if file is part of the CLEAR project."""
    # Must be in project root
    if not filepath.startswith(PROJECT_ROOT):
        return False
    
    # Must not be in system directories
    if '/.conda/' in filepath or '/venv/' in filepath or '/.git/' in filepath:
        return False
    
    # Check if it's in one of our directories
    rel_path = filepath[len(PROJECT_ROOT):].lstrip('/')
    for dir_name in CLEAR_DIRS:
        if rel_path.startswith(dir_name + '/'):
            return True
    
    # Or if it's a file in the root
    if '/' not in rel_path and filepath.endswith('.py'):
        return True
    
    return False

def fix_auth_py():
    """Specifically fix the auth.py file."""
    auth_file = os.path.join(PROJECT_ROOT, 'CLEAR/models/auth.py')
    
    try:
        with open(auth_file, 'r') as f:
            lines = f.readlines()
        
        # Find the last meaningful line
        last_real_line = -1
        for i in range(len(lines) - 1, -1, -1):
            if lines[i].strip() and not lines[i].strip() == '"""':
                last_real_line = i
                break
        
        # Keep everything up to the last real line
        if last_real_line >= 0:
            lines = lines[:last_real_line + 1]
            
            # Make sure the file ends properly
            if lines and not lines[-1].endswith('\n'):
                lines[-1] += '\n'
            
            with open(auth_file, 'w') as f:
                f.writelines(lines)
            
            logger.info("Fixed auth.py by removing trailing docstring")
            return True
    
    except Exception as e:
        logger.error(f"Error fixing auth.py: {e}")
    
    return False

def fix_file_imports(filepath):
    """Fix import issues in a file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original = content
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Fix double imports
            if 'from from' in line:
                line = line.replace('from from', 'from')
            
            # Fix 'from .' with actual content after
            if re.match(r'^from \.\s*$', line.strip()):
                # This is an incomplete import, skip it
                continue
            
            # Fix lines that just say 'from' or 'import'
            if line.strip() in ['from', 'import', 'from .', 'from ..']:
                continue
            
            # Fix imports like "from .imports_base import *from"
            if 'import *from' in line:
                line = line.replace('import *from', 'import *\nfrom')
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        if content != original:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error fixing imports in {filepath}: {e}")
        return False

def check_and_fix_syntax(filepath):
    """Check a file for syntax errors and attempt to fix them."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse
        try:
            ast.parse(content)
            return True  # No syntax errors
        except SyntaxError as e:
            logger.info(f"Syntax error in {filepath}: {e}")
            
            # Try to fix based on error type
            if "unterminated triple-quoted string" in str(e):
                # Count triple quotes
                triple_double = content.count('"""')
                triple_single = content.count("'''")
                
                if triple_double % 2 == 1:
                    content += '\n"""'
                    logger.info(f"Added closing triple quotes to {filepath}")
                elif triple_single % 2 == 1:
                    content += "\n'''"
                    logger.info(f"Added closing triple quotes to {filepath}")
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                return True
            
            return False
            
    except Exception as e:
        logger.error(f"Error checking {filepath}: {e}")
        return False

def main():
    """Main function to fix syntax errors in CLEAR project."""
    logger.info("Starting CLEAR syntax error fixes...")
    
    # First fix auth.py specifically
    fix_auth_py()
    
    fixed_count = 0
    error_count = 0
    
    # Fix import issues
    logger.info("\nFixing import issues...")
    for root, dirs, files in os.walk(PROJECT_ROOT):
        # Skip non-project directories
        if not is_project_file(root + '/'):
            continue
            
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                
                if not is_project_file(filepath):
                    continue
                
                if fix_file_imports(filepath):
                    fixed_count += 1
    
    # Check for remaining syntax errors
    logger.info("\nChecking for remaining syntax errors...")
    files_with_errors = []
    
    for root, dirs, files in os.walk(PROJECT_ROOT):
        if not is_project_file(root + '/'):
            continue
            
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                
                if not is_project_file(filepath):
                    continue
                
                if not check_and_fix_syntax(filepath):
                    files_with_errors.append(filepath)
                    error_count += 1
    
    logger.info(f"\nFixed {fixed_count} files")
    logger.info(f"Files with remaining errors: {error_count}")
    
    if files_with_errors:
        logger.error("\nFiles still with syntax errors:")
        for f in files_with_errors[:10]:
            logger.error(f"  {f}")
        if len(files_with_errors) > 10:
            logger.error(f"  ... and {len(files_with_errors) - 10} more")
    
    return error_count == 0

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("\nAll CLEAR project syntax errors fixed!")
    else:
        logger.warning("\nSome syntax errors remain. Manual fixes needed.")