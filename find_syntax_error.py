#!/usr/bin/env python3
"""Find the actual syntax error in projects.py"""

import ast


def find_error():
    with open('CLEAR/models/projects.py') as f:
        lines = f.readlines()

    # Try parsing progressively
    for i in range(len(lines)):
        try:
            code = ''.join(lines[:i+1])
            ast.parse(code)
        except SyntaxError as e:
            if i > 0:
                print(f"Syntax error appears after line {i}")
                print(f"Error: {e}")
                print(f"\nLines {max(0, i-5)} to {i+1}:")
                for j in range(max(0, i-5), min(i+2, len(lines))):
                    print(f"{j+1}: {lines[j].rstrip()}")
                return

    print("No syntax error found!")

if __name__ == "__main__":
    find_error()
